
"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Users, ClipboardList, Lightbulb, BarChart3, Settings, UserCog, Database } from 'lucide-react'; // Changed UsersCog to UserCog, Added Database
import { cn } from '@/lib/utils';
import {
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from '@/components/ui/sidebar';

const navItems = [
  { href: '/', label: 'لوحة التحكم', icon: Home },
  { href: '/children', label: 'الأطفال', icon: Users },
  { href: '/users', label: 'إدارة المستخدمين', icon: UserCog },
  { href: '/data-management', label: 'إدارة البيانات', icon: Database }, // Added Data Management link
  { href: '/settings', label: 'الإعدادات', icon: Settings },
  // More items can be added later, e.g., for assessments overview, reports
  // { href: '/assessments', label: 'التقييمات', icon: ClipboardList },
  // { href: '/learning-plans', label: 'خطط التعلم', icon: Lightbulb },
  // { href: '/reports', label: 'التقارير', icon: BarChart3 },
];

export default function AppSidebar() {
  const pathname = usePathname();

  return (
    <nav className="flex flex-col h-full">
      <SidebarMenu className="flex-1">
        {navItems.map((item) => (
          <SidebarMenuItem key={item.href}>
            <Link href={item.href} passHref legacyBehavior>
              <SidebarMenuButton
                className={cn(
                  'w-full justify-start',
                  pathname === item.href || (item.href !== "/" && pathname.startsWith(item.href))
                    ? 'bg-sidebar-accent text-sidebar-accent-foreground font-semibold'
                    : 'hover:bg-sidebar-accent/80'
                )}
                isActive={pathname === item.href || (item.href !== "/" && pathname.startsWith(item.href))}
                tooltip={{children: item.label, side: "left", align:"center"}}
              >
                <item.icon className="h-5 w-5" />
                <span className="group-data-[collapsible=icon]:hidden">{item.label}</span>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
      {/* Optional: Add settings or user profile link at the bottom */}
      {/* <SidebarMenu className="mt-auto">
        <SidebarMenuItem>
           <Link href="/settings" passHref legacyBehavior>
              <SidebarMenuButton
                className={cn(
                  'w-full justify-start',
                  pathname === "/settings"
                    ? 'bg-sidebar-accent text-sidebar-accent-foreground font-semibold'
                    : 'hover:bg-sidebar-accent/80'
                )}
                isActive={pathname === "/settings"}
                tooltip={{children: "الإعدادات", side: "left", align:"center"}}
              >
                <Settings className="h-5 w-5" />
                 <span className="group-data-[collapsible=icon]:hidden">الإعدادات</span>
              </SidebarMenuButton>
            </Link>
        </SidebarMenuItem>
      </SidebarMenu> */}
    </nav>
  );
}

    