'use client';

import { useEffect, useState } from 'react';
import type { Child, Assessment } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, FileText, PlusCircle } from 'lucide-react'; // Changed ArrowLeft to ArrowRight for RTL
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { formatDate } from '@/lib/utils';
import AgeDisplay from '@/components/assessment/AgeDisplay';
import { useChildren, useAssessments } from '@/hooks/use-storage';

export default function AssessmentHistoryPage({ params }: { params: { childId: string } }) {
  const { getChild } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments(params.childId);

  const [child, setChild] = useState<Child | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const childData = getChild(params.childId);
    setChild(childData || null);
    setLoading(false);
  }, [params.childId, getChild]);

  const sortedAssessments = assessments.sort((a,b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime());

  if (loading || assessmentsLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات التقييمات...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Link href={`/children/${child.id}`} className="inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4">
        العودة إلى ملف {child.name}
        <ArrowRight className="h-4 w-4" /> {/* Icon now on the right */}
      </Link>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-primary">سجل التقييمات لـ {child.name}</h1>
        <Link href={`/children/${child.id}/assessment/new`}>
          <Button>
            <PlusCircle className="ml-2 h-4 w-4" /> بدء تقييم جديد
          </Button>
        </Link>
      </div>

      {sortedAssessments.length > 0 ? (
        <div className="space-y-6">
          {sortedAssessments.map(assessment => (
            <Card key={assessment.id} className="shadow-md">
              <CardHeader>
                <CardTitle>تاريخ التقييم: {formatDate(assessment.assessmentDate)}</CardTitle>
                <CardDescription>
                  <AgeDisplay birthDate={child.birthDate} assessmentDate={assessment.assessmentDate} label="عمر الطفل عند التقييم:" />
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">المهارات المقيمة: {assessment.assessedSkills.length}</p>
                {/* Here you might show a summary or key findings */}
                <div className="mt-4">
                  <Link href={`/children/${child.id}/assessment/${assessment.id}`}>
                    <Button variant="outline">عرض التفاصيل</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="text-center py-12">
          <CardHeader>
            <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
            <CardTitle className="mt-2 text-xl font-semibold">لم يتم العثور على تقييمات</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mt-1 text-sm text-muted-foreground">
              لم يتم تسجيل أي تقييمات لـ {child.name} بعد.
            </p>
            <Link href={`/children/${child.id}/assessment/new`} className="mt-4 inline-block">
              <Button>بدء التقييم الأول</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
