import { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA } from '@/lib/constants';
import type { Child, Assessment } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight, FileText, PlusCircle } from 'lucide-react'; // Changed ArrowLeft to ArrowRight for RTL
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { formatDate } from '@/lib/utils';
import AgeDisplay from '@/components/assessment/AgeDisplay';

async function getChildById(id: string): Promise<Child | undefined> {
  return MOCK_CHILDREN_DATA.find(child => child.id === id);
}

async function getAssessmentsByChildId(childId: string): Promise<Assessment[]> {
  return MOCK_ASSESSMENTS_DATA.filter(assessment => assessment.childId === childId)
    .sort((a,b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime());
}

export default async function AssessmentHistoryPage({ params }: { params: { childId: string } }) {
  const child = await getChildById(params.childId);
  const assessments = await getAssessmentsByChildId(params.childId);

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <Link href={`/children/${child.id}`} className="inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4">
        العودة إلى ملف {child.name}
        <ArrowRight className="h-4 w-4" /> {/* Icon now on the right */}
      </Link>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-primary">سجل التقييمات لـ {child.name}</h1>
        <Link href={`/children/${child.id}/assessment/new`}>
          <Button>
            <PlusCircle className="ml-2 h-4 w-4" /> بدء تقييم جديد
          </Button>
        </Link>
      </div>

      {assessments.length > 0 ? (
        <div className="space-y-6">
          {assessments.map(assessment => (
            <Card key={assessment.id} className="shadow-md">
              <CardHeader>
                <CardTitle>تاريخ التقييم: {formatDate(assessment.assessmentDate)}</CardTitle>
                <CardDescription>
                  <AgeDisplay birthDate={child.birthDate} assessmentDate={assessment.assessmentDate} label="عمر الطفل عند التقييم:" />
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">المهارات المقيمة: {assessment.assessedSkills.length}</p>
                {/* Here you might show a summary or key findings */}
                <div className="mt-4">
                  <Link href={`/children/${child.id}/assessment/${assessment.id}`}>
                    <Button variant="outline">عرض التفاصيل</Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="text-center py-12">
          <CardHeader>
            <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
            <CardTitle className="mt-2 text-xl font-semibold">لم يتم العثور على تقييمات</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mt-1 text-sm text-muted-foreground">
              لم يتم تسجيل أي تقييمات لـ {child.name} بعد.
            </p>
            <Link href={`/children/${child.id}/assessment/new`} className="mt-4 inline-block">
              <Button>بدء التقييم الأول</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
