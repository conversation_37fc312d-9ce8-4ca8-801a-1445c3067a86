<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use <PERSON><PERSON>\Jetstream\Events\TeamCreated;
use <PERSON><PERSON>\Jetstream\Events\TeamDeleted;
use <PERSON><PERSON>\Jetstream\Events\TeamUpdated;
use <PERSON><PERSON>\Jetstream\Team as JetstreamTeam;

class Team extends JetstreamTeam
{
    /** @use HasFactory<\Database\Factories\TeamFactory> */
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'personal_team',
    ];

    /**
     * The event map for the model.
     *
     * @var array<string, class-string>
     */
    protected $dispatchesEvents = [
        'created' => TeamCreated::class,
        'updated' => TeamUpdated::class,
        'deleted' => TeamDeleted::class,
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'personal_team' => 'boolean',
        ];
    }
}
