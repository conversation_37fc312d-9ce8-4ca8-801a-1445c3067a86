{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/hooks/use-mobile.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { differenceInYears, differenceInMonths, differenceInDays, addYears, addMonths, parseISO, isValid, format } from 'date-fns';\nimport { arSA } from 'date-fns/locale'; // Import Arabic locale\nimport type { CalculatedAge } from './types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function calculateAge(birthDateString: string, assessmentDateString?: string): CalculatedAge {\n  const birthDate = parseISO(birthDateString);\n  const assessmentDate = assessmentDateString ? parseISO(assessmentDateString) : new Date();\n\n  if (!isValid(birthDate) || !isValid(assessmentDate)) {\n    // console.error(\"Invalid date provided for age calculation\", { birthDateString, assessmentDateString });\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  if (birthDate > assessmentDate) {\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  let tempAgeDate = new Date(birthDate);\n\n  const years = differenceInYears(assessmentDate, tempAgeDate);\n  tempAgeDate = addYears(tempAgeDate, years);\n\n  const months = differenceInMonths(assessmentDate, tempAgeDate);\n  tempAgeDate = addMonths(tempAgeDate, months);\n\n  const days = differenceInDays(assessmentDate, tempAgeDate);\n\n  return { years, months, days };\n}\n\nexport function formatDate(dateString: string | Date, dateFormat: string = 'PPP'): string {\n  try {\n    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;\n    if (!isValid(date)) return \"تاريخ غير صالح\";\n    return format(date, dateFormat, { locale: arSA }); // Use Arabic locale\n  } catch (error) {\n    return \"تاريخ غير صالح\";\n  }\n}\n\n// Generate a unique child ID number\nexport function generateChildIdNumber(): string {\n  const currentYear = new Date().getFullYear();\n  const randomNumber = Math.floor(Math.random() * 999) + 1;\n  return `CH-${currentYear}-${randomNumber.toString().padStart(3, '0')}`;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,mQAAwC,uBAAuB;;;;;AAGxD,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,eAAuB,EAAE,oBAA6B;IACjF,MAAM,YAAY,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE;IAC3B,MAAM,iBAAiB,uBAAuB,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,wBAAwB,IAAI;IAEnF,IAAI,CAAC,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,cAAc,CAAC,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QACnD,yGAAyG;QACzG,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,YAAY,gBAAgB;QAC9B,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,cAAc,IAAI,KAAK;IAE3B,MAAM,QAAQ,CAAA,GAAA,oJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;IAChD,cAAc,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAEpC,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB;IAClD,cAAc,CAAA,GAAA,4IAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAErC,MAAM,OAAO,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;IAE9C,OAAO;QAAE;QAAO;QAAQ;IAAK;AAC/B;AAEO,SAAS,WAAW,UAAyB,EAAE,aAAqB,KAAK;IAC9E,IAAI;QACF,MAAM,OAAO,OAAO,eAAe,WAAW,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QACrE,IAAI,CAAC,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAC3B,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,YAAY;YAAE,QAAQ,qJAAA,CAAA,OAAI;QAAC,IAAI,oBAAoB;IACzE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;IACvD,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,EAAE,aAAa,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACxE", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,QAAQ,qKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,qKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,qKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,qKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,qKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,qKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,qKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/sidebar.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeft, PanelRight } from \"lucide-react\" // Added PanelRight\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from \"@/components/ui/sheet\" // Added SheetHeader, SheetTitle\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContext = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n  currentSide: \"left\" | \"right\"; // Added to track current side for RTL\n}\n\nconst SidebarContext = React.createContext<SidebarContext | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n    const [currentSide, setCurrentSide] = React.useState<\"left\" | \"right\">(\"right\"); // Default to right for RTL\n\n    React.useEffect(() => {\n        const dir = document.documentElement.dir;\n        setCurrentSide(dir === 'rtl' ? 'right' : 'left');\n    }, []);\n\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContext>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n        currentSide,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar, currentSide]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\" // Prop still exists for explicit override if needed\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side: propSide, // Use propSide to distinguish from context's currentSide\n      variant = \"sidebar\",\n      collapsible = \"offcanvas\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile, currentSide: contextSide } = useSidebar()\n    const effectiveSide = propSide || contextSide; // Prioritize prop, then context (for RTL)\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"flex flex-col w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={effectiveSide}\n          >\n            <SheetHeader className=\"p-4 border-b border-sidebar-border\">\n              <SheetTitle className=\"sr-only\">القائمة الرئيسية</SheetTitle>\n            </SheetHeader>\n            <div className=\"flex-1 overflow-y-auto\">\n              {children}\n            </div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden md:block text-sidebar-foreground\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={effectiveSide}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n             // For RTL, rotation is handled by overall dir=\"rtl\" on html\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex\",\n            effectiveSide === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\",\n            effectiveSide === \"left\" && (variant !== \"floating\" && variant !== \"inset\") ? \"group-data-[collapsible=icon]:border-r\" : \"\",\n            effectiveSide === \"right\" && (variant !== \"floating\" && variant !== \"inset\") ? \"group-data-[collapsible=icon]:border-l\" : \"\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar, currentSide } = useSidebar()\n  const Icon = currentSide === 'right' ? PanelRight : PanelLeft;\n\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <Icon />\n      <span className=\"sr-only\">تبديل الشريط الجانبي</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar, currentSide } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"تبديل الشريط الجانبي\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"تبديل الشريط الجانبي\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border sm:flex\",\n        currentSide === 'left' ? \"group-data-[side=left]:-right-4\" : \"group-data-[side=right]:-left-4 group-data-[side=left]:-right-auto\",\n        currentSide === 'left' ? \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\" : \"[[data-side=left]_&]:cursor-e-resize [[data-side=right]_&]:cursor-w-resize\",\n        currentSide === 'left' ? \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\" : \"[[data-side=left][data-state=collapsed]_&]:cursor-w-resize [[data-side=right][data-state=collapsed]_&]:cursor-e-resize\",\n\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        currentSide === 'left' ? \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\" : \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex min-h-svh flex-1 flex-col bg-background\",\n        \"peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:mr-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        \"md:peer-data-[side=right]:peer-data-[state=expanded]:peer-data-[variant!=inset]:mr-[var(--sidebar-width)]\",\n        \"md:peer-data-[side=left]:peer-data-[state=expanded]:peer-data-[variant!=inset]:ml-[var(--sidebar-width)]\",\n        \"md:peer-data-[side=right]:peer-data-[state=collapsed]:peer-data-[collapsible=icon]:peer-data-[variant!=inset]:mr-[var(--sidebar-width-icon)]\",\n        \"md:peer-data-[side=left]:peer-data-[state=collapsed]:peer-data-[collapsible=icon]:peer-data-[variant!=inset]:ml-[var(--sidebar-width-icon)]\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n  const { currentSide } = useSidebar();\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        currentSide === 'left' ? \"right-3\" : \"left-3\", // Adjust based on RTL\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state, currentSide } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side={currentSide === 'right' ? 'left' : 'right'} // Adjust tooltip side for RTL\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n  const { currentSide } = useSidebar();\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        currentSide === 'left' ? \"right-1\" : \"left-1\", // Adjust based on RTL\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n    const { currentSide } = useSidebar();\n    return (\n        <div\n        ref={ref}\n        data-sidebar=\"menu-badge\"\n        className={cn(\n            \"absolute flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none\",\n            currentSide === 'left' ? \"right-1\" : \"left-1\", // Adjust based on RTL\n            \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n            \"peer-data-[size=sm]/menu-button:top-1\",\n            \"peer-data-[size=default]/menu-button:top-1.5\",\n            \"peer-data-[size=lg]/menu-button:top-2.5\",\n            \"group-data-[collapsible=icon]:hidden\",\n            className\n        )}\n        {...props}\n        />\n    )\n})\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"rounded-md h-8 flex gap-2 px-2 items-center\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 flex-1 max-w-[--skeleton-width]\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => {\n    const { currentSide } = useSidebar();\n    return (\n        <ul\n        ref={ref}\n        data-sidebar=\"menu-sub\"\n        className={cn(\n            \"flex min-w-0 translate-x-px flex-col gap-1 px-2.5 py-0.5\",\n            currentSide === 'left' ? \"mx-3.5 border-l border-sidebar-border\" : \"mx-3.5 border-r border-sidebar-border\", // Adjust border for RTL\n            \"group-data-[collapsible=icon]:hidden\",\n            className\n        )}\n        {...props}\n        />\n    )\n})\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;AACA;AACA,+WAAqD,mBAAmB;AAAxE;AAEA;AACA;AACA;AACA;AACA;AACA,uOAAqF,gCAAgC;AACrH;AACA;;;AAdA;;;;;;;;;;;;;AAqBA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAalC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAyB;AAElE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,MAAM,gCAAkB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WAQrC,CACE,EACE,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAoB,UAAU,2BAA2B;IAE5G,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACZ,MAAM,MAAM,SAAS,eAAe,CAAC,GAAG;YACxC,eAAe,QAAQ,QAAQ,UAAU;QAC7C;oCAAG,EAAE;IAGL,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WACH;8DAAc,CAAC,OAAS,CAAC;+DACzB;8DAAQ,CAAC,OAAS,CAAC;;QACzB;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;QAAe;KAAY;IAGzF,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;;QA7FmB,iIAAA,CAAA,cAAW;;;;QAAX,iIAAA,CAAA,cAAW;;;;AA+FhC,gBAAgB,WAAW,GAAG;AAE9B,MAAM,wBAAU,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAQ7B,CACE,EACE,MAAM,QAAQ,EACd,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,WAAW,EAAE,GAAG;IACjF,MAAM,gBAAgB,YAAY,aAAa,0CAA0C;IAEzF,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAEF,KAAK;YACJ,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,oIAAA,CAAA,aAAU;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAElC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;IAKX;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;;0BAGX,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iGACA,0CACC,4DAA4D;gBAC7D,YAAY,cAAc,YAAY,UAClC,yFACA;;;;;;0BAGR,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,kBAAkB,SACd,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,kGACA,0DACJ,kBAAkB,UAAW,YAAY,cAAc,YAAY,UAAW,2CAA2C,IACzH,kBAAkB,WAAY,YAAY,cAAc,YAAY,UAAW,2CAA2C,IAC1H;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;;QAxFmF;;;;QAAA;;;;AA0FrF,QAAQ,WAAW,GAAG;AAEtB,MAAM,+BAAiB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGpC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;;IACnC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG;IACvC,MAAM,OAAO,gBAAgB,UAAU,qNAAA,CAAA,aAAU,GAAG,mNAAA,CAAA,YAAS;IAG7D,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,KAAK;QACL,gBAAa;QACb,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;QACzB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC;;;;;0BACD,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;;QArByC;;;;QAAA;;;;AAsBzC,eAAe,WAAW,GAAG;AAE7B,MAAM,4BAAc,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IAC1B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG;IAEvC,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oLACA,gBAAgB,SAAS,oCAAoC,sEAC7D,gBAAgB,SAAS,+EAA+E,8EACxG,gBAAgB,SAAS,2HAA2H,0HAEpJ,2JACA,gBAAgB,SAAS,8DAA8D,6DACvF;QAED,GAAG,KAAK;;;;;;AAGf;;QAvByC;;;;QAAA;;;;AAwBzC,YAAY,WAAW,GAAG;AAE1B,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA,gRACA,6GACA,4GACA,gJACA,+IACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;;AACA,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sOACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,kBAAkB,WAAW,GAAG;AAEhC,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAGxC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;;IAC3C,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oRACA,gBAAgB,SAAS,YAAY,UACrC,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;;QAjB0B;;;;QAAA;;;;AAkB1B,mBAAmB,WAAW,GAAG;AAEjC,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGzC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;;AAGb,oBAAoB,WAAW,GAAG;AAElC,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,qzBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,MAAM,kCAAoB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAQvC,CACE,EACE,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OACJ,EACD;;IAEA,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG;IAEzC,MAAM,uBACJ,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAM,gBAAgB,UAAU,SAAS;gBACzC,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;;QAlC2C;;;;QAAA;;;;AAoC7C,kBAAkB,WAAW,GAAG;AAEhC,MAAM,kCAAoB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAMvC,CAAC,EAAE,SAAS,EAAE,UAAU,KAAK,EAAE,cAAc,KAAK,EAAE,GAAG,OAAO,EAAE;;IAChE,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0UACA,gBAAgB,SAAS,YAAY,UACrC,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;;QAtB0B;;;;QAAA;;;;AAuB1B,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IACxB,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,qBACI,6LAAC;QACD,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,kKACA,gBAAgB,SAAS,YAAY,UACrC,4HACA,yCACA,gDACA,2CACA,wCACA;QAEH,GAAG,KAAK;;;;;;AAGjB;;QAlB4B;;;;QAAA;;;;AAmB5B,iBAAiB,WAAW,GAAG;AAE/B,MAAM,oCAAsB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,aAKzC,CAAC,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,GAAG,OAAO,EAAE;;IAC5C,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,+BAAiB,KAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,cAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;;IACxB,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,qBACI,6LAAC;QACD,KAAK;QACL,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,4DACA,gBAAgB,SAAS,0CAA0C,yCACnE,wCACA;QAEH,GAAG,KAAK;;;;;;AAGjB;;QAd4B;;;;QAAA;;;;AAe5B,eAAe,WAAW,GAAG;AAE7B,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGxC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,6LAAC;QAAG,KAAK;QAAM,GAAG,KAAK;;;;;;;AAChD,mBAAmB,WAAW,GAAG;AAEjC,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAO1C,CAAC,EAAE,UAAU,KAAK,EAAE,OAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAClE,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1348, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\"\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;;AAHA;;AAUA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAS;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;8BAAE;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS", "debugId": null}}, {"offset": {"line": 1514, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/toast.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AASA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6lBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1649, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/toaster.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,6LAAC,oIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO;gBAChE,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB;sCACD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,+HAA<PERSON>,CAAA,WAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 1734, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/constants.ts"], "sourcesContent": ["\nimport type { PortageDimension, Assessment, Child, User, UserRole, ProgressStatus, SessionNote, CaseStudyData } from './types';\nimport { format, parseISO } from 'date-fns'; // For session note dates\n\nexport const APP_NAME = \"بورتيج بلس\";\n\nexport const AGE_RANGES = [\n  \"0-9 أشهر\",\n  \"9-18 شهرًا\",\n  \"18-36 شهرًا\",\n  \"3-4 سنوات\",\n  \"4-5 سنوات\",\n  \"5-6 سنوات\",\n  \"غير محدد\",\n];\n\nexport const SKILL_STATUS_OPTIONS: { value: 'yes' | 'no' | 'unclear'; label: string; symbol: string }[] = [\n  { value: 'yes', label: 'نعم', symbol: '✔️' },\n  { value: 'no', label: 'لا', symbol: '❌' },\n  { value: 'unclear', label: 'غير واضح', symbol: '❓' },\n];\n\nexport const PROGRESS_STATUS_OPTIONS: { value: ProgressStatus; label: string; symbol: string; colorClass: string }[] = [\n  { value: 'pending', label: 'قيد الانتظار', symbol: '⏳', colorClass: 'bg-gray-500 text-white' },\n  { value: 'implemented', label: 'تم البدء بالتنفيذ', symbol: '🚀', colorClass: 'bg-blue-500 text-white' },\n  { value: 'mastered', label: 'أُتقن الهدف', symbol: '🏆', colorClass: 'bg-green-600 text-white' },\n  { value: 'postponed', label: 'تم التأجيل', symbol: '🗓️', colorClass: 'bg-orange-500 text-white' },\n];\n\n\nexport const USER_ROLE_OPTIONS: { value: UserRole; label: string }[] = [\n  { value: 'super_admin', label: 'إداري الشركة (المتحكم بالموقع)' },\n  { value: 'eiu_manager', label: 'مدير وحدة التدخل المبكر' },\n  { value: 'case_manager', label: 'مدير الحالة' },\n  { value: 'specialist', label: 'الأخصائي' },\n  { value: 'educator', label: 'المعلم' },\n  { value: 'viewer', label: 'مشاهد' },\n];\n\nexport const AGE_DISTRIBUTION_GROUPS: { label: string; minMonths: number; maxMonths: number; count?: number }[] = [\n  { label: '0-1 سنة', minMonths: 0, maxMonths: 12 },\n  { label: '1-2 سنة', minMonths: 12, maxMonths: 24 },\n  { label: '2-3 سنوات', minMonths: 24, maxMonths: 36 },\n  { label: '3-4 سنوات', minMonths: 36, maxMonths: 48 },\n  { label: '4-5 سنوات', minMonths: 48, maxMonths: 60 },\n  { label: '5-6 سنوات', minMonths: 60, maxMonths: 72 },\n];\n\n\nexport const defaultCaseStudyData: CaseStudyData = {\n  basicInfo: {\n    childName: \"\",\n    birthDate: \"\",\n    currentAge: \"\",\n    gender: \"unknown\",\n    guardianName: \"\",\n    guardianPhoneNumber: \"\",\n    homeAddress: \"\",\n    guardianRelationship: \"\",\n    hasSiblings: \"no\",\n    siblingsInfo: \"\",\n  },\n  pregnancyAndBirthInfo: {\n    motherAgeAtPregnancy: \"\",\n    fullTermPregnancy: \"yes\",\n    prematureBirthMonth: \"\",\n    motherHealthIssuesDuringPregnancy: \"no\",\n    motherHealthIssuesDetails: \"\",\n    deliveryType: \"natural\",\n    childHealthIssuesAtBirth: \"no\",\n    childHealthIssuesDetails: \"\",\n  },\n  reinforcerResponseInfo: {\n    favoriteToys: \"\",\n    enjoyableActivities: \"\",\n    favoriteFoods: \"\",\n    happinessExpression: \"\",\n    motivationMethods: \"\",\n    smilesAtGuardian: \"unknown\",\n    communicatesNeeds: \"unknown\",\n  },\n};\n\n\nexport const PORTAGE_CHECKLIST_DATA: PortageDimension[] = [\n  {\n    id: \"social\",\n    name: \"البعد الاجتماعي\",\n    subCategories: [\n      {\n        id: \"social-relationships\",\n        name: \"العلاقات\",\n        skills: [\n          { id: \"soc-rel-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"ينظر لفترة مناسبة إلى مقدم الرعاية.\", applicationMethod: \"سؤال الأسرة/ مقدم الرعاية هل يحدق النظر الى وجهك لفترة مناسبة أثناء حمله بينما يواجه وجهه، وجهك؟.\" },\n          { id: \"soc-rel-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يظهر تمييزا لمقدمي الرعاية الرئيسيين.\", applicationMethod: \"سؤال الأسرة/ مقدم الرعاية هل يبتسم أو يحرك يديه حين يأتي أحدكم باتجاهه أو يسمع صوته؟.\" },\n          { id: \"soc-rel-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يعبر عن رغبته بوقف النشاط من خلال إشارة معينة (يتجهم، يبتعد، أو يبكي).\", applicationMethod: \"الملاحظة المباشرة أو سؤال الأسرة/ مقدم الرعاية يلاحظ الميسر ردود أفعال الرضيع لطول فترة التقييم معه وما اذا كان يبكي أو يظهر عدم ارتياحه كرغبة في وقف النشاط، وان لم يلاحظ ذلك بشكل مباشر بإمكانه سؤال الأسرة/ مقدم الرعاية.\" },\n          { id: \"soc-rel-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"ينظر إلى مقدم الرعاية منتظرا موافقته أو تأكيده على أمر ما.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ الميسر ما اذا كان الرضيع ينظر اليه قبل أن يمد يده لأخذ لعبة خاصة بالمقيم، وان لم يلاحظ ذلك بشكل مباشر بإمكانه سؤال الأسرة/ مقدم الرعاية.\" },\n          { id: \"soc-rel-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يقرأ انفعالات مقدم الرعاية وتعبيراته الوجهية.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يطلب من أحد أفراد الأسرة/ أو مقدم الرعاية أن يضحك بوجه الطفل، ومن ثم يظهر غضبه بتقطيب حاجبية، فإن لم يلاحظ تغير تعبيرات الطفل الوجهية استجابة لتغير ايماءات الكبير بشكل مباشر بإمكانه سؤال الأسرة/ مقدم الرعاية.\" },\n          { id: \"soc-rel-6\", itemNumber: \"6\", ageRange: \"9-18 شهرًا\", behavior: \"يشارك الكبار بطريقة عفوية.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يطلب المقدم من أحد أفراد الأسرة/ أو مقدم الرعاية أن يطلب من الطفل اعطاءه لعبة بيده، فإن لم يستجب بإمكانه سؤال الأسرة/ مقدم الرعاية عما اذا كان \\\"يطعمه اذا قال له الكبير: \\\"\\\"طعميني\\\"\\\"\\\".\" },\n          { id: \"soc-rel-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يستكشف الفكاهة من خلال أدائه للأشياء المضحكة كي يحصل على الانتباه.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يطلب من أحد أفراد الأسرة/ أو مقدم الرعاية أن يقول له صحة، فإن لم يسعل بإمكانه سؤال الأسرة/ مقدم الرعاية عما اذا كان يسعل ان قال له: \\\"\\\"صحة\\\"\\\"، أو يكرر أي فعل يحصل من خلاله على انتباه الكبار.\" },\n          { id: \"soc-rel-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يجرب سلوكيات مختلفة لاختبار استجابات ومحددات مقدم الرعاية.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل يحاول لفت نظر والدته أو مقدم الرعاية أثناء انشغاله بالحديث للمقيم، قد يحاول فتح التلفاز أو يطلب الذهاب إلى الحمام أو يعبر عن جوعه، فإن لم يظهر ذلك يتم سؤال الأسرة/ مقدم الرعاية.\" },\n          { id: \"soc-rel-9\", itemNumber: \"9\", ageRange: \"18-36 شهرًا\", behavior: \"ينفذ بعض الأفعال أو يري الأشياء لمقدم الرعاية كي يحصل على موافقته.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل يأخذ اذنه قبل أخذ شيء من الألعاب الخاصة بالتقييم، فإن لم يكترث بها يتم سؤال الأسرة/ مقدم الرعاية عما اذا كان يستأذن أو يظهر رغبته بالحصول على شيء ما بطرق إيجابية.\" },\n          { id: \"soc-rel-10\", itemNumber: \"10\", ageRange: \"18-36 شهرًا\", behavior: \"ينفصل بسهولة عن والديه في البيئات المألوفة.\", applicationMethod: \"سؤال الأسرة/ مقدم الرعاية يسأل المقيم مقدم الرعاية عما اذا كان الطفل يقبل البقاء بعيدا عن والديه في البيئات المألوفة كبيت جده أو دار الحضانة النهارية مثلا.\" },\n          { id: \"soc-rel-11\", itemNumber: \"11\", ageRange: \"3-4 سنوات\", behavior: \"يتعاون مع طلبات الكبار غالبية الوقت.\", applicationMethod: \"الملاحظة المباشرة نطلب من الأسرة أن تطلب منه احضار شيء أو اغلاق الباب ونلاحظ استجابته.\" },\n          { id: \"soc-rel-12\", itemNumber: \"12\", ageRange: \"3-4 سنوات\", behavior: \"يظهر فخرا بما أنجزه دون مساعدة الكبار.\", applicationMethod: \"الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل يظهر فخره بما أنجز بعد أن يعززه المقيم على استجابته بوضع نجمة على جبينه، ويحاول لفت نظر مقدم الرعاية لها، وان لم يفعل يمكن سؤال مقدم الرعاية عن ذلك.\" },\n          { id: \"soc-rel-13\", itemNumber: \"13\", ageRange: \"4-5 سنوات\", behavior: \"يحاول حل مشاكله مع أقرانه دون عراك جسدي بمساعدة من الكبار.\", applicationMethod: \"سؤال الأسرة/ مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل أثناء تفاعله مع أصدقاءه واذا ما واجهته مشاكل في اللعب معهم، يحاول حل مشكلاته معهم دون أن يلجأ مباشرة إلى العنف أو الضرب (قد يطلب المساعدة في ذلك من مقدم الرعاية أو الكبار من حوله).\" },\n          { id: \"soc-rel-14\", itemNumber: \"14\", ageRange: \"5-6 سنوات\", behavior: \"يحل خلافاته مع الأطفال الآخرين دون مساعدة من الكبار.\", applicationMethod: \"سؤال الأسرة/ مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل أثناء تفاعله مع أصدقاءه واذا ما واجهته مشاكل في اللعب معهم، يلجأ إلى الحوار والتفاوض معهم (قد يطلب المساعدة في ذلك من مقدم الرعاية أو الكبار من حوله).\" }\n        ]\n      },\n      {\n        id: \"social-interaction\",\n        name: \"التفاعل مع الاخرين\",\n        skills: [\n          { id: \"soc-int-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"ينام أو يحرك وجهه جانبا بعد أن يكون قد تعرض لمثيرات عديدة.\", applicationMethod: \"سؤال الأسرة عما اذا كان ينام أو يحرك وجهه جانبا بعد أن يكون قد تعرض لمثيرات عديدة.\" },\n          { id: \"soc-int-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يعيد الأصوات والأفعال التي تعزز الاستجابات التفاعلية.\", applicationMethod: \"الملاحظة المباشرة أو سؤال الأسرة نطلب من الأم أن تقلد الأصوات التي يصدرها ونلاحظ ما اذا كان يكررها، وان لم يكررها نسأل مقدم الرعاية.\" },\n          { id: \"soc-int-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يستجيب لواحد أو اثنين من التعبيرات اللفظية الروتينية.\", applicationMethod: \"الملاحظة المباشرة أو سؤال الأسرة نطلب من الأسرة أن تقول له نام ونلاحظ ما اذا كان يميل رأسه متظاهرا بالنوم، فان لم يفعل نسأل مقدم الرعاية.\" },\n          { id: \"soc-int-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يبادر ببعض الألعاب البسيطة ليلفت انتباه الكبار: تصفيق اليدين، لعبة الإخفاء باليد (أو بيييي).\", applicationMethod: \"الملاحظة المباشرة أو سُؤال مقدم الرعاية نلاحظ سلوك الطفل أثناء التقييم وما اذا كان يبادر ببعض الألعاب البسيطة ليلفت انتباه الكبار: تصفيق اليدين، لعبة الإخفاء باليد (أو بيييي) وان لم يفعل نسأل مقدم الرعاية.\" },\n          { id: \"soc-int-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يبتهج لألعاب الفعل ورد الفعل (مثال: الاختفاء والبحث والوصول إلى شخص ما).\", applicationMethod: \"الملاحظة المباشرة: نطلب من مقدم الرعاية أن يلعب مع الطفل ألعاب الفعل ورد الفعل (مثال: الاختفاء والبحث والوصول إلى شخص ما) ونلاحظ استجابته.\" },\n          { id: \"soc-int-6\", itemNumber: \"6\", ageRange: \"9-18 شهرًا\", behavior: \"يتحكم بنفسه لمدة تتراوح ما بين 10-5 دقائق حين يتواجد بالقرب من الكبار.\", applicationMethod: \"الملاحظة المباشرة: نلاحظ سلوك الطفل العام بوجود المقيم وما اذا كان يستطيع البقاء منضبطا لفترة تتراوح ما بين 10-5 دقائق.\" },\n          { id: \"soc-int-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يأخذ دورا في الأنشطة والمحادثات مع بعض المساعدة.\", applicationMethod: \"الملاحظة المباشرة: يطرح المقيم على الطفل بعض الأسئلة البسيطة مثل: \\\"\\\"ما أسمك؟\\\"\\\" \\\"\\\"ما هي لعبتك المفضلة؟\\\"\\\" ونلاحظ استجابته، وان لم يستجب معنا نطلب من الأم سؤاله، ويؤجل السؤال لآخر الجلسة تحسبا ما اذا كانت ضعف استجابته خجلا.\" },\n          { id: \"soc-int-8\", itemNumber: \"8\", ageRange: \"3-4 سنوات\", behavior: \"يشارك الأطفال الآخرين مع بعض المساعدة من الكبار.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك مجموعة أطفال في موقف التقييم نلاحظ مستوى تفاعله الأطفال الآخرين مع بعض المساعدة من الكبار، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية.\" },\n          { id: \"soc-int-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يُشارك ضمن مجموعات صغيرة في الأنشطة التي تتضمن قوانين لمدة تتراوح ما بين 15-10 دقيقة.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك مجموعة أطفال في موقف التقييم نلاحظ مشاركته ضمن مجموعات صغيرة في الأنشطة التي تتضمن قوانين لمدة تتراوح من 15-10 دقيقة، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية.\" },\n          { id: \"soc-int-10\", itemNumber: \"10\", ageRange: \"4-5 سنوات\", behavior: \"يستمع إلى محادثات الكبار ويشارك فيها.\", applicationMethod: \"الملاحظة المباشرة: نلاحظ ما اذا كان يشارك في الحديث مع الكبار في الأمور المتعلقة به، شريطة أن تتم إدارة الحوار من المقيم بحيث يتجنب أي حديث سلبي عن الطفل أمامه.\" },\n          { id: \"soc-int-11\", itemNumber: \"11\", ageRange: \"4-5 سنوات\", behavior: \"يتحدث عن أصدقائه الجدد.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: يستفسر المقيم من الطفل عن أصدقاءه الجدد في الروضة أو منطقة اللعب، وان لم يستجب الطفل للمقيم بإمكانه الاستفسار من مقدم الرعاية عما اذا كان الطفل يحضر من الروضة ويخبره عن الأصدقاء الجدد الذين صادفهم أو ان التقاهم في حديقة عامة أو مكان عام.\" },\n          { id: \"soc-int-12\", itemNumber: \"12\", ageRange: \"4-5 سنوات\", behavior: \"يتحدث عن مشاعره ومشاعر الآخرين.\", applicationMethod: \"الملاحظة المباشرة أو سُؤال مقدم الرعاية يطلب المقيم من مقدم الرعاية أن يتظاهر بالعبوس، ويسأله (مابها والدتك/ والدك؟)، ثم يطلب من مقدم الرعاية الضحك، ويستفسر منه عما تغير في تعابير وجهه. إضافة إلى ذلك يستفسر المقيم أيضا من مقدم الرعاية عما اذا كان الطفل في العادة يأتي ويخبره عن غضبه أو فرحه معبرا عن مشاعره.\" },\n          { id: \"soc-int-13\", itemNumber: \"13\", ageRange: \"5-6 سنوات\", behavior: \"يختار أصدقاءه بنفسه.\", applicationMethod: \"سُؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يبادر في مكان عام أو في الروضة بالتعرف على الأطفال، أم أنه ينتظر القيام بذلك من الكبار أو الأقران الآخرين؟.\" },\n          { id: \"soc-int-14\", itemNumber: \"14\", ageRange: \"5-6 سنوات\", behavior: \"يتبع القواعد الموجهة للألعاب الذهنية والجسدية حين يلعب مع أقرانه.\", applicationMethod: \"سُؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يلعب الألعاب الذهنية أو الجسدية الجماعية (مثال: فتحت الوردة، طاق طاق طاقية)، سواء عند تجمع العائلة أو في الروضة.\" }\n        ]\n      },\n      {\n        id: \"social-emotional-response\",\n        name: \"الاستجابة الانفعالية\",\n        skills: [\n          { id: \"soc-er-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يستخدم التعبيرات الوجهية المختلفة لعكس حالته الانفعالية.\", applicationMethod: \"الملاحظة المباشرة أو سؤال الأسرة نلاحظ ما اذا كانت تعبيرات وجهه تعكس راحته أو عدم راحته، وان لم يظهرها نسأل مقدم الرعاية.\" },\n          { id: \"soc-er-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يعبر عن عدم راحته عند مغادرة مقدم الرعاية.\", applicationMethod: \"الملاحظة المباشرة نطلب من الأسرة مغادرة المكان وترك الطفل في الغرفة مع المقيّم مع بقاء عين مقدم الرعاية بالقرب من الباب لسرعة الاستجابة في حال بكاءه أو لطمأنته الفورية ان اظهر عدم راحة.\" },\n          { id: \"soc-er-3\", itemNumber: \"3\", ageRange: \"9-18 شهرًا\", behavior: \"يكرر محاولاته للوصول إلى النتائج المرجوة (مثال: يرمي لعبة فيلتقطها مقدم الرعاية بثبات).\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ خلال الجلسة ما اذا كان الطفل يحاول أن يلفت نظر الكبير من خلال سلوكيات كرمي الخشخيشة، وان لم يفعل أثناء وجود المقيم يمكنه سؤال مقدم الرعاية عن ذلك.\" },\n          { id: \"soc-er-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يحافظ على هدوئه لفترة قصيرة حين يكون محبطا.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ ردود فعل الطفل عند غياب مقدم الرعاية مثلا أو أخذ لعبة مفضلة منه، وان لم تظهر أي ردة فعل تعكس احباطه نسأل مقدم الرعاية.\" },\n          { id: \"soc-er-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يستكشف بثقة منزله أو البيئات المألوفة له.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ ذلك بشكل مباشر سواء كان يتنقل زحفا أو دحرجة أو بالكراجة أو بأي طريقة لاستكشاف بيئته، وان لم يُظهر ذلك نسأل مقدم الرعاية.\" },\n          { id: \"soc-er-6\", itemNumber: \"6\", ageRange: \"18-36 شهرًا\", behavior: \"يظهر تحكما بالنفس من خلال اتباع القوانين البسيطة (مثال: تناول الطعام على الطاولة).\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية، فاذا كان جاء وقت تناول الطعام للطفل أثناء التقييم، نلاحظ مستوى استجابته للتعليمات المتعلقة بتناول الطعام، وان لم يحدث ذلك فإن سؤال مقدم الرعاية يصبح ضروريا.\" },\n          { id: \"soc-er-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يظهر الحذر في الأوضاع الخطرة، ويتجنب الأخطار الشائعة.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية، إذا لاحظ المقيم ذلك الأمر فإن ذلك يكون مناسبا، ولكن لا ينبغي أن يضع المقيم الطفل في أي موقف خطر، وان لم يحدث الموقف أمامه، بإمكانه سؤال مقدم الرعاية عما إذا كان يتجنب المخاطر مثل الصوبة أو الدرج المرتفع أو غيرها من المخاطر الشائعة.\" },\n          { id: \"soc-er-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يحاول تأدية غالبية المهام باستقلالية (تنظيف الأنف، ارتداء الملابس، تناول الطعام، الذهاب إلى الحمام)\", applicationMethod: \"سؤال مقدم الرعاية عما اذا كان الطفل يحاول تأدية غالبية المهام باستقلالية (تنظيف الأنف، ارتداء الملابس، تناول الطعام، الذهاب إلى الحمام) حتى وان كان لا يتقن أداءها بشكل كامل، ولكن الهدف معرفة مستوى رغبته في الاستقلالية.\" },\n          { id: \"soc-er-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يستكشف المواقع والأشخاص غير المألوفين أثناء وجود مقدم الرعاية.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية هذا السؤال مرحلة متقدمة من سؤال (5)، ولكن يتعلق بالأشخاص والأماكن غير المألوفة، وهنا على المقيم ملاحظة سلوكه نحوه ورغبته في التعرف على المقيم أو التعامل معه، وان لم يظهر ذلك من الممكن سؤال مقدم الرعاية.\" },\n          { id: \"soc-er-10\", itemNumber: \"10\", ageRange: \"3-4 سنوات\", behavior: \"ينتظر من 5-3 دقائق حتى تلبى احتياجاته.\", applicationMethod: \"سؤال مقدم الرعاية عما إذا كان ينتظر من 5-3 دقائق حتى تلبى احتياجاته.\" },\n          { id: \"soc-er-11\", itemNumber: \"11\", ageRange: \"3-4 سنوات\", behavior: \"يعبر عن محبته نحو أصدقاء اللعب وأفراد الأسرة.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: يلاحظ المقيم اذا كان الطفل يعبر عن محبته لنحو أصدقاء اللعب ان وُجدوا، وأفراد الآسرة، وإن لم يُظهر ذلك بإمكانه سؤال مقدم الرعاية عن ذلك.\" },\n          { id: \"soc-er-12\", itemNumber: \"12\", ageRange: \"4-5 سنوات\", behavior: \"يتحكم بردود فعله الانفعالية بمساعدة الكبار.\", applicationMethod: \"سؤال مقدم الرعاية: يستفسر المقيم من مقدم الرعاية مما اذا كان الطفل سريع الغضب حال حدوث أمر يستدعي ذلك أما أنه يضبط نفسه ويتحكم بردود أفعاله.\" },\n          { id: \"soc-er-13\", itemNumber: \"13\", ageRange: \"4-5 سنوات\", behavior: \"يجرب بنفسه خبرات جديدة.\", applicationMethod: \"الملاحظة المباشرة يلاحظ المقيم ومن خلال تفاعله مع الطفل واستخدام الأدوات الموجودة في حقيبة التقييم ما اذا كان الطفل يحب المرور بخبرات التقييم ويتفاعل معها بشكل جيد أم أنه يرفض تجربة الأمور الجديدة.\" },\n          { id: \"soc-er-14\", itemNumber: \"14\", ageRange: \"4-5 سنوات\", behavior: \"يعمل لوحده في مهمة لمدة تتراوح ما بين 20-15 دقيقة.\", applicationMethod: \"الملاحظة المباشرة يلاحظ المقيم ومن خلال تفاعله مع الطفل، ما اذا كان يتنقل بسرعة بين الأنشطة ولا يركز انتباهه في النشاط الواحد لفترة مناسبة، أم أنه يلتزم بالعمل في المهمة (مثال: العمل بالمكعبات) لمدة تتراوح ما بين 20-15 دقيقة.\" },\n          { id: \"soc-er-15\", itemNumber: \"15\", ageRange: \"5-6 سنوات\", behavior: \"يتبع القوانين الموجهة لسلوكهم العام.\", applicationMethod: \"الملاحظة المباشرة وسؤال مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل ينضبط لتنبيهات مقدم الرعاية بالجلوس مع المقيم ومتابعة تعليماته، كما ويستفسر من مقدم الرعاية عما اذا كان في العادة يلتزم بآداب الطعام أثناء تناول الوجبات.\" },\n          { id: \"soc-er-16\", itemNumber: \"16\", ageRange: \"5-6 سنوات\", behavior: \"يعبر عن حقوقه وواجباته.\", applicationMethod: \"الملاحظة المباشرة يستفسر المقيم من الطفل عن مسؤولياته تجاه والديه، وما يفترض أن يقوما به تجاههم.\" },\n          { id: \"soc-er-17\", itemNumber: \"17\", ageRange: \"5-6 سنوات\", behavior: \"يتحدث عن نفسه بإيجابية.\", applicationMethod: \"الملاحظة المباشرة يطلب المقيم من الطفل أن يحدثه عن نفسه ويلاحظ ما اذا كان تعبيره عن نفسه إيجابيا أم سلبيا.\" },\n          { id: \"soc-er-18\", itemNumber: \"18\", ageRange: \"5-6 سنوات\", behavior: \"يضع لنفسه أهدافا وينفذها.\", applicationMethod: \"الملاحظة المباشرة وسؤال مقدم الرعاية يستفسر المقيم من الطفل عن أهدافه للعطلة الصيفية، أو لعطلة منتصف الفصل الدراسي، ويسأل مقدم الرعاية عما اذا كان الطفل ينفذ ما يخطط له بالفعل.\" }\n        ]\n      },\n      {\n        id: \"social-play-development\",\n        name: \"تطور اللعب الاجتماعي\",\n        skills: [\n          { id: \"soc-spd-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يفضل الأشخاص عن الألعاب والأشياء.\", applicationMethod: \"التجريب المباشر بعد أن تطلب من والدته أن تمد يديها نحوه إيذانا بحمله، فيما تقوم بهز الخشخيشة، هل يميل نحو أمه أم الخشخيشة؟.\" },\n          { id: \"soc-spd-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يظهر سعادة ومتعة لمشاهدة الأطفال الآخرين وهم يلعبون.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم نلاحظ ما اذا كان ينظر اليهم وهم يلعبون، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية.\" },\n          { id: \"soc-spd-3\", itemNumber: \"3\", ageRange: \"18-36 شهرًا\", behavior: \"يلعب قرب الأطفال الآخرين، بينما ينفذ كل منهم نشاطا منفصلا.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم يحرص المقيم على توفير لعبة لكل طفل ونلاحظ ما اذا بقي يلعب بلعبته (بجانبهم ودون تفاعل معهم)، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية.\" },\n          { id: \"soc-spd-4\", itemNumber: \"4\", ageRange: \"18-36 شهرًا\", behavior: \"يقلد سلوكيات الآخرين أثناء اللعب (مثال: يتظاهر بالطبخ أو تناول وجبة خفيفة).\", applicationMethod: \"الملاحظة المباشرة: يعطي المقيم الطفل لعبة المطبخ ونبدأ بالتظاهر بالأكل بها أمامه، ونلاحظ ما اذا كان يبدأ بتقليدنا أثناء اللعب بالتظاهر بالطبخ أو تناول الوجبة الخفيفة.\" },\n          { id: \"soc-spd-5\", itemNumber: \"5\", ageRange: \"3-4 سنوات\", behavior: \"يلعب مع 3-1 أطفال ويتشاركون الألعاب.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم يلاحظ المقيم ما اذا كان الطفل يلعب بتعاون ويتشارك أدوات لعبة تشاركية كالمكعبات مع الأطفال الآخرين أثناء اللعب مع أقرانه، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية. (هذا السؤال مرتبط مع سؤال 10 تنظيم ذاتي).\" },\n          { id: \"soc-spd-6\", itemNumber: \"6\", ageRange: \"3-4 سنوات\", behavior: \"يتبع القوانين بتقليد حركات الأطفال الآخرين.\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم يطلب المقيم منهم اللعب في لعبة جماعية لها قوانين محددة وتتضمن أداء بعض الحركات (مثال: كيف بنغسل أيدينا؟ هيك بيطيروا العصافير؟) وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية.\" },\n          { id: \"soc-spd-7\", itemNumber: \"7\", ageRange: \"4-5 سنوات\", behavior: \"يشارك في اللعب الدرامي مع 4-3 أطفال لوقت مناسب.\", applicationMethod: \"سؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل قد أدى دورا في مسرحية أو مشهد تمثيلي، وما اذا كان يشارك في العادة في أنشطة الروضة الدرامية مع 4-3 أطفال.\" },\n          { id: \"soc-spd-8\", itemNumber: \"8\", ageRange: \"4-5 سنوات\", behavior: \"يأخذ دورا.\", applicationMethod: \"سؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل ينتظر دوره أم يحاول تجاوز الآخرين ولا يحترم الدور.\" },\n          { id: \"soc-spd-9\", itemNumber: \"9\", ageRange: \"5-6 سنوات\", behavior: \"يعمل بتعاون مع 5-3 أطفال آخرين.\", applicationMethod: \"الملاحظة المباشرة أو سُؤال مقدم الرعاية اذا كان التقييم يتم في بيئة التعلم المبكر، يلاحظ المقيم ما اذا كان الطفل يتعاون مع 5-3 أطفال آخرين في تنفيذ نشاط مشترك، وان لم يحدث التقييم في بيئة التعلم المبكر من الممكن أن يستفسر من الوالدين عما اذا كان يحدث ذلك في التجمعات الأسرية.\" }\n        ]\n      },\n      {\n        id: \"social-creative-expression\",\n        name: \"التعبير الإبداعي عن الذات\",\n        skills: [\n          { id: \"soc-sce-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يبتسم ويصدر بعض الأصوات والحركات للحصول على الانتباه.\", applicationMethod: \"الملاحظة المباشرة أو سؤال الأسرة نلاحظ ما اذا كان يبتسم أو يصدر بعض الأصوات والحركات للحصول على الانتباه، وان لم يفعل نسأل مقدم الرعاية.\" },\n          { id: \"soc-sce-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يختبر بنفسه طرق إصدار الأصوات المختلفة ويتحرك استجابة للموسيقى.\", applicationMethod: \"الملاحظة المباشرة: يضع الخشخيشة بيد الطفل ويلاحظ ما اذا كان يهزها لإصدار أصوات مختلفة، ويحرك جسمه استجابة لإيقاع مألوف ومفضل سمعه.\" },\n          { id: \"soc-sce-3\", itemNumber: \"3\", ageRange: \"18-36 شهرًا\", behavior: \"يستكشف الخيارات المتنوعة لاستخدام الأدوات الفنية وأدوات البناء (مثال: الألوان، معجونة اللعب، المكعبات).\", applicationMethod: \"الملاحظة المباشرة: يضع المكعبات بيد الطفل ويلاحظ ما إذا كان يتفاعل معها بشكل جيد ويستخدمها بشكل مناسب. يمكن الاستعانة بالألوان ومعجونة اللعب الغذائية الآمنة ان توفرت في بيئة التقييم.\" },\n          { id: \"soc-sce-4\", itemNumber: \"4\", ageRange: \"18-36 شهرًا\", behavior: \"يستجيب للموسيقى بالتصفيق أو الرقص أو بضرب أداتين معا.\", applicationMethod: \"الملاحظة المباشرة: هذه المرحلة متقدمة على سؤال 2، ففي تلك المرحلة تكون الحركة غير متناسقة، ولكن هنا يطلب المقيم من مقدم الرعاية يغني للطفل الأغنية المفضلة أو يديرها على الهاتف المحمول ويلاحظ ردة فعل الطفل عليها بالتصفيق أو الرقص أو ضرب يده بالطاولة أو غيرها من الحركات المُستجيبة للإيقاع.\" },\n          { id: \"soc-sce-5\", itemNumber: \"5\", ageRange: \"3-4 سنوات\", behavior: \"يستخدم قدراته الفنية والأدوات المتوفرة بيديه لتحقيق أهدافه الخاصة.\", applicationMethod: \"الملاحظة المباشرة: يضع المقيم المكعبات بيد الطفل ويلاحظ ما إذا كان يؤدي منها نماذج تعكس مهارته في استخدامها. يمكن الاستعانة بالألوان ومعجونة اللعب الغذائية الآمنة ان توفرت في بيئة التقييم.\" },\n          { id: \"soc-sce-6\", itemNumber: \"6\", ageRange: \"3-4 سنوات\", behavior: \"يشارك في الألعاب الموسيقية أو يستجيب للإيقاع الموسيقي.\", applicationMethod: \"الملاحظة المباشرة: يطلب المقيم من مقدم الرعاية أن يؤدي مع الطفل لعبة موسيقية (مثال: حركة وصنم، أو بعد الأناشيد التي تتضمن أداء مثل القفز والدوران)، ويلاحظ استجابته بتأدية حركات مناسبة للإيقاع.\" },\n          { id: \"soc-sce-7\", itemNumber: \"7\", ageRange: \"4-5 سنوات\", behavior: \"يستجيب للإيقاع بأداء حركات منظمة.\", applicationMethod: \"الملاحظة المباشرة وسؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما إذا كان للطفل مهارات أدائية مرتبطة بالرقص أو الدبكة، وان كان كذلك يطلب منه مشاهدة تسجيلات له، أو ان كان لدى الطفل الرغبة في أدائها أمام المقيم يمكنه مشاهدتها مباشرة.\" },\n          { id: \"soc-sce-8\", itemNumber: \"8\", ageRange: \"4-5 سنوات\", behavior: \"يرسم رسوما بسيطة ومفهومة أو يستخدم بعض الأدوات في تنفيذ تصميم معين.\", applicationMethod: \"الملاحظة المباشرة يطلب المقيم من الطفل أن يرسم باستخدام الورق وألوان الفلومستر، ويلاحظ ما اذا كانت رسومه مفهومة (مثال: الرأس واليدين والقدمين لرسم الرجل).\" },\n          { id: \"soc-sce-9\", itemNumber: \"9\", ageRange: \"5-6 سنوات\", behavior: \"يرسم ويبني بتفصيل وإبداع أكثر.\", applicationMethod: \"الملاحظة المباشرة يطلب المقيم من الطفل أن يرسم باستخدام الورق وألوان الفلومستر، ويلاحظ ما اذا كانت رسومه تتضمن تفاصيل أكثر (مثال: الرأس بتفاصيل العينين والأنف والفم والأذنين والشعر واليدين والقدمين لرسم الرجل).\" },\n          { id: \"soc-sce-10\", itemNumber: \"10\", ageRange: \"5-6 سنوات\", behavior: \"يشارك في الأنشطة الأدائية أو الإيقاعية مع الأطفال الآخرين (الغناء، الرقص، الخ).\", applicationMethod: \"الملاحظة المباشرة وسؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان للطفل قد أدى عروضا أدائية مرتبطة بالرقص أو الدبكة في أي من احتفالات الروضة، وان كان كذلك يطلب منه مشاهدة تسجيلات له.\" }\n        ]\n      },\n    ],\n  },\n  {\n    id: \"exploration-learning\",\n    name: \"بعد الاستكشاف وطرق التعلم\",\n    subCategories: [\n      {\n        id: \"exp-cognitive-development\",\n        name: \"التطور الإدراكي\",\n        skills: [\n            { id: \"exp-cog-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يظهر وعيا بالأوضاع والجديدة والغريبة.\", applicationMethod: \"المُلاحظة المباشرة لردة فعل الطفل عند حضور المقيم أول مرة، وسؤال والدته عما اذا كانت ردة فعله هذه يومية أم هي بسبب وجود المقيم.\" },\n            { id: \"exp-cog-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يلعب بنفس اللعبة لمدة تتراوح من 5-2 دقائق.\", applicationMethod: \"المُلاحظة المباشرة نضع وعاء المكعبات وهو مفتوح أمامه ونلاحظ ما إذا كان يلعب بها لفترة مناسبة لا تقل عند دقيقتين.\" },\n            { id: \"exp-cog-3\", itemNumber: \"3\", ageRange: \"9-18 شهرًا\", behavior: \"يجمع أداتان معا أثناء اللعب.\", applicationMethod: \"المُلاحظة المباشرة نضع أمامه مكعبين، ونجلس مقابله ونحمل مكعبين، ثم نقوم بطرق المكعبين الذين بيدينا أمامه ونلاحظ ما اذا كان يقلد ما قمنا به.\" },\n            { id: \"exp-cog-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يستكشف طرقا للتحكم بالبيئة.\", applicationMethod: \"المُلاحظة المباشرة أثناء انشغاله بالمكعب، نسحبه من يده ونخفيه تحت غطاء الوعاء، أو تحت الفراش، ونلاحظ ما اذا كان يُحاول الوصول إليه بأي طريقة كانت.\" },\n            { id: \"exp-cog-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يمر بخبرات حول الأحجام والعلاقات الفراغية أثناء اللعب.\", applicationMethod: \"المُلاحظة المباشرة نضع وعاء المكعبات وهو مفتوح أمامه ونلاحظ ما اذا كان قد طور مهارات فراغية متقدمة من حيث وضع الأشياء تحت الغطاء مثلا، أو تكديسها فوق بعضها البعض، أو صفها على شكل قطار.\" },\n            { id: \"exp-cog-6\", itemNumber: \"6\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم أداة واحدة لتقوم مقام أخرى أثناء اللعب بتوجيه من الكبار.\", applicationMethod: \"الملاحظة المباشرة نمسك أمام الطفل قطعة من المكعبات مستطيلة الشكل ونتظاهر بأننا نرغب بالحديث معه عبر الهاتف، ونعطيه قطعة مشابهة لها، ونبدأ بالحديث كما لو كانت القطعة المستطيلة هاتفا، ونلاحظ ما اذا قربها في حينه أو لاحقا من أذنه للتظاهر بالحديث عبر الهاتف.\" },\n            { id: \"exp-cog-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يطابق ويقارن الأشكال المتشابهة.\", applicationMethod: \"الملاحظة المباشرة نضع أمام الطفل قطعة مربعة من المكعبات، ونطلب منه أن يختار ما يشبهها من مجموعة المكعبات، وكذلك قطعة مستطيلة، وأخيرا قطعة دائرية.\" },\n            { id: \"exp-cog-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم أصابعه لترتيب الأشياء أو وضع الأشياء الصغيرة في وعاء مفتوح أو لتقليب الصفحات.\", applicationMethod: \"الملاحظة المباشرة نطلب من الطفل إعادة وضع المكعبات في الوعاء واحدة تلو الأخرى.\" },\n            { id: \"exp-cog-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يصنف الأدوات في مجموعات.\", applicationMethod: \"الملاحظة المباشرة نطلب من الطفل جمع الألوان المتشابهة مع بعضها البعض– يمكن قبول الأداء إن جمع الأدوات حسب الشكل أو الحجم.\" },\n            { id: \"exp-cog-10\", itemNumber: \"10\", ageRange: \"3-4 سنوات\", behavior: \"يكمل أحجية مكونة من 10-5 قطع.\", applicationMethod: \"المُلاحظة المباشرة يضع الميسر أحجية مجزأة إلى 5 قطع، ويطلب من الطفل تركيب قطعها.\" },\n            { id: \"exp-cog-11\", itemNumber: \"11\", ageRange: \"4-5 سنوات\", behavior: \"يفهم بعض الأنماط ويقلدها.\", applicationMethod: \"الملاحظة المباشرة يختار المقيم من المكعبات 4 مكعبات لونها أصفر و4 مكعبات لونها أخضر من نفس الحجم، ويبدأ بوضع مكعب أصفر ثم أخضر ويطلب من الطفل أن يكمل ترتيبها بنفس التسلسل.\" },\n            { id: \"exp-cog-12\", itemNumber: \"12\", ageRange: \"5-6 سنوات\", behavior: \"يرتب الأشياء تبعا للطول أو الحجم.\", applicationMethod: \"الملاحظة المباشرة يحضر المقيم مكعبا ومستطيلا متوسط الطول، وآخر طويل ويطلب من الطفل ترتيبها من الأقصر إلى الأطول (اذا لم تتوفر مكعبات متفاوتة في الطول ممكن أن يكون الترتيب بحسب الحجم من الأصغر إلى الأكبر).\" },\n            { id: \"exp-cog-13\", itemNumber: \"13\", ageRange: \"5-6 سنوات\", behavior: \"يركب أحجية مكونة من 10 قطع أو أكثر.\", applicationMethod: \"المُلاحظة المباشرة يضع الميسر أحجية مجزأة إلى 10 قطع، ويطلب من الطفل تركيب قطعها.\" },\n            { id: \"exp-cog-14\", itemNumber: \"14\", ageRange: \"5-6 سنوات\", behavior: \"يخبر عن أوجه شبه الأشياء وأوجه اختلافها.\", applicationMethod: \"الملاحظة المباشرة يحضر المقيم مكعبا كبيرا أحمر، ومكعبا صغيرا أصفر، ويطلب من الطفل أن يخبره عن الفرق بين المكعبين (الإجابة لابد أن تراعي الفروق في الحجم واللون).\" },\n        ]\n      },\n      {\n        id: \"exp-critical-thinking\",\n        name: \"التفكير الناقد والاستكشاف\",\n        skills: [\n            { id: \"exp-crit-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يهز أداة محددة بصورة متكررة.\", applicationMethod: \"المُلاحظة المباشرة نطلب من الأم/ مقدم الرعاية أن تضع بيده خشخيشته الخاصة، ونلاحظ إذا هزها بصورة متكررة (ان لم تتواجد لدى الطفل خشخيشة يستعين المقيم بخشخيشة من حقيبة ألعابه).\" },\n            { id: \"exp-crit-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يبحث ببصره عن مصدر صوت ما.\", applicationMethod: \"المُلاحظة المباشرة نطلب من الأم/ مقدم الرعاية أن تحرك الخشخيشة خلف رأسه، ونلاحظ ما اذا كان يبحث ببصره عن مصدر الصوت (ان لم تتواجد لدى الطفل خشخيشة يستعين المقيم بخشخيشة من حقيبة ألعابه).\" },\n            { id: \"exp-crit-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يفضل الألعاب غير المألوفة على الألعاب المألوفة.\", applicationMethod: \"المُلاحظة المباشرة نطلب من الأم/ مقدم الرعاية أن تُحضر احدى أدوات الطفل المألوفة له، وتضع أمامه لعبته المألوفة بالإضافة إلى الخشخيشة الخاصة بالمقيم والموجودة في حقيبة التقييم، ونلاحظ ما اذا كان مد يده أو وجه بصره نحو خشخيشة المقيم كمؤشر لتفضيله الألعاب غير المألوفة على الألعاب المألوفة.\" },\n            { id: \"exp-crit-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يبدأ بتسمية الأشياء/ الأشخاص عند غيابهم.\", applicationMethod: \"سؤال مقدم الرعاية نستفسر من مقدم الرعاية عما اذا كان الطفل يسمي أشخاصا مألوفين له عند غيابهم (مثال: تسمية ماما، أو بابا، أو أحد أشقائه) في مؤشر عن استفقاد لهم.\" },\n            { id: \"exp-crit-5\", itemNumber: \"5\", ageRange: \"18-36 شهرًا\", behavior: \"يظهر فهمه لاثنين أو أكثر من الكلمات الفئوية.\", applicationMethod: \"الملاحظة المباشرة نطلب منه بتزويدنا بمكعب، وان زودنا بمكعب واحد نقول له اعطني مكعبات، ونلاحظ ما اذا أعطانا أكثر من مكعب في مؤشر لفهمه لصيغة الجمع (الكلمات الفئوية).\" },\n            { id: \"exp-crit-6\", itemNumber: \"6\", ageRange: \"18-36 شهرًا\", behavior: \"يخبر عن سلسلة من نشاطين مألوفين.\", applicationMethod: \"الملاحظة المباشرة نسأله عما يفعله عند الاستيقاظ من النوم، ونلاحظ ما اذا كان يسرد حدثين على الأقل بالتسلسل (تقبل الإشارة إذا كان الطفل غير ناطق).\" },\n            { id: \"exp-crit-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم الخصائص الفردية لكل لعبة وأداة أثناء اللعب.\", applicationMethod: \"الملاحظة المباشرة نزوده بأدوات المطبخ وبوعاء فيه مكعبات، ونطلب منه بناء بيت، ونلاحظ ما اذا كان يستخدم المكعبات في ذلك، ثم نطلب منه التظاهر بطهي الطعام، ونلاحظ ما اذا كان يستخدم لعبة أدوات المطبخ في ذلك.\" },\n            { id: \"exp-crit-8\", itemNumber: \"8\", ageRange: \"3-4 سنوات\", behavior: \"يسمي الأشياء التي تتناسب معا أو يزاوج بينها عندما يطلب منه ذلك.\", applicationMethod: \"الملاحظة المباشرة نضع القلم والملعقة أمامه على الطاولة (أو الأرض)، ثم نطلب منه أن يضع المبراة مع ما يناسبها من الأدوات الموجودة على الأرض، ثم أن يضع الشوكة مع ما يناسبها من الأدوات الموضوعة على الأرض.\" },\n            { id: \"exp-crit-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يجيب على أسئلة (ماذا لو...؟) بذكر الأفعال المناسبة.\", applicationMethod: \"الملاحظة المباشرة نسأله عما يحدث إذا اقترب أحد من النار؟، أو إذا كسر أحد كوب زجاج؟ (تقبل الإشارة إذا كان الطفل غير ناطق).\" },\n            { id: \"exp-crit-10\", itemNumber: \"10\", ageRange: \"4-5 سنوات\", behavior: \"يخبر عن الأنشطة اللاحقة.\", applicationMethod: \"الملاحظة المباشرة نسأله عما سيفعله عند انتهاء التقييم (تقبل الإشارة إذا كان الطفل غير ناطق).\" },\n            { id: \"exp-crit-11\", itemNumber: \"11\", ageRange: \"4-5 سنوات\", behavior: \"يُخبر عن توقعاته لما سيحدث.\", applicationMethod: \"الملاحظة المباشرة يستفسر مقدم الرعاية من الطفل عن توقعاته لما ستفعله الأسرة في العطلة الصيفية مثلا ، وقد يجيب: نذهب إلى رحلة، نذهب إلى البحر،....الخ.\" },\n            { id: \"exp-crit-12\", itemNumber: \"12\", ageRange: \"5-6 سنوات\", behavior: \"يتوصل إلى حلول متنوعة لكل سؤال أو مهمة أو مشكلة بالتعاون مع أقرانه أو الكبار من حوله.\", applicationMethod: \"الملاحظة المباشرة يستفسر مقدم الرعاية من الطفل عما يتوقع حدوثه في حال تعطلت سيارة والده، أو في حال انقطاع الكهرباء، ويلاحظ ما اذا كان يصل الى طرح حلول متنوعة ومقنعة حتى لو تشاور بدرجة قليلة مع من حوله من الكبار.\" },\n        ]\n      },\n      {\n        id: \"exp-early-math\",\n        name: \"الرياضيات المبكرة\",\n        skills: [\n            { id: \"exp-math-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يظهر تفضيله لألعاب أو أشياء محددة.\", applicationMethod: \"المُلاحظة المباشرة نضع أمامه الوعاء الذي يضم مجموعة من المكعبات، ونلاحظ ما اذا كان يفضل أحدها وينجذب ربما للونه أو لملمسه.\" },\n            { id: \"exp-math-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يضع أدوات في وعاء ويفرغها منه.\", applicationMethod: \"المُلاحظة المباشرة نضع وعاء المكعبات وهو مفتوح أمامه ونلاحظ ما إذا كان يضع المكعبات بداخل الوعاء ويخرجها منها.\" },\n            { id: \"exp-math-3\", itemNumber: \"3\", ageRange: \"18-36 شهرًا\", behavior: \"يعطي المزيد من الشيء حين يطلب منه ذلك.\", applicationMethod: \"الملاحظة المباشرة: يحضر مجموعة من المكعبات من حقيبة التقييم، ويضعها أمام الطفل، ويطلب أن يناوله أحدها، وبعد أن يناوله أحده يطلب منه اعطاءه المزيد (كمان).\" },\n            { id: \"exp-math-4\", itemNumber: \"4\", ageRange: \"18-36 شهرًا\", behavior: \"يلمس ويعد من 3-1 أشياء.\", applicationMethod: \"الملاحظة المباشرة يحضر المقيم 3 مكعبات من حقيبة التقييم ويرتبها بشكل متتال، ثم يطلب من الطفل أن يضع أصبعه عليها واحدة تلو الأخرى ليعدها.\" },\n            { id: \"exp-math-5\", itemNumber: \"5\", ageRange: \"3-4 سنوات\", behavior: \"يميز الأرقام الملصقة (مثال: رقم الغرفة، رقم الصفحة).\", applicationMethod: \"الملاحظة المباشرة: يحضر المقيم من حقيبة التقييم قصة مصورة ويطلب من الطفل قراءة الأرقام البسيطة الموجودة فيها.\" },\n            { id: \"exp-math-6\", itemNumber: \"6\", ageRange: \"3-4 سنوات\", behavior: \"يلمس ويعد الأرقام والأشياء المتزايدة.\", applicationMethod: \"الملاحظة المباشرة: يحضر المقيم عدادا من حقيبة التقييم، ويطلب منه عد خط منها.\" },\n            { id: \"exp-math-7\", itemNumber: \"7\", ageRange: \"3-4 سنوات\", behavior: \"يزاوج بين الأشياء (واحد إلى واحد).\", applicationMethod: \"الملاحظة المباشرة: يضع المقيم 5 مكعبات منفردة كل على حدى، ثم يطلب من الطفل أن يضع مكعبا بجانب كل مكعب منفرد.\" },\n            { id: \"exp-math-8\", itemNumber: \"8\", ageRange: \"4-5 سنوات\", behavior: \"يعد إلى 7 مع مفهوم العدد.\", applicationMethod: \"الملاحظة المباشرة يحضر المقيم 7 مكعبات من حقيبة التقييم ويطلب من المقيم اخباره عن عددها.\" },\n            { id: \"exp-math-9\", itemNumber: \"9\", ageRange: \"4-5 سنوات\", behavior: \"يظهر فهما للمقاييس المختلفة.\", applicationMethod: \"الملاحظة المباشرة يحضر المقيم من حقيبة التقييم لعبة الأوزان، والتي تتضمن قطعتي قماش متماثلتين تماما في الشكل ومختلفتين في الوزن، ويطلب من الطفل وضع احداها في كفه اليمين، والثانية في كفه اليسار، ثم يطلب منه وصفها بالكلمات (خفيف، وثقيل).\" },\n            { id: \"exp-math-10\", itemNumber: \"10\", ageRange: \"5-6 سنوات\", behavior: \"يقرأ الأرقام المكتوبة من 5-1.\", applicationMethod: \"الملاحظة المباشرة يكتب المقيم الأرقام من 1 إلى 5 ويطلب من الطفل أن يقرأها بشكل غير متسلسل.\" },\n            { id: \"exp-math-11\", itemNumber: \"11\", ageRange: \"5-6 سنوات\", behavior: \"يقارن كميا بين المجموعات مستخدما كلمات بسيطة.\", applicationMethod: \"الملاحظة المباشرة يضع 5 مكعبات في المجموعة الأولى، و3 مكعبات في المجموعة الثانية، وأخيرا مكعبا واحدا لوحده ويطلب من الطفل وصفها مستخدما عبارات (قليل جدا، ومتوسط وكثير).\" },\n            { id: \"exp-math-12\", itemNumber: \"12\", ageRange: \"5-6 سنوات\", behavior: \"يجمع ويطرح الأدوات والأشياء الملموسة.\", applicationMethod: \"الملاحظة المباشرة يحضر المقيم من حقيبة التقييم ثلاث مكعبات، ويسأل الطفل إذا ضفنا اليها مكعبين كم يصبح المجموع؟ ثم يسأله بعد أن أصبح عددها 5 مكعبات لو أخذت منها مكعبا كم يصبح العدد.\" },\n        ]\n      },\n      {\n        id: \"exp-science\",\n        name: \"العلوم\",\n        skills: [\n          { id: \"exp-sci-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يستكشف الألعاب بنفس الطريقة (يهزها، يرميها، يضربها، يضعها في فمه).\", applicationMethod: \"نلاحظ عند وضع مكعب بيد الطفل ما اذا كان يهزه، أو يرميه، أو يضربه، أو يضعه في فمه مُحاولة منه لاستكشافه.\" },\n          { id: \"exp-sci-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يبحث عن مقدم الرعاية أو عن لعبة أزيحت عن مستوى نظره.\", applicationMethod: \"نُقرب المكعب مستوى نظر الطفل (يفضل اختيار مكعب بلون غامق) ونزيحه فجأة من مستوى نظره ونلاحظ ما اذا كان الطفل يحرك رأسه بحثا عنه.\" },\n          { id: \"exp-sci-3\", itemNumber: \"3\", ageRange: \"18-36 شهرًا\", behavior: \"يظهر وعيا بنتائج أفعاله في الأوضاع المألوفة.\", applicationMethod: \"نسأل مقدم الرعاية عما اذا كان يعتذر ان فعل شيئا خاطئا، وما اذا كان يحاول تنظيف ما سكبه.\" },\n          { id: \"exp-sci-4\", itemNumber: \"4\", ageRange: \"3-4 سنوات\", behavior: \"يصف وظائف الأشياء.\", applicationMethod: \"يحضر المقيم قلم، ويسأل الطفل عما نعمله به، ثم يحضر مقص ويسأل الطفل عما نفعله به، ثم يحضر ملعقة ويسأل الطفل عما نفعله بها.\" },\n          { id: \"exp-sci-5\", itemNumber: \"5\", ageRange: \"4-5 سنوات\", behavior: \"يمر بخبرات جديدة ويجرب بنفسه أشياء ترتبط بها.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية بطرح بعض الأسئلة مثل: هل يحب الطفل المغامرات الجديدة؟ أو الذهاب إلى أماكن جديدة؟ أو تناول مأكولات غريبة وجديدة عليه؟.\" },\n          { id: \"exp-sci-6\", itemNumber: \"6\", ageRange: \"5-6 سنوات\", behavior: \"يحل المشكلات من خلال الاستكشاف بفاعلية والمرور بخبرات المحاولة والخطأ أو الدخول في حوارات ونقاشات مع الآخرين.\", applicationMethod: \"يستفسر من مقدم الرعاية عما إذا كان الطفل قد مر بتحد ما ولجأ إلى حله بمفرده أو ناقش الحلول المقترحة مع والده (مثال: إذا رغب الطفل في تناول شيء من الطعام أثناء انشغال الكبير، هل حاول تنفيذها لنفسه وبمفرده؟ أو ناقش طريقة عملها مع الطفل).\" },\n          { id: \"exp-sci-7\", itemNumber: \"7\", ageRange: \"5-6 سنوات\", behavior: \"يلاحظ ويناقش ويصف عالم الطبيعة والكائنات الحية والعمليات الطبيعية.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل قد تعلم شيئا له علاقة بدورة حياة الكائنات الحية في الطبيعة، أو الكائنات التي تلد والتي تبيض، وان أجاب بالإيجاب يطلب من مقدم الرعاية أن يسأل الطفل أسئلة حولها.\" },\n        ]\n      },\n    ],\n  },\n  {\n    id: \"communication\",\n    name: \"البعد الاتصالي\",\n    subCategories: [\n      {\n        id: \"comm-communication\",\n        name: \"التواصل\",\n        skills: [\n          { id: \"comm-comm-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يظهر ردود فعل مناسبة حين يحرك أحد جسمه أو حين يكون محمولا.\", applicationMethod: \"نلاحظ ما اذا كان يُظهر ارتياحا حين يحرك مقدم الرعاية جسمه أو حين يحمله.\" },\n          { id: \"comm-comm-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يتوقف عن البكاء عندما يحمله شخص كبير.\", applicationMethod: \"نلاحظ ما اذا كان يتوقف عن البكاء عندما يحمله شخص كبير، وان لم يبك أثناء التقييم يمكننا سؤال مقدم الرعاية عن ذلك.\" },\n          { id: \"comm-comm-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يختلف بكاءه عند الجوع عن الألم عن عدم الراحة.\", applicationMethod: \"نسأل مقدم الرعاية عما اذا كان بكاءه يختلف باختلاف حاجته (مثال: بكاء الجوع يختلف عن بكاء الألم أو عن البكاء تعبيرا عن عدم الراحة).\" },\n          { id: \"comm-comm-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يستخدم الأصوات والإشارات والإيماءات للفت الانتباه والتعليق والاستجابة.\", applicationMethod: \"عند انشغال مقدم الرعاية بالحديث مع المقيم، نلاحظ ما اذا بدأ الطفل باستخدام الأصوات والإشارات والإيماءات للفت الانتباه، فإن لم يحدث ذلك أثناء التقييم نستفسر من مقدم الرعاية.\" },\n          { id: \"comm-comm-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يفهم التعليمات البسيطة ويستجيب إليها.\", applicationMethod: \"نطلب من مقدم الرعاية أن يقول للطفل أي تعبير يفهمه عليه ونلاحظ استجابته له (مثال: هات، باي.....الخ).\" },\n          { id: \"comm-comm-6\", itemNumber: \"6\", ageRange: \"9-18 شهرًا\", behavior: \"يحرك رأسه بالنفي أو الإيجاب بطريقة ذات معنى.\", applicationMethod: \"نطلب من مقدم الرعاية أن يسأل الطفل أسئلة بسيطة مثل (تأكل؟، تنام؟،...الخ) ونلاحظ ما اذا حرك رأسه بالنفي أو الإيجاب.\" },\n          { id: \"comm-comm-7\", itemNumber: \"7\", ageRange: \"9-18 شهرًا\", behavior: \"يشارك في الأناشيد والألعاب الغنائية.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن ينشد أنشودة مفضلة للطفل ونلاحظ ما اذا كان الطفل يتمتم معها بأصوات أو كلمات بسيطة، أو حتى بالإيقاع.\" },\n          { id: \"comm-comm-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يُعبر عن احتياجاته بالكلمات والأصوات والإيماءات (مثال: الحمام، والجوع، والألم).\", applicationMethod: \"يسأل المقيم مقدم الرعاية عما اذا كان الطفل يُعبر عن احتياجاته الأساسية (مثال: الحمام، والجوع، والألم).(بالكلمات والأصوات والإيماءات).\" },\n          { id: \"comm-comm-9\", itemNumber: \"9\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم بعض كلمات القياس (كبير، صغير، قليل) في وصف الأشياء.\", applicationMethod: \"نخبر الطفل بأن من بين المكعبات الموضوعة أمامه مكعبات كبيرة وأخرى صغيرة، ثم نشير لأحد المكعبات ونقول له هذا..... وننتظر منه الإجابة، ثم نسأل عن الحجم الثاني.\" },\n          { id: \"comm-comm-10\", itemNumber: \"10\", ageRange: \"18-36 شهرًا\", behavior: \"يضحك على المثيرات والأفعال المُضحكة.\", applicationMethod: \"نطلب من مقدم الرعاية مداعبته بالطريقة المفضلة لديه، ونلاحظ استجابته بالابتسام أو الضحك.\" },\n          { id: \"comm-comm-11\", itemNumber: \"11\", ageRange: \"3-4 سنوات\", behavior: \"يتحدث عن الأشياء المألوفة، والأفعال والخبرات المباشرة أو القصص المألوفة.\", applicationMethod: \"يطلب المقيم من الطفل أن يتحدث عن الأشياء المألوفة (مثال: الروضة)، والأفعال والخبرات المباشرة (مثال: ماذا تفعل في البقالة) أو القصص المألوفة (مثال: القصة التي اعتادت جدته أن تقصها عليه).\" },\n          { id: \"comm-comm-12\", itemNumber: \"12\", ageRange: \"3-4 سنوات\", behavior: \"يسأل أسئلة ليشبع فضوله ويجيب على أسئلة من وكيف وماذا.\", applicationMethod: \"نلاحظ خلال جلسة التقييم ما اذا كان الطفل يسأل أسئلة ليشبع فضوله (مثال: قد يسأل المقيم عما يوجد في حقيبته) ويجيب على أسئلة من وكيف وماذا (مثال: أن يجيب على أسئلة مقدم الرعاية التي تبدأ بمن- من أمك- أو كيف-,كيف حالك- أو ماذا- مثل ماذا أفطرت؟-( تُقبل الإشارة في الحالات التي تتطلب قبولها.\" },\n          { id: \"comm-comm-13\", itemNumber: \"13\", ageRange: \"3-4 سنوات\", behavior: \"يستخدم اللغة في التحكم والتفاوض أثناء اللعب مع الآخرين.\", applicationMethod: \"خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يستخدم كلمات بسيطة محكية أو بالإشارة في النقاش مع المقيم أو حتى مع مقدم الرعاية، وإن لم يظهر ذلك نستفسر من مقدم الرعاية.\" },\n          { id: \"comm-comm-14\", itemNumber: \"14\", ageRange: \"4-5 سنوات\", behavior: \"يتبع التعليمات المعقدة التي تتضمن أكثر من طلب.\", applicationMethod: \"يطلب منه مقدم الرعاية أن يأخذ مكعبا ويعطيه لوالدته ثم يغلق الباب (أو يفتح الباب) ان استجاب الطفل بسهولة ولم يعد بعد الطلب الأول دون تنفيذ بقية الطلبات لا تعتبر استجابته مناسبة.\" },\n          { id: \"comm-comm-15\", itemNumber: \"15\", ageRange: \"5-6 سنوات\", behavior: \"يشارك تجاربه أو ممتلكاته لفظيا مع أقرانه.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل في العادة يقبل بأن يلعب أقرانه عند زيارته في المنزل بألعابه أم أنه يرفض مشاركتها؟.\" },\n          { id: \"comm-comm-16\", itemNumber: \"16\", ageRange: \"5-6 سنوات\", behavior: \"يقول نكات بسيطة.\", applicationMethod: \"يطلب المقيم من الطفل أن يسرد عليه نكتة بسيطة.\" },\n          { id: \"comm-comm-17\", itemNumber: \"17\", ageRange: \"5-6 سنوات\", behavior: \"يتحدث بعبارات بسيطة من لغة أخرى (غير اللغة الأم).\", applicationMethod: \"يسأل المقيم مقدم الرعاية عما اذا كان الطفل قد تعلم بعض الكلمات بلغة غير الأم (اذا كانت اللغة العربية الأم قد تكون اللغة المطلوبة هي الإنجليزية)، ومن ثم يطلب منه أن يخبر الطفل بالحديث مستخدمها أو اخبار المقيم عنها.\" }\n        ]\n      },\n      {\n        id: \"comm-speech-language\",\n        name: \"الكلام واللغة\",\n        skills: [\n          { id: \"comm-sl-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يلعب بالأصوات، ويعيد أصواته وسجعاته وضحكاته الشخصية.\", applicationMethod: \"خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يناغي ويصدر أصواتا تعكس عن ارتياحه، وان لم يفعلها أمامنا نستفسر من مقدم الرعاية عن ذلك.\" },\n          { id: \"comm-sl-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يُثرثر أو يُحاول تقليد الأصوات الكلامية.\", applicationMethod: \"خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يجيب على كلام الكبير (المقيم أو مقدم الرعاية) بمناغاة أو أصوات يصدرها، وإن لم يفعل نستفسر من مقدم الرعاية.\" },\n          { id: \"comm-sl-3\", itemNumber: \"3\", ageRange: \"9-18 شهرًا\", behavior: \"يتبع التعليمات البسيطة كالإشارة إلى أجزاء الجسم، والأشياء أو يتبع تعليمات الخطوة الواحدة.\", applicationMethod: \"نطلب من مقدم الرعاية أن يسأله (أين عينك؟) ونلاحظ ما اذا كان يضع يده على عينه.\" },\n          { id: \"comm-sl-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يبدأ باستخدام أجزاء الكلمات للإشارة إلى الأشياء المألوفة (مثال: نم للإشارة إلى الطعام).\", applicationMethod: \"يلاحظ المقيم ما اذا كان يستخدم أجزاء الكلمات للإشارة إلى الأشياء المألوفة (مثال: نم للإشارة إلى الطعام) أثناء تواجده وان لم يفعل نستفسر من مقدم الرعاية.\" },\n          { id: \"comm-sl-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يستخدم عددا من الكلمات والمصطلحات والإشارات الرسمية.\", applicationMethod: \"يلاحظ المقيم ما اذا كان يستخدم عددا من الكلمات والمصطلحات (مثال: هات، راح، نام، باي) أثناء فترة التقييم.\" },\n          { id: \"comm-sl-6\", itemNumber: \"6\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم جملة مكونة من كلمتين أو ثلاث كلمات.\", applicationMethod: \"يلاحظ المقيم ما اذا كان الطفل أظهر استخدامه لجملة مكونة من كلمتين أو ثلاث كلمات أثناء فترة التقييم.\" },\n          { id: \"comm-sl-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم 50 كلمة أو إشارة.\", applicationMethod: \"يلاحظ المقيم مخزون الطفل اللغوي وما اذ كان يتحدث بكلمات واضحة (أو إشارات واضحة) للتعبير عن حاجاته وبما لا يقل عن 50 كلمة أو إشارة خلال فترة التقييم.\" },\n          { id: \"comm-sl-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم صيغ الجمع الشائعة.\", applicationMethod: \"نضع مكعب منفرد، ومجموعة مكعبات مع بعضها البعض ضمن مجموعة، ونشير الى المكعب المنفرد ونقول له هذا مكعب، ثم نشير إلى المجموعة ونقول له هذه..... وننتظر منه إتمام الكلمة (مكعبات.).\" },\n          { id: \"comm-sl-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يستخدم جملا أطول في التواصل.\", applicationMethod: \"يلاحظ المقيم ما اذا كان الطفل يستخدم جملا أطول في التواصل طوال فترة التقييم بحيث تتناسب مع الأطفال في سن الرابعة.\" },\n          { id: \"comm-sl-10\", itemNumber: \"10\", ageRange: \"4-5 سنوات\", behavior: \"يتحدث مستخدما جملا معقدة لوصف الأحداث والأشياء.\", applicationMethod: \"خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يتحدث مستخدما جملا معقدة لوصف الأحداث والأشياء مع المقيم أو حتى مع مقدم الرعاية.\" },\n          { id: \"comm-sl-11\", itemNumber: \"11\", ageRange: \"4-5 سنوات\", behavior: \"يستخدم مفردات متنوعة أثناء الحديث.\", applicationMethod: \"خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يتحدث مستخدما مفردات متنوعة أثناء الحديث مع المقيم أو حتى مع مقدم الرعاية.\" },\n          { id: \"comm-sl-12\", itemNumber: \"12\", ageRange: \"4-5 سنوات\", behavior: \"يُخبر عن أضداد الكلمات.\", applicationMethod: \"يسأل المقيم الطفل عن المعنى المعاكس لبعض الكلمات (مثال: ما عكس نهار: ليل، وما عكس أبيض؟ أسود، وما عكس مفتوح؟ مغلق).\" },\n          { id: \"comm-sl-13\", itemNumber: \"13\", ageRange: \"5-6 سنوات\", behavior: \"يستخدم جملا مكونة من 8-6 كلمات.\", applicationMethod: \"خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يتحدث مستخدما جملا طويلة مكونة من 8-6 كلمات أثناء الحديث مع المقيم أو حتى مع مقدم الرعاية.\" },\n          { id: \"comm-sl-14\", itemNumber: \"14\", ageRange: \"5-6 سنوات\", behavior: \"يربط بين الأصوات والكلمات المكتوبة.\", applicationMethod: \"يحضر المقيم قلما ورقة من حقيبة التقييم، ويقوم بكتابة كلمات بسيطة مثل (باب) ثم يقرأها أمام الطفل: بـــــــاااااب ثم يطلب منه أن يشير الى حرف الـ (ا) المكتوب، ونكرر ذلك مع أحرف العلة الأخرى (مثال: بووووووت، أين الـ (و)، بيييييييت، أين الـ (ي)).\" }\n        ]\n      },\n      {\n        id: \"comm-early-reading\",\n        name: \"القراءة المبكرة\",\n        skills: [\n          { id: \"comm-er-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"ينظر إلى جسم أو شخص عند تسميته.\", applicationMethod: \"ينادي المقيم باسم مقدم الرعاية ويلاحظ ما اذا كان الطفل ينظر اليه عند تسميته.\" },\n          { id: \"comm-er-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"ينظر إلى صور في كتاب الصور.\", applicationMethod: \"يضع المقيم أمام الطفل كتاب مصور شريطة أن تكون ألوانه متغايرة (واضحة وليست فاتحة)، ويلاحظ ما اذا كان الطفل ينظر إلى الصور الموجودة في الكتاب.\" },\n          { id: \"comm-er-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يستمع لمدة دقيقة إلى دقيقتين لقصة تسرد عليه.\", applicationMethod: \"يعطي المقيم الكتاب لمقدم الرعاية ويطلب منه سرد القصة الموجودة فيه على الطفل، ويلاحظ ما اذا كان الطفل يتابع القصة لمدة لا تقل عن دقيقة.\" },\n          { id: \"comm-er-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"يختار كتابا مفضلا لينظر إليه أو ليقرأه شخص كبير عليه.\", applicationMethod: \"يسأل المقيم مقدم الرعاية عما اذا كان لدى الطفل كُتب مناسبة لعمره، واذا كانت الأسرة معتادة على القراءة له وان كان الأمر كذلك يطلب منه احضار كتابين من كتب الطفل، ويطلب منه أن يضعهما أمامه ويلاحظ ما اذا كان الطفل سيختار أحدهما سواء بالنظر أو مد يده إليه (ملاحظة: اذا لم تعتد الطفلة على القراءة للطفل ولا تتواجد لديه كتب مناسبة نضع إشارة x.\" },\n          { id: \"comm-er-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يُشير إلى صورة مسماة.\", applicationMethod: \"يعرض المقيم صورتين على الطفل لأشياء مألوفة (مثال: تفاحة وموزة/ ملعقة وصحن)، ويطلب منه الإشارة إلى صورة أحدها.\" },\n          { id: \"comm-er-6\", itemNumber: \"6\", ageRange: \"18-36 شهرًا\", behavior: \"يجد الأشياء المتطابقة ويوصل بينها.\", applicationMethod: \"يضع المقيم أمام الطفل مكعبا أحمر ومكعبا أخضر ومكعبا أصفر، ثم يعطيه مكعبا أحمر آخر ويطلب منه أن يضعه فوق الذي يشبهه أمامه، وكذلك الأمر بالنسبة لبقية الألوان.\" },\n          { id: \"comm-er-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يجد كتابا محددا عند الطلب ويجلس ليستمع إلى قصة لمدة تتراوح من 8-5 دقيقة.\", applicationMethod: \"يسأل المقيم مقدم الرعاية عما اذا كان لدى الطفل كُتب مناسبة لعمره، واذا كانت الأسرة معتادة على القراءة له وان كان الأمر كذلك يطلب من الطفل احضار كتابه المفضل، ليستمع معه لقصة يسردها مقدم الرعاية عليه (ملاحظة: اذا لم يعتد مقدم الرعاية على القراءة للطفل ولا تتواجد لديه كتب مناسبة نضع إشارة x.\" },\n          { id: \"comm-er-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يُخبر عن الأحداث المتمثلة في الكتب المصورة.\", applicationMethod: \"يستخدم المقيم احدى القصص الموجودة لديه، شريطة أن تكون صورها واضحة ومألوفة للأطفال، ويطلب من الطفل تقديم وصف بسيط لما يشاهده في صفحتين على الأقل من صفحات الكتاب.\" },\n          { id: \"comm-er-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يأخذ دورا في سرد القصص بتكرار الجمل التي تعاد فيها باستمرار أو بإكمال كلمات محددة.\", applicationMethod: \"يسأل المقيم مقدم الرعاية عما اذا كان قد اعتاد سرد القصص على الطفل حتى لو كان من الذاكرة بدون كتاب، ويطلب من الطفل أن يخبره عن أحداث تلك القصة بمساعدة مقدم الرعاية، ويقبل ما اذا سرد الطفل فقط الجمل التي تعاد في القصة باستمرار أو كلمات محددة من القصة (ملاحظة: اذا لم يعتد مقدم الرعاية على سرد القصص للطفل نضع إشارة x.\" },\n          { id: \"comm-er-10\", itemNumber: \"10\", ageRange: \"3-4 سنوات\", behavior: \"يتعرف على بعض الرموز والإشارات في بيئته.\", applicationMethod: \"يستخدم المقيم بطاقات الرموز البيئية (مثال إشارة الحمام الخاص بالرجال والحمام الخاص بالنساء) الموجودة في حقيبته، ويرفع رمز الرجل ورمز المرأة ويسأله أين رمز حمام الرجل؟ ثم أين رمز حمام النساء؟.\" },\n          { id: \"comm-er-11\", itemNumber: \"11\", ageRange: \"3-4 سنوات\", behavior: \"يربط أصوات الحروف المكونة للكلمات بأشكالها المكتوبة.\", applicationMethod: \"يحضر المقيم قلما ورقة من حقيبة التقييم، ويقوم بكتابة حروف العلة مثل (ا) ثم يقرأها أمام الطفل: اااااااااا ثم يطلب منه أن يشير الى حرف الـ (ا) المكتوب، ونكرر ذلك مع أحرف العلة الأخرى (مثال: وووووووووو، أين الـ (و)، ييييييييي، أين الـ (ي)).\" },\n          { id: \"comm-er-12\", itemNumber: \"12\", ageRange: \"4-5 سنوات\", behavior: \"يسرد الأغاني والأنغام من الذاكرة.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يخبره عن أنشودة يحفظها، ثم يطلب من الطفل أن ينشدها له.\" },\n          { id: \"comm-er-13\", itemNumber: \"13\", ageRange: \"4-5 سنوات\", behavior: \"يعيد سرد الأحداث الرئيسية لقصة مألوفة.\", applicationMethod: \"يطلب المقيم من الطفل أن يقص عليه قصة مألوفة وتُعتبر الاستجابة صحيحة في حال ذكر الطفل أبرز أحداث القصة وليس جميعها كما تعتبر صحيحة إن لم يسردها بالتسلسل الصحيح.\" },\n          { id: \"comm-er-14\", itemNumber: \"14\", ageRange: \"4-5 سنوات\", behavior: \"يقرأ 10 أحرف على الأقل بما فيها الأحرف المكونة ل أسمه.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عن 10 أحرف يتقن الطفل قراءتها، ثم يكتبها حرفا تلو الآخر ويطلب من الطفل قراءتها.\" },\n          { id: \"comm-er-15\", itemNumber: \"15\", ageRange: \"4-5 سنوات\", behavior: \"يتعرف على بعض الكلمات المكتوبة.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عن 3 كلمات مكتوبة يتقن الطفل قراءتها صوريا (دون تهجئة)، ثم يكتبها أمام الطفل ويطلب من الطفل قراءتها.\" },\n          { id: \"comm-er-16\", itemNumber: \"16\", ageRange: \"5-6 سنوات\", behavior: \"يحدد اليمين واليسار والأعلى والأسفل والمفاهيم المكانية ذات العلاقة.\", applicationMethod: \"يسأل المقيم الطفل عن يده اليمنى، ومن ثم يده اليسرى، وعينه اليمنى وعينه اليسرى ويطلب منه وضع أداة أعلى الطاولة، ومن ثم أسفلها، ولابد أن يستجيب الطفل لجميع هذه المفاهيم لاعتبار الاستجابة صحيحة.\" },\n          { id: \"comm-er-17\", itemNumber: \"17\", ageRange: \"5-6 سنوات\", behavior: \"يسرد 5-3 أحداث من قصة بالتسلسل الصحيح.\", applicationMethod: \"يطلب المقيم من الطفل أن يقص عليه قصة مألوفة وتُعتبر الاستجابة صحيحة فقط ان ذكر ثلاثة أحداث على الأقل بالتسلسل الصحيح.\" },\n          { id: \"comm-er-18\", itemNumber: \"18\", ageRange: \"5-6 سنوات\", behavior: \"يقرأ قصصا بسيطة.\", applicationMethod: \"يحضر المقيم قصة بسيطة بحيث تتضمن كلمات بسيطة بما لا يزيد عن ثلاث كلمات في الصفحة، ويطلب من الطفل قراءتها (من الممكن أن تكون القراءة تهجئة ولا تكون متقنة، ولكن لا تقبل الاستجابة في حال لم يتمكن الطفل من قراءة %50 من المطلوب منه).\" }\n        ]\n      },\n    ],\n  },\n  {\n    id: \"motor-activities\",\n    name: \"بعد الأنشطة الحركية الهادفة\",\n    subCategories: [\n      {\n        id: \"motor-gross\",\n        name: \"المهارات الحركية الكبيرة\",\n        skills: [\n          { id: \"motor-gross-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يُحرك ذراعية وقدميه بطريقة عشوائية أثناء الاستلقاء على ظهره أو بطنه.\", applicationMethod: \"يُلاحظ المقيم أثناء التقييم ما اذا كان الطفل يُحرك ذراعيه وقدميه بطريقة عشوائية أثناء الاستلقاء على ظهره أو بطنه.\" },\n          { id: \"motor-gross-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يُحرك رأسه من جانب إلى آخر أثناء استلقائه على معدته.\", applicationMethod: \"يُلاحظ المقيم أثناء التقييم ما اذا كان الطفل يُحرك رأسه من جانب إلى آخر أثناء استلقائه على معدته، ويمكنه استخدام خشخيشة بتحريكها إلى جانبه لتحفيزه على ذلك.\" },\n          { id: \"motor-gross-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يحافظ على استقامة رأسه حين يُحمل بشكل عامودي.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية حمل الطفل ويُلاحظ المقيم أثناء التقييم ما اذا كان الطفل يحافظ على استقامة رأسه حين يُحمل بشكل عامودي.\" },\n          { id: \"motor-gross-4\", itemNumber: \"4\", ageRange: \"0-9 أشهر\", behavior: \"يرفع رأسه، والجزء العلوي من جذعه، وذراعيه أثناء الاستلقاء على بطنه (وضعية الطائرة).\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل على بطنه ويلاحظ ما اذا كان يرفع رأسه، والجزء العلوي من جذعه، وذراعيه أثناء الاستلقاء على بطنه (وضعية الطائرة)، ويمكنه استخدام خشخيشة بتحريكها فوق رأسه أثناء الاستلقاء لتحفيزه على ذلك.\" },\n          { id: \"motor-gross-5\", itemNumber: \"5\", ageRange: \"0-9 أشهر\", behavior: \"يتدحرج من جانبه إلى ظهره.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل على جانبه ويلاحظ ما اذا كان يتدحرج من جانبه إلى ظهره، ويمكنه استخدام خشخيشة بتحريكها فوق رأسه أثناء الاستلقاء لتحفيزه على ذلك.\" },\n          { id: \"motor-gross-6\", itemNumber: \"6\", ageRange: \"0-9 أشهر\", behavior: \"يجلس بمساندة.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الجلوس ويُساعده في اسناده بوسائد مناسبة، ويلاحظ ما اذا كان يجلس بمساندة.\" },\n          { id: \"motor-gross-7\", itemNumber: \"7\", ageRange: \"0-9 أشهر\", behavior: \"يُحافظ على وضعية رأسه في المنتصف عندما يُسحب لوضعية الجلوس.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يسحب الطفل من وضعية الاستلقاء على الظهر إلى وضعية الجلوس بمسكه من تحت ابطيه، يلاحظ ما اذا كان الطفل يُحافظ على وضعية رأسه في المنتصف عندما يُسحب لوضعية الجلوس.\" },\n          { id: \"motor-gross-8\", itemNumber: \"8\", ageRange: \"0-9 أشهر\", behavior: \"يقلب من بطنه إلى ظهره وبالعكس بشكل مقصود.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل في وضعية الاستلقاء على بطنه، ويلاحظ ما اذا كان يقلب من بطنه إلى ظهره وبالعكس بشكل مقصود، ويمكنه استخدام خشخيشة بتحريكها إلى جانبه أثناء الاستلقاء لتحفيزه على ذلك.\" },\n          { id: \"motor-gross-9\", itemNumber: \"9\", ageRange: \"0-9 أشهر\", behavior: \"يزحف للأمام على معدته ويتقدم بواسطة ذراعيه.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل في وضعية الاستلقاء على معدته، ويلاحظ ما اذا كان يزحف للأمام على معدته ويتقدم بواسطة ذراعيه، ويمكنه استخدام خشخيشة بتحريكها أمامه لتحفيزه على ذلك.\" },\n          { id: \"motor-gross-10\", itemNumber: \"10\", ageRange: \"9-18 شهرًا\", behavior: \"يحرك جسمه للأمام والخلف بدفع يديه وركبتيه معا.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل في وضعية الاستلقاء على بطنه، ويلاحظ ما اذا كان يحرك جسمه للأمام والخلف بدفع يديه وركبتيه معا كبداية للحبو، ويمكنه استخدام خشخيشة بتحريكها أمامه أثناء الاستلقاء لتحفيزه على ذلك.\" },\n          { id: \"motor-gross-11\", itemNumber: \"11\", ageRange: \"9-18 شهرًا\", behavior: \"يجلس لوحده باعتدال.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الجلوس، ويلاحظ ما اذا كان يجلس لوحده دون اسناد وبشكل معتدل، مع الحرص على توفر سبل الأمان بوجود مقدم الرعاية أمامه لتلقيه اذا ما فقد توازنه.\" },\n          { id: \"motor-gross-12\", itemNumber: \"12\", ageRange: \"9-18 شهرًا\", behavior: \"يسحب نفسه إلى وضعية الوقوف.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الجلوس على الأرض قرب طاولة الوسط، ويلاحظ ما اذا كان يسحب نفسه إلى وضعية الوقوف مستندا على الطاولة، ويمكن الاستعانة بخشخيشة وتحريكها من منتصف الطاولة لتحفيزه على ذلك، مع توفير أقصى سبل الحماية حوله ووجود المقيم خفه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه.\" },\n          { id: \"motor-gross-13\", itemNumber: \"13\", ageRange: \"9-18 شهرًا\", behavior: \"يُبحر (يمشي مستندا على الأثاث).\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف مستندا على الطاولة، ويلاحظ ما اذا كان يمشي مستندا على الأثاث، ويمكن الاستعانة بخشخيشة وتحريكها أمامه لتحفيزه على ذلك، مع توفير أقصى سبل الحماية حوله ووجود المقيم ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه.\" },\n          { id: \"motor-gross-14\", itemNumber: \"14\", ageRange: \"9-18 شهرًا\", behavior: \"يقف لوحده.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف وملاحظة ما اذا كان يقف لوحده، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه.\" },\n          { id: \"motor-gross-15\", itemNumber: \"15\", ageRange: \"9-18 شهرًا\", behavior: \"يمشي باستقلالية.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف وملاحظة ما اذا كان يمشي لوحده، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه.\" },\n          { id: \"motor-gross-16\", itemNumber: \"16\", ageRange: \"18-36 شهرًا\", behavior: \"يرمي كرة من وضعية الوقوف أو الجلوس نحو الاتجاه المرغوب.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف (أو الجلوس إن لم يتمكن من الوقوف بعد) ويحضر المقيم كرة ويطلب من مقدم الرعاية أن يعطيها للطفل ثم يطلب منه أن يرميها باتجاهه.\" },\n          { id: \"motor-gross-17\", itemNumber: \"17\", ageRange: \"18-36 شهرًا\", behavior: \"يتسلق على كرسي الكبار، يدير نفسه ويجلس.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية احضار لعبة أو أداة مفضلة للطفل ويضعها على كرسي الكبار الموجود في غرفة التقييم، ويلاحظ ما اذا كان الطفل يتسلق الكرسي للوصول إلى اللعبة ثم يدير نفسه ويجلس عليه ليلعب باللعبة (من الضروري مراعاة سبل الأمان والتأكد بأن الكرسي ثابت وغير متحرك بصورة قد تيسر سقوط الطفل منه).\" },\n          { id: \"motor-gross-18\", itemNumber: \"18\", ageRange: \"18-36 شهرًا\", behavior: \"ينحني أو يقرفص ليلتقط لعبة من الأرض دون أن يسقط.\", applicationMethod: \"يضع المقيم خشخيشة على الأرض ويحركها من مكان منخفض، ثم يطلب من الطفل تناولها، ويُلاحظ ما اذا كان ينحني أو يقرفص ليلتقطها من الأرض دون أن يسقط (يحرص المقيم على تعقيم أي أداة توضع على الأرض بعد انتهاء التقييم).\" },\n          { id: \"motor-gross-19\", itemNumber: \"19\", ageRange: \"18-36 شهرًا\", behavior: \"يصعد الدرج بوضع قدمين على كل درجة.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية اصطحابه الى درج آمن، ويلاحظ ما إذا كان الطفل يصعد الدرج بوضع قدمين على كل درجة مستندا على الحائط أو الدرابزين، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه. في حال لم يكن هناك درج في البيت وأخبر مقدم الرعاية المقيم بأن الطفل لم يتعرض لهذه الخبرة مسبقا، توضع إشارة خطأ، أما إن أخبره بأنه يصعد الدرج بوضع قدمين على كل درجة في بيت جده أو غيره من الأماكن تُقبل الإجابة.\" },\n          { id: \"motor-gross-20\", itemNumber: \"20\", ageRange: \"18-36 شهرًا\", behavior: \"يقفز في مكانه برفع كلتا قدميه معا.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يساعده في الطلب من الطفل القفز في مكانه برفع كلتا قدميه، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه.\" },\n          { id: \"motor-gross-21\", itemNumber: \"21\", ageRange: \"18-36 شهرًا\", behavior: \"يصعد الدرج ويهبط منه واضعا قدم واحدة على كل درجة أثناء الإمساك بالدرابزين أو بمساعدة من الكبير.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية اصطحابه الى درج آمن، ويلاحظ ما إذا كان الطفل يصعد الدرج ويهبط منه واضعا قدم واحدة على كل درجة أثناء الإمساك بالدرابزين أو بمساعدة مقدم الرعاية، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه. في حال لم يكن هناك درج في البيت وأخبر مقدم الرعاية المقيم بأن الطفل لم يتعرض لهذه الخبرة مسبقا، توضع إشارة خطأ.\" },\n          { id: \"motor-gross-22\", itemNumber: \"22\", ageRange: \"3-4 سنوات\", behavior: \"يقود الدراجة أو العربات ذات العجلات مستخدما كلتا قدميه في تحريكها.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية ما اذا كان لدى الطفل دراجة أو عربة ذات عجلات، ويصطحبه الى مكان اللعب عليه ليلاحظ ما اذا كان الطفل يقود الدراجة أو العربات ذات العجلات مستخدما كلتا قدميه في تحريكها (دون استخدام البدالات) مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه وفي حال أخبر مقدم الرعاية المقيم بأن الطفل يمارس ذلك في الحضانة أو بيت جده أو أي مكان آخر وبتوازن، تقبل الإجابة أما اذا أخبر المقيم بأنه لم يمر بهذه الخبرة نهائيا توضع إشارة x.\" },\n          { id: \"motor-gross-23\", itemNumber: \"23\", ageRange: \"3-4 سنوات\", behavior: \"يتسلق درجتين على الأقل من هيكل التسلق.\", applicationMethod: \"في حال كان التقييم يتم في مركز التعلم المبكر يلاحظ المقيم بنفسه ما اذا كان الطفل يتسلق درجتين على الأقل من هيكل التسلق مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه، أما اذا كان الطفل في البيت وأخبر مقدم الرعاية المقيم بأن الطفل لم يتعرض لهذه الخبرة مسبقا، توضع إشارة خطأ أما إن أخبره بأنه يفعلها في المنتزه أو الحديقة العامة أو غيرها من الأماكن تُقبل الإجابة.\" },\n          { id: \"motor-gross-24\", itemNumber: \"24\", ageRange: \"3-4 سنوات\", behavior: \"يقفز فوق الأشياء أو عن صندوق أو من أعلى درجة ترتفع عن الأرض.\", applicationMethod: \"اذا كان هناك درج في البيت يتم الاستفادة منه، وان لم يكن هناك درج نضع قطعة كبيرة من المكعبات على الأرض ونطلب من الطفل القفز فوقها ثلاث مرات، مع الحرص على تعقيمها عند العودة الى المركز.\" },\n          { id: \"motor-gross-25\", itemNumber: \"25\", ageRange: \"3-4 سنوات\", behavior: \"يركض بخطوات واسعة وبسهولة مُحركا ذراعيه بتناسق.\", applicationMethod: \"ينسق المقيم مع مقدم الرعاية لملاحظة الطفل أثناء الركض، وذلك بتنفيذ نشاط عادة ما يحفزه على ذلك في مكان واسع وآمن ومن ثم يلاحظ ما إذا كان يركض بخطوات واسعة وبسهولة مُحركا ذراعيه بتناسق. في حال لم يتوفر مكان واسع وآمن، وأخبر مقدم الرعاية المقيم بأن الطفل يمارس ذلك في الحضانة أو بيت جده أو أي مكان آخر وبتوازن، تقبل الإجابة أما اذا أخبر المقيم بأنه لم يمر بهذه الخبرة نهائيا توضع إشارة x.\" },\n          { id: \"motor-gross-26\", itemNumber: \"26\", ageRange: \"3-4 سنوات\", behavior: \"يمد كلتا ذراعيه ليلتقط بيديه كرة مقذوفة نحوه.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يقف أمام الطفل ويرمي الكرة باتجاهه، ويلاحظ ما إذا كان الطفل يلتقط الكرة المقذوفة نحوه (من الممكن إعطاء الطفل ثلاث فرص، وملاحظة ما إذا كان يلتقط الكرة في احداها).\" },\n          { id: \"motor-gross-27\", itemNumber: \"27\", ageRange: \"4-5 سنوات\", behavior: \"يعدو بسرعة.\", applicationMethod: \"اذ كان التقييم يتم في بيئة التعلم المبكر يلاحظ ما اذا كان الطفل يعدو (العدو هو المشي السريع) وان لم يكن كذلك يمشي المقيم بسرعة في مكان آمن ويطلب من الطفل المشي مثله.\" },\n          { id: \"motor-gross-28\", itemNumber: \"28\", ageRange: \"4-5 سنوات\", behavior: \"يلعب ألعابا تتضمن القفز وركل الكرة.\", applicationMethod: \"يطلب المقيم من الطفل أن يقفز من مكان إلى آخر كالأرنب (3 قفزات على الأقل) ومن ثم يلتقط كرة موضوعة على الأرض ليرميها باتجاه المقيم.\" },\n          { id: \"motor-gross-29\", itemNumber: \"29\", ageRange: \"4-5 سنوات\", behavior: \"يُحافظ على توازنه أثناء المشي على سطح ضيق أو على خشبة التوازن.\", applicationMethod: \"يضع المقيم حبلا على أرضية الغرفة ويمشي أمام الطفل على خط مستقيم دون أن يتجاوز الحبل الموضوع على الأرض، ومن ثم يطلب من الطفل أن يمشي مثله فوق الحبل.\" },\n          { id: \"motor-gross-30\", itemNumber: \"30\", ageRange: \"4-5 سنوات\", behavior: \"يرمي كرة صغيرة بيد واحدة لشخص آخر ويلتقطها عند قذفها باتجاهه مستخدما كلتا يديه.\", applicationMethod: \"يستعين المقيم بكرة صغيرة من حقيبة التقييم ويرميها باتجاه الطفل، ويلاحظ ما اذا كان يلتقطها بكلتا يديه وقد يضمها أيضا إلى صدره، ثم يطلب من الطفل أن يرميها باتجاهه.\" },\n          { id: \"motor-gross-31\", itemNumber: \"31\", ageRange: \"5-6 سنوات\", behavior: \"يُظهر مرونة وقدرة على الاحتمال أثناء لعبه في الساحة الخارجية.\", applicationMethod: \"يستفسر من مقدم الرعاية عما اذا كان الطفل يحب اللعب بالألعاب التي تتطلب مرونة في مناطق اللعب العامة مثل ألعاب التسلق، والركض ويتحمل ممارسة هذه الألعاب لفترة مناسبة.\" },\n          { id: \"motor-gross-32\", itemNumber: \"32\", ageRange: \"5-6 سنوات\", behavior: \"يثب بتبادل القدمين ليقفز عن الحبل.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يمسك بطرف الحبل بينما يمسك هو بالطرف الآخر ويرفعاه عن الأرض 15 سم، ثم يطلب من الطفل القفز عنه، مع الحرص على أن تكون منطقة التقييم خالية من أية عقبات أو معيقات أو عوامل خطورة.\" },\n          { id: \"motor-gross-33\", itemNumber: \"33\", ageRange: \"5-6 سنوات\", behavior: \"يلعب بالكرة بمهارة عالية تتضمن التصويب والالتقاط والتربيت عليها.\", applicationMethod: \"يستفسر من مقدم الرعاية عما اذا كان الطفل يمارس عادة اللعب بالكرة (ليس مجرد التقاط وتصويب)، وانما أيضا التربيت عليها بمهارة عالية.\" }\n        ]\n      },\n      {\n        id: \"motor-fine-writing\",\n        name: \"المهارات الحركية الصغيرة والكتابة المبكرة\",\n        skills: [\n          { id: \"motor-fine-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يمسك شيئا وضع بيده لفترة محددة ويتركه عن دون قصد.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل بيده ويلاحظ ما اذا كان يمسكها بيده لفترة دقيقة وقد يتركه عن دون قصد بعد ذلك، (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به).\" },\n          { id: \"motor-fine-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يحرك كلتا ذراعيه باتجاه لعبة تتحرك في خط الوسط.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل بمستوى نظر الطفل في خط الوسط ويلاحظ ما اذا كان يحرك كلتا ذراعيه باتجاهها، (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به).\" },\n          { id: \"motor-fine-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"ينقل لعبة من يد ل أخرى.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل بيده ويلاحظ ما اذا كان ينقلها من يد لأخرى (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به).\" },\n          { id: \"motor-fine-4\", itemNumber: \"4\", ageRange: \"0-9 أشهر\", behavior: \"يطعم نفسه بيديه.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يتناول أطعمة بسيطة كالبسكويت بيده، وان لاحظ ذلك أثناء تواجده يأخذه بعين الاعتبار دون سؤال الأسرة.\" },\n          { id: \"motor-fine-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يستخدم يديه وعينيه معا بتآزر (مثال: تناول الطعام بالأصابع، أو إدخال زر كبير موجود في دميته داخل العروة المخصصة له).\", applicationMethod: \"يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل أمامه ويلاحظ ما اذا كان يستخدم يديه وعينيه معا بتآزر لالتقاطها من أمامه (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به).\" },\n          { id: \"motor-fine-6\", itemNumber: \"6\", ageRange: \"9-18 شهرًا\", behavior: \"يمسك الأقلام في البداية للخربشة.\", applicationMethod: \"يحضر المقيم ورقة وقلم مناسبين من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل ويضع قلم الفلومستر أمامه ويلاحظ ما اذا بدأ يمسكه للخربشة.\" },\n          { id: \"motor-fine-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يلعب بالأشياء مستخدما كلتا يديه بتناسق.\", applicationMethod: \"يحضر المقيم مجموعة مكعبات من حقيبة التقييم، ويضعها على الطاولة أمام الطفل ويلاحظ ما اذا كان الطفل يلعب بها مستخدمات كلتا يديه بتناسق.\" },\n          { id: \"motor-fine-8\", itemNumber: \"8\", ageRange: \"18-36 شهرًا\", behavior: \"يؤدي حركات بأصابعه تتناسب مع كلمات الأغاني.\", applicationMethod: \"يسأل المقيم مقدم الرعاية عما اذا كان الطفل يؤدي بعض الحركات لأغنية مألوفة (مثال: هيك بيطيروا العصافير)، واذا أفاد بالإيجاب يطلب المقيم من مقدم الرعاية أن يغنيها له ويطلب من الطفل المشاركة في الحركات.\" },\n          { id: \"motor-fine-9\", itemNumber: \"9\", ageRange: \"18-36 شهرًا\", behavior: \"يرسم دوائر وخطوطا عامودية وأفقية.\", applicationMethod: \"يحضر المقيم ورقة وقلم مناسبين من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل ويضع قلم الفلومستر أمامه ويلاحظ ما اذا بدأ برسم خطوط عامودية وأفقية ودوائر (لا يشترط معرفة الطفل لاسمها، ولكن يمكن أن يرسمها المقيم أو مقدم الرعاية أمامه وينتظر من الطفل أن يقلدها حتى لو لم تكن الدوائر مغلقة أو الخطوط مستقيمة بشكل كامل تقبل الاستجابة، ولا تقبل ان كانت الخطوط عشوائية).\" },\n          { id: \"motor-fine-10\", itemNumber: \"10\", ageRange: \"3-4 سنوات\", behavior: \"يستخدم المقص في قص الورقة إلى قسمين.\", applicationMethod: \"يحضر المقيم ورقة ومقص آمن ومناسب من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل ويطلب من الطفل أن يمسك المقص ويقص الورقة، ويشترط الدقة في مسك المقص وفتحه واغلاقه أثناء قص الورقة لصحة الإجابة.\" },\n          { id: \"motor-fine-11\", itemNumber: \"11\", ageRange: \"3-4 سنوات\", behavior: \"يرسم المربع مقلدا.\", applicationMethod: \"يحضر المقيم ورقة وقلم مناسبين من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل وفوقها قلم الفلومستر أمامه ثم يرسم المقيم أو مقدم الرعاية أمامه مربع ويطلب من الطفل أن يقلده، ويشترط الدقة في الرسم لصحة الإجابة.\" },\n          { id: \"motor-fine-12\", itemNumber: \"12\", ageRange: \"4-5 سنوات\", behavior: \"يقص ويلصق أشكالا بسيطة.\", applicationMethod: \"يحضر مقصا وورقة، ويرسم على الورقة شكل مثلث وشكل مربع وشكل دائرة، ويطلب من الطفل قصها ومن ثم الصاقها على ورقة أخرى.\" },\n          { id: \"motor-fine-13\", itemNumber: \"13\", ageRange: \"4-5 سنوات\", behavior: \"ينسخ كلمات بسيطة.\", applicationMethod: \"يكتب المقيم على الورقة كلمات بسيطة تتضمن حرف علة (مثال: باب، توت، نور، بيت، بوت) ويلاحظ ما اذا كان الطفل ينسخ 3 كلمات على الأقل منها.\" },\n          { id: \"motor-fine-14\", itemNumber: \"14\", ageRange: \"5-6 سنوات\", behavior: \"يستخدم كلتا يديه في أداء مهمة معقدة.\", applicationMethod: \"يحضر مقدم الرعاية رباطا (أو شريطا قماشيا)، ويطلب من الطفل عقده ومن ثم فكه.\" }\n        ]\n      },\n      {\n        id: \"motor-senses\",\n        name: \"الحواس\",\n        skills: [\n          { id: \"motor-senses-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يُصدر بعض الأصوات، ويُظهر تعبيرات وجهية أو يُحرك جسده كاستجابة للتغيرات الصوتية.\", applicationMethod: \"نطلب من مقدم الرعاية أن يتحدث للطفل بلطف، ومن ثم بنبرة جادة ونلاحظ مستوى تغير تعبيرات الطفل الوجهية أو الجسدية كاستجابة لهذا التغير في نبرة صوت مقدم الرعاية.\" },\n          { id: \"motor-senses-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يسمح بحمله عن قرب لتغيير وضعية الجلوس، أو بحثا عن الراحة أو لتناول الطعام.\", applicationMethod: \"يلاحظ المقيم ما اذا كان الطفل يتقبل ضم مقدم الرعاية له وحمله أو يتذمر من حمله، وان تذمر يستفسر من مقدم الرعاية ما اذا كان هذا الأمر مستمر أو أنه طارئ بسبب مرض أو تعب.\" },\n          { id: \"motor-senses-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يستكشف الأشياء بواسطة فمه.\", applicationMethod: \"يلاحظ المقيم عند وضع مكعب بيد الطفل ما اذا كان يضعه في فمه مُحاولة منه لاستكشافه (يحرص المقيم على ابعاد المكعب فور تقريبه من فمه، وتعقيمه عند العودة إلى مكان العمل).\" },\n          { id: \"motor-senses-4\", itemNumber: \"4\", ageRange: \"0-9 أشهر\", behavior: \"يتحمل الإضاءة الداخلية التقليدية دون أن يغلق عينيه أو دون أن تنحرفا.\", applicationMethod: \"يلاحظ الميسر ذلك بشكل مباشر ان كان الطفل يجلس بوجود الإضاءة الداخلية التقليدية دون تذمر أو انحراف للعينين، وان كانت الغرفة معتمة قليلا يطلب من مقدم الرعاية اشعاله ويلاحظ ردة فعل الطفل نحو ذلك.\" },\n          { id: \"motor-senses-5\", itemNumber: \"5\", ageRange: \"0-9 أشهر\", behavior: \"يستجيب الطفل لتغيير وضعيته عند تحريك جسمه (مثال: يتحرك للأمام ليتم إنزاله من كرسي مرتفع).\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يمد يده أمام الطفل كمؤشر عن رغبة مقدم الرعاية في حمل الطفل، ويلاحظ ردة الفعل نحو ذلك بتحرك الطفل مثلا للأمام كي يحمله.\" },\n          { id: \"motor-senses-6\", itemNumber: \"6\", ageRange: \"9-18 شهرًا\", behavior: \"ينقل انتباهه البصري من مثير إلى آخر.\", applicationMethod: \"يضع المقيم احدى دمى الأصابع على سبابته اليمنى، ثم دمية مختلفة على سبابته اليسرى، ومن ثم يحرك اليمنى أولا وبعد أن يلتفت الطفل لها يحضر اليسرى بجانبها وقرب خط منتصف الجسم، ويلاحظ ما اذا نقل الطفل انتباهه لها.\" },\n          { id: \"motor-senses-7\", itemNumber: \"7\", ageRange: \"9-18 شهرًا\", behavior: \"يسمح للأشياء والملامس بأن تلمس منطقة فمه ولسانه وخده.\", applicationMethod: \"يطلب المقيم من مقدم الرعاية أن يمرر ابهامه حول منطقة الفم والخد، ويلاحظ ما اذا كان الطفل يسمح له بذلك دون أن يظهر أي ردود فعل دفاعية مقاومة لذلك.\" },\n          { id: \"motor-senses-8\", itemNumber: \"8\", ageRange: \"9-18 شهرًا\", behavior: \"يربط المعلومات الحسية بالأنشطة الروتينية اليومية.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يظهر ردود أفعال تجاه المثيرات الحسية التمييزية والمرتبطة ببعض الأعمال الروتينية (مثال: يظهر استعدادا للنوم عند إطفاء الضوء، أو يبدأ بتحريك فمه عند سماء صوت رج زجاجة الرضاعة) هذا السؤال مرتبط بسؤال 6 (تنظيم ذاتي- نفس المرحلة العمرية)، و 12 (تنظيم ذاتي- مرحلة عمرية أكثر تقدما).\" },\n          { id: \"motor-senses-9\", itemNumber: \"9\", ageRange: \"18-36 شهرًا\", behavior: \"يُمارس أنشطة التحمل كالأرجحة والدوران حول النفس وغيرها من الأنشطة التي تُنفذ بعكس الجاذبية.\", applicationMethod: \"إذا كان التقييم يتم في مركز التعلم المبكر، من المناسب أن يتم اصطحاب الطفل إلى المكان المخصص للمهارات الحركية الكبيرة، وملاحظة ما إذا كان يُمارس أنشطة التحمل كالأرجحة والدوران حول النفس وغيرها من الأنشطة التي تُنفذ بعكس الجاذبية، وان لم يكن كل ذلك متاحا من الممكن سؤال الأسرة عن ذلك.\" },\n          { id: \"motor-senses-10\", itemNumber: \"10\", ageRange: \"18-36 شهرًا\", behavior: \"يستمع للأصوات والموسيقى الصادرة بصوت متوسط دون إغلاق أُذنيه أو التذمر منها.\", applicationMethod: \"يُشغل الميسر أنشودة أطفال من هاتفه المحول، ويلاحظ ما اذا كان الطفل يستمع للأصوات والموسيقى الصادرة بصوت متوسط دون أن يظهر ردود فعل دفاعية مثل إغلاق أُذنيه أو التذمر منها.\" },\n          { id: \"motor-senses-11\", itemNumber: \"11\", ageRange: \"18-36 شهرًا\", behavior: \"يأكل أطعمة مختلفة القوام والنكهة.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يأكل أطعمة مختلفة القوام والنكهة مثل جبنة الكيري، والحلاوة الطحينية، وغيرها من المواد لزجة القوام والتي قد تُثير ردود فعل دفاعية لدى بعض الأطفال.\" },\n          { id: \"motor-senses-12\", itemNumber: \"12\", ageRange: \"18-36 شهرًا\", behavior: \"يلعب في الأماكن المغلقة (مثال: القنوات المغلقة، والصناديق).\", applicationMethod: \"اذا كان التقييم يتم في مركز التعلم المبكر، من المناسب أن يتم اصطحاب الطفل إلى المكان المخصص للمهارات الحركية الكبيرة، وملاحظة ما اذا كان يلعب في الأماكن المغلقة مثل القنوات والصناديق، وان لم يتوفر ذلك من المناسب الجلوس مع الطفل على الأرض، وتخصيص مكان جلوسه تحت الطاولة (مع مراعاة متطلبات السلامة)، وملاحظة مستوى استجابته لوجوده في ذلك المكان، وان لم يكن كل ذلك متاحا من الممكن سؤال الأسرة عن ذلك.\" },\n          { id: \"motor-senses-13\", itemNumber: \"13\", ageRange: \"3-4 سنوات\", behavior: \"يتحمل قوام وملامس الأدوات والأسطح المختلفة (الغراء، ألوان الأصابع، الرمل) عند استخدامها في الأنشطة.\", applicationMethod: \"نضع قليل من الصمغ السائل على وسط ورقة، ونطلب من الطفل تحريكها بأصبعه بشكل دائري، ونلاحظ ما اذا كان الطفل يتحمل قوامها أو يظهر ردود فعل تعكس عدم تحمله لملامسته، ومن المفضل إن كانت هناك مساحة من المكان الذي يتم فيه التقييم مخصصة للتلوين بألوان الأصابع أو للعب بالرمل يمكن أن يتم تنفيذ النشاط فيها.\" },\n          { id: \"motor-senses-14\", itemNumber: \"14\", ageRange: \"3-4 سنوات\", behavior: \"يتحمل المدخلات الحسية خلال أنشطة العناية الذاتية (تنظيف الأسنان، تصفيف الشعر، غسل الوجه واليدين).\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل لا يتذمر أو يظهر عدم تحمل لوضع معجون الأسنان والفرشاة داخل فمه، أو ملامسة الفرشاة أو المشط (دون ألم) لشعره، أو من ملامس الصابون عند غسل اليدين والوجه.\" },\n          { id: \"motor-senses-15\", itemNumber: \"15\", ageRange: \"4-5 سنوات\", behavior: \"يُنظم جسده أثناء اللعب (يتحرك بشكل عامودي أو أفقي في الفراغ بتوازن).\", applicationMethod: \"يلاحظ المقيم ما إذا كان الطفل يتحرك بتوازن ودون أن يرتطم بالأشياء أثناء التنقل والحركة، ويطلب منه القفز في مكانه، ويلاحظ ما اذا كان يقوم بذلك بشكل صحيح ومتوازن.\" },\n          { id: \"motor-senses-16\", itemNumber: \"16\", ageRange: \"4-5 سنوات\", behavior: \"يتخطى العقبات الموضوعة أمامه بنجاح.\", applicationMethod: \"يضع المقيم خمس مكعبات متفرقة على الأرض، ويطلب من الطفل أن يحضر إليه من بداية الغرفة مع مراعاة تخطي العقبات الموضوعة تارة بالقفز تارة بالمشي إلى اليمين أو اليسار. (على المقيم أن يراعي تعقيم أية أدوات توضع على الأرض بعد انتهاء التقييم، وخلو المنطقة من أي عقبات أو مصادر خطر والتأهب لتلقي الطفل عند أي تعثر محتمل).\" },\n          { id: \"motor-senses-17\", itemNumber: \"17\", ageRange: \"5-6 سنوات\", behavior: \"ينظم المعلومات الحسية بشكل مناسب (يختار الملابس المناسبة للطقس، ويضبط درجة حرارة الماء عند غسل اليدين أو الاستحمام).\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل في العادة يختار ملابس مناسبة للطقس (مثال: لا يطلب ارتداء ملابس خفيفة في الطقس البارد)، وبأن لديه القدرة على ضبط حرارة الماء عند غسل اليدين أو الاستحمام.\" },\n          { id: \"motor-senses-18\", itemNumber: \"18\", ageRange: \"5-6 سنوات\", behavior: \"يمُرُ بخبرات مع وسائل الإعلام المختلفة (الحاسوب، التلفاز، أجهزة التسجيل، الخ).\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يقضي وقت الشاشة في أنشطة تفاعلية متنوعة لا تقتصر فقط على التلقي السلبي للمعلومة، وقد يتضمن ذلك استخدام الحاسوب والتلفاز والتطبيقات الذكية على ألا يزيد ذلك عن ساعة واحدة يوميا.\" }\n        ]\n      },\n      {\n        id: \"motor-self-regulation\",\n        name: \"التنظيم الذاتي\",\n        skills: [\n          { id: \"motor-selfreg-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يُظهر أشكالا متنوعة من الأوضاع الفسيولوجية (النوم بعمق، النوم مع وجود الضوء، التنبيه الخفيف، التنبيه الفعال، البكاء، النعاس).\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يتكيف بالنوم في أوضاع مختلفة عند نعاسه ويحتاج إلى القليل من التهيئة للنوم عند النعاس حتى وان كان هناك ضوء أو كانت الأسرة خارج المنزل، وان لاحظ المقيم بأن الطفل نام أثناء التقييم لا داعي للسؤال.\" },\n          { id: \"motor-selfreg-2\", itemNumber: \"2\", ageRange: \"0-9 أشهر\", behavior: \"يبدأ بالانتظار قليلا حتى تلبى حاجاته.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل ينتظر قليلا ويعبر بأصوات بسيطة قبل أن تُلبى احتياجاته، وقد يظهر أصوات بكاء أولية تعكس حاجاته (مثال: نييييه للجوع، وأوووه عند النعاس، وإيه عند التعب)، وان لاحظ المقيم ذلك بنفسه أثناء التقييم لا داعي للسؤال.\" },\n          { id: \"motor-selfreg-3\", itemNumber: \"3\", ageRange: \"0-9 أشهر\", behavior: \"يستخدم أساليب التهدئة الذاتية (مثال: يمص إصبعه، يضع اللهاية في فمه).\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يُهدئ نفسه بنفسه كأن يقرب ملابسه من فمه لتصبير نفسه على الجوع، أو مص أصبعه أو تقريب اللهاية من فمه، وان لاحظ المقيم ذلك بنفسه أثناء التقييم لا داعي للسؤال.\" },\n          { id: \"motor-selfreg-4\", itemNumber: \"4\", ageRange: \"9-18 شهرًا\", behavior: \"لديه حركة أمعاء طبيعية.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كانت حركة أمعاء الطفل طبيعية (مثال: لا يشكو من أي مشكلات في الإخراج كالإمساك أو الإسهال).\" },\n          { id: \"motor-selfreg-5\", itemNumber: \"5\", ageRange: \"9-18 شهرًا\", behavior: \"يوقف النشاط أو يتردد عن إتمامه حين يقال له (لا).\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يتوقف عن تقريب يده من أي خطر محتمل (مثال: فتح واغلاق مقابس الكهرباء) بعد أن ينبهه شخص كبير بصوت حازم وجاد قائلا له (لا)، وان لاحظ المقيم ذلك بنفسه أثناء التقييم لا داعي للسؤال.\" },\n          { id: \"motor-selfreg-6\", itemNumber: \"6\", ageRange: \"9-18 شهرًا\", behavior: \"يتبع نماذج قابلة للتوقع في وقت النوم أو وقت تناول الطعام.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل لديه نماذج قابلة للتوقع للنوم أو وقت تناول الطعام (مثال: يظهر استعدادا للنوم عند إطفاء الضوء، أو يبدأ بتحريك فمه عند سماء صوت رج زجاجة الرضاعة) هذا السؤال مرتبط بسؤال رقم 12 من نفس البعد (مستوى أعلى)، وسؤال 9 حواس (نفس الفئة العمرية).\" },\n          { id: \"motor-selfreg-7\", itemNumber: \"7\", ageRange: \"9-18 شهرًا\", behavior: \"يقبل التغيير في الروتين اليومي أو البيئة دون انزعاج واضح.\", applicationMethod: \"يلاحظ المقيم ما إذا كان وجوده في بيئة الطفل للتقييم قد أربك الطفل وأزعجه بشكل واضح وغير اعتيادي، وإن حدث ذلك يتأكد من مقدم الرعاية عما إذا كان هذا الانزعاج دائم أم أنه حدث بسبب وجود المقيم أو التغيير في المكان والروتين؟.\" },\n          { id: \"motor-selfreg-8\", itemNumber: \"8\", ageRange: \"9-18 شهرًا\", behavior: \"يعرض مستوى صوتي وطبقة صوتية مشابهة للأطفال الآخرين أو لبعض أفراد الأسرة.\", applicationMethod: \"يلاحظ المقيم ما إذا كان الطفل عند تفاعله داخل بيئة التقييم (بيئته المألوفة) يتحدث بنبرة صوت مشابهة لحديث الأطفال الآخرين من حوله أو بعض أفراد أسرته.\" },\n          { id: \"motor-selfreg-9\", itemNumber: \"9\", ageRange: \"18-36 شهرًا\", behavior: \"يتنقل بسهولة بين الأنشطة بتوجيه من الكبار.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يقبل بسهولة الانتقال بين الأنشطة سواء في المنزل أو مركز التعلم المبكر، وهنا من الممكن أن يكون التنقل بين الأنشطة بتوجيه من الكبار أو تذكير منهم.\" },\n          { id: \"motor-selfreg-10\", itemNumber: \"10\", ageRange: \"18-36 شهرًا\", behavior: \"يُشارك في نشاط لمدة تتراوح من 15-10 دقيقة دون مساعدة من الكبار.\", applicationMethod: \"يستفسر من مقدم الرعاية ما اذا كان الطفل يُشارك في نشاط لمدة تتراوح من 15-10 دقيقة دون مساعدة من الكبار (قد يكون نشاط لعب مثل بناء المكعبات)- هذا السؤال مرتبط بسؤال (5) تطور اللعب الاجتماعي.\" },\n          { id: \"motor-selfreg-11\", itemNumber: \"11\", ageRange: \"18-36 شهرًا\", behavior: \"ينتظر لوقت قصير حتى تُلبى احتياجاته.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يراعي مشاغل مقدم الرعاية وينتظر لوقت قصير حتى تُلبى احتياجاته دون أن يبكي مطالبا بتلبية حاجته فورا.\" },\n          { id: \"motor-selfreg-12\", itemNumber: \"12\", ageRange: \"3-4 سنوات\", behavior: \"لديه أنماط نوم وطعام روتينية قابلة للتوقع.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل لديه أنماط نوم وطعام روتينية قابلة للتوقع (مثال: يبادر من نفسه بتنظيف أسنانه والذهاب إلى الحمام وغسل اليدين قبل النوم)، هذا السؤال مرتبط بسؤال 6 من نفس البعد وسؤال 9 حواس (كلا السؤالين من فئة عمرية أقل).\" },\n          { id: \"motor-selfreg-13\", itemNumber: \"13\", ageRange: \"3-4 سنوات\", behavior: \"ينام نوما عميقا بمساعدة قليلة.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل ينام نوما عميقا بمساعدة قليلة تتعلق بتهيئته للنوم من خلال قراءة قصة قصيرة، ولا يستيقظ إلا مرة على الأكثر لقضاء حاجة كالذهاب إلى الحمام أو شرب الماء.\" },\n          { id: \"motor-selfreg-14\", itemNumber: \"14\", ageRange: \"4-5 سنوات\", behavior: \"يستخدم كميات مناسبة من الأشياء الموضوعة أمامه (يصب كمية مناسبة من الحليب، يوزع كمية مناسبة من الصمغ السائل).\", applicationMethod: \"يحضر المقيم صمغا سائلا وورقة من حقيبة التقييم ويطلب من الطفل استخدامه للصق ورقة شقها منه، ويلاحظ ما اذا كان الطفل يوزع كمية مناسبة من الصمغ السائل على الورقة.\" },\n          { id: \"motor-selfreg-15\", itemNumber: \"15\", ageRange: \"4-5 سنوات\", behavior: \"ينتقل بين الأنشطة بسهولة.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يقبل بسهولة الانتقال بين الأنشطة سواء في المنزل أو مركز التعلم المبكر، وهنا لابُد أن يكون التنقل بين الأنشطة دون توجيه من الكبار أو تذكير منهم.\" },\n          { id: \"motor-selfreg-16\", itemNumber: \"16\", ageRange: \"5-6 سنوات\", behavior: \"يُغير مستوى نشاطه بشكل يتناسب مع الموقف.\", applicationMethod: \"يلعب المقيم مع الطفل لعبة حركة وصنم، (يستعين بأنشودة من على الهاتف الذكي، ويشرح قواعد اللعبة للطفل بحيث يتوقع منه أن يتحرك عند تشغيلها ويتوقف تماما عن الحركة عند وقفها).\" },\n          { id: \"motor-selfreg-17\", itemNumber: \"17\", ageRange: \"5-6 سنوات\", behavior: \"يستمر في النشاط لفترة زمنية طويلة.\", applicationMethod: \"يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يستمر في نشاط ما لفترة زمنية طويلة (مثال: حل الواجبات، القراءة،..الخ) - يستثنى من ذلك الأنشطة التي يكون فيها الطفل متلق سلبي مثل عروض الفيديو أو التلفاز.\" }\n        ]\n      },\n      {\n        id: \"motor-independence\",\n        name: \"الاستقلالية\",\n        skills: [\n          { id: \"motor-ind-1\", itemNumber: \"1\", ageRange: \"0-9 أشهر\", behavior: \"يصبح أكثر نشاطا عند مشاهدة زجاجة الرضاعة أو الثدي\", applicationMethod: \"الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ ما اذا كان يصبح أكثر نشاطا عند مشاهدة زجاجة الرضاعة أو نستفسر من الأم عن ذلك ان كان يرضع رضاعة طبيعية.\" },\n          { id: \"motor-ind-2\", itemNumber: \"2\", ageRange: \"9-18 شهرًا\", behavior: \"يتعاون في ارتداء ملابسه\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يتعاون معه بمد يديه إلى الأكمام مثلا عندما يبدأ مقدم الرعاية بإلباسه ملابسه.\" },\n          { id: \"motor-ind-3\", itemNumber: \"3\", ageRange: \"18-36 شهرًا\", behavior: \"يُشير إلى حاجته للحمام خلال اليوم\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يعبر بالإشارة أو بكلمات بسيطة عن حاجته للحمام خلال اليوم (مثل: بيبي، نونو، مام).\" },\n          { id: \"motor-ind-4\", itemNumber: \"4\", ageRange: \"18-36 شهرًا\", behavior: \"يُنظف أسنانه بمساعدة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يحرك فرشاة الأسنان داخل فمه في محاولة منه لتنظيف أسنانه بعد أن يكون شخص كبير قد أعدها له بوضع المعجون والماء عليها.\" },\n          { id: \"motor-ind-5\", itemNumber: \"5\", ageRange: \"18-36 شهرًا\", behavior: \"يغسل ويجفف يديه بمساعدة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يغسل يديه ويجففها حين يحمل له أحد المنشفة مع بعض المساعدة في موازنة درجة حرارة الماء مثلا.\" },\n          { id: \"motor-ind-6\", itemNumber: \"6\", ageRange: \"18-36 شهرًا\", behavior: \"يُنفذ مهام ارتداء الملابس البسيطة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يلبس ملابسه الفضفاضة بمساعدة بسيطة للتزرير.\" },\n          { id: \"motor-ind-7\", itemNumber: \"7\", ageRange: \"18-36 شهرًا\", behavior: \"يستخدم الحمام عند الحاجة مع بعض المساعدة في تنظيف نفسه\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يقضي حاجته في الحمام دون حوادث تذكر خلال النهار (من المقبول تقديم مساعدة بسيطة للطفل فقط لتنظيف نفسه).\" },\n          { id: \"motor-ind-8\", itemNumber: \"8\", ageRange: \"3-4 سنوات\", behavior: \"يساعد في الأعمال المنزلية والصفية البسيطة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يساعد في الأعمال المنزلية البسيطة (أو الصفية ان كان التقييم يتم في بيئة التعلم المبكر) مثل توزيع الصحون والملاعق على مائدة الطعام أو رفعها ووضعها في المجلى.\" },\n          { id: \"motor-ind-9\", itemNumber: \"9\", ageRange: \"3-4 سنوات\", behavior: \"يُعد فرشاة الأسنان لينظف أسنانه\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يُعد فرشاة الأسنان بوضع المعجون ومن ثم الماء عليها لينظف أسنانه.\" },\n          { id: \"motor-ind-10\", itemNumber: \"10\", ageRange: \"3-4 سنوات\", behavior: \"يستخدم أدوات الطعام التي تتناسب مع عادات وتقاليد الأسرة (الملعقة، الشوكة، الكوب، الخ) في تناول الطعام بشكل مستقل\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يستخدم أدوات الطعام التي تتناسب مع عادات وتقاليد الأسرة في تناول الطعام بشكل مستقل (قد تكون عادات الأسرة تتطلب الأكل باليد، أو بالملعقة، أو الشوكة وينبغي أن يأكل مثلهم تماما).\" },\n          { id: \"motor-ind-11\", itemNumber: \"11\", ageRange: \"3-4 سنوات\", behavior: \"يلبس ويخلع ملابسه بمساعدة بسيطة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يلبس ويخلع ملابسه الفضفاضة (لا الضيقة كالجينز مثلا) بمساعدة بسيطة للتزرير.\" },\n          { id: \"motor-ind-12\", itemNumber: \"12\", ageRange: \"3-4 سنوات\", behavior: \"يضع أدواته الشخصية في أماكن مناسبة\", applicationMethod: \"سؤال مقدم الرعاية. نستفسر من مقدم الرعاية عما اذا كان الطفل يقوم بتنظيم أدواته وألعابه في المكان المخصص لها قبل النوم كل في موقعه.\" },\n          { id: \"motor-ind-13\", itemNumber: \"13\", ageRange: \"4-5 سنوات\", behavior: \"يتبع قواعد الصحة والسلامة الأساسية (مثال: أثناء قطع الشارع أو التعرض لخطر الحريق)\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل ينتبه للمخاطر ويبتعد عن مصادرها (مثال: يتجنب الصوبة، وموقد الغاز، وينظر إلى اليمين واليسار قبل قطع الشارع).\" },\n          { id: \"motor-ind-14\", itemNumber: \"14\", ageRange: \"4-5 سنوات\", behavior: \"ينفذ بعض المهمات الروتينية اليومية بمساعدة شخص كبير\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر من مقدم الرعاية عما إذا كان الطفل يؤدي مهام العناية الذاتية مثل غسل اليدين وتناول الطعام والذهاب إلى الحمّام مع بعض المساعدة فقط.\" },\n          { id: \"motor-ind-15\", itemNumber: \"15\", ageRange: \"4-5 سنوات\", behavior: \"يرتدي ملابسه دون مساعدة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يرتدي ملابسه كاملة دون مساعدة نهائيا.\" },\n          { id: \"motor-ind-16\", itemNumber: \"16\", ageRange: \"5-6 سنوات\", behavior: \"ينجز مهمات يومية لوحده\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يؤدي مهام يومية لوحده بشكل كامل دون مساعدة مثل ترتيب سريره، أو ترتيب ألعابه.\" },\n          { id: \"motor-ind-17\", itemNumber: \"17\", ageRange: \"5-6 سنوات\", behavior: \"يؤدي مهمات العناية الذاتية دون مساعدة\", applicationMethod: \"سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يؤدي مهام العناية الذاتية كاملة دون مساعدة ويشمل ذلك غسل اليدين وتناول الطعام والذهاب إلى الحمّام بشكل مستقل وارتداء وخلع الملابس بشكل مستقل.\" }\n        ]\n      },\n    ],\n  },\n];\n\n// Dummy data for children - replace with actual data fetching\nexport let MOCK_CHILDREN_DATA: Child[] = [\n  {\n    id: \"child1\",\n    childIdNumber: \"CH-2024-001\",\n    name: \"أحمد خالد\",\n    birthDate: \"2022-05-15\",\n    enrollmentDate: \"2024-01-10\",\n    specialistName: \"د. سارة\",\n    avatarUrl: \"https://placehold.co/100x100.png?text=أخ\",\n    gender: \"male\",\n    sessionNotes: [\n      {\n        id: \"note-1678886400000\",\n        date: format(parseISO(\"2024-03-15\"), \"yyyy-MM-dd\"),\n        goalDiscussed: \"تحسين مهارة التواصل البصري أثناء اللعب\",\n        attendees: \"الأم, الأخصائية سارة\",\n        notes: \"تمت مناقشة أهمية التواصل البصري. الأم ذكرت أن أحمد يتجنب النظر المباشر أحيانًا. تم اقتراح ألعاب تتطلب النظر للوجه مثل \\\"أين أنفي؟\\\".\",\n        nextSteps: \" تطبيق الألعاب المقترحة يوميًا لمدة 10 دقائق. متابعة في الجلسة القادمة.\"\n      },\n      {\n        id: \"note-1679886400000\",\n        date: format(parseISO(\"2024-06-20\"), \"yyyy-MM-dd\"),\n        goalDiscussed: \"تشجيع المشاركة في الألعاب الجماعية البسيطة\",\n        attendees: \"الأم, الأب, الأخصائية سارة\",\n        notes: \"لوحظ أن أحمد يفضل اللعب بمفرده. تم استعراض استراتيجيات لدمجه تدريجيًا في ألعاب مع طفل آخر، مع التركيز على تبادل الأدوار البسيط.\",\n        nextSteps: \"محاولة دمج أحمد مع ابن عمه في لعبة رمي الكرة. ملاحظة تفاعلاته.\"\n      }\n    ],\n    caseStudy: {\n        basicInfo: {\n            childName: \"أحمد خالد\",\n            birthDate: \"2022-05-15\",\n            currentAge: \"2 سنوات و 2 أشهر\",\n            gender: \"male\",\n            guardianName: \"والدة أحمد\",\n            guardianPhoneNumber: \"**********\",\n            homeAddress: \"الرياض، حي الملز\",\n            guardianRelationship: \"أم\",\n            hasSiblings: \"yes\",\n            siblingsInfo: \"سارة (5 سنوات)\"\n        },\n        pregnancyAndBirthInfo: {\n            motherAgeAtPregnancy: \"30\",\n            fullTermPregnancy: \"yes\",\n            motherHealthIssuesDuringPregnancy: \"no\",\n            deliveryType: \"natural\",\n            childHealthIssuesAtBirth: \"no\"\n        },\n        reinforcerResponseInfo: {\n            favoriteToys: \"السيارات الصغيرة، المكعبات\",\n            enjoyableActivities: \"اللعب في الخارج، الاستماع لأغاني الأطفال\",\n            favoriteFoods: \"الموز، البسكويت\",\n            happinessExpression: \"يبتسم ويصفق بيديه\",\n            motivationMethods: \"إعطاؤه سيارة صغيرة، التشجيع اللفظي\",\n            smilesAtGuardian: \"yes\",\n            communicatesNeeds: \"yes\"\n        }\n    }\n  },\n  { id: \"child2\", childIdNumber: \"CH-2023-045\", name: \"ليلى فارس\", birthDate: \"2021-11-02\", enrollmentDate: \"2023-09-01\", specialistName: \"أ. عمر\", avatarUrl: \"https://placehold.co/100x100.png?text=لف\", gender: \"female\", caseStudy: defaultCaseStudyData },\n  { id: \"child3\", childIdNumber: \"CH-2024-012\", name: \"يوسف علي\", birthDate: \"2023-01-20\", enrollmentDate: \"2024-03-05\", specialistName: \"د. سارة\", avatarUrl: \"https://placehold.co/100x100.png?text=يع\", gender: \"male\", caseStudy: defaultCaseStudyData },\n];\n\nexport let MOCK_ASSESSMENTS_DATA: Assessment[] = [\n    {\n        id: \"assess1-child1\",\n        childId: \"child1\",\n        assessmentDate: \"2024-07-01\", // Updated to a more recent date for better testing of alerts\n        assessedSkills: [\n            {\n              skillId: \"soc-rel-1\",\n              status: \"yes\",\n              progressStatus: 'mastered',\n              implementationStartDate: '2024-07-02',\n              targetCompletionDate: '2024-07-09',\n              progressNotes: 'أتقن الطفل النظر إلى مقدم الرعاية بسرعة.'\n            },\n            {\n              skillId: \"soc-rel-2\",\n              status: \"yes\"\n            },\n            {\n              skillId: \"soc-rel-3\",\n              status: \"no\",\n              notes: \"يحتاج المزيد من التدريب.\",\n              progressStatus: 'implemented',\n              implementationStartDate: '2024-07-10',\n              targetCompletionDate: '2024-08-17', // Date in the future\n              progressNotes: 'بدأنا العمل على هذا الهدف، يظهر بعض التحسن.'\n            },\n            {\n              skillId: \"soc-int-1\",\n              status: \"yes\"\n            },\n             {\n              skillId: \"soc-int-4\",\n              status: \"no\",\n              notes: \"سيتم التركيز عليه قريباً.\",\n              progressStatus: 'pending'\n            },\n             {\n              skillId: \"exp-cog-1\",\n              status: \"yes\"\n            },\n            {\n              skillId: \"exp-cog-2\",\n              status: \"yes\"\n            },\n            {\n              skillId: \"exp-cog-3\",\n              status: \"no\"\n            },\n             // Add more skills from different dimensions for child1 for better analysis testing\n            { skillId: \"exp-crit-1\", status: \"yes\" },\n            { skillId: \"exp-crit-2\", status: \"yes\" },\n            { skillId: \"exp-crit-3\", status: \"yes\" }, // Forms a baseline for exp-critical-thinking\n            { skillId: \"exp-crit-4\", status: \"no\" },\n            { skillId: \"exp-crit-5\", status: \"no\" },\n            { skillId: \"exp-crit-6\", status: \"no\" }, // Forms a ceiling for exp-critical-thinking\n\n            { skillId: \"comm-comm-1\", status: \"yes\"},\n            { skillId: \"comm-comm-2\", status: \"yes\"},\n            { skillId: \"comm-comm-3\", status: \"unclear\"},\n            { skillId: \"comm-comm-4\", status: \"no\"},\n\n            { skillId: \"motor-gross-1\", status: \"yes\" },\n            { skillId: \"motor-gross-2\", status: \"yes\" },\n            { skillId: \"motor-gross-3\", status: \"yes\" },\n            { skillId: \"motor-gross-4\", status: \"yes\" },\n            { skillId: \"motor-gross-5\", status: \"yes\" },\n            { skillId: \"motor-gross-6\", status: \"yes\" },\n            { skillId: \"motor-gross-7\", status: \"yes\" },\n            { skillId: \"motor-gross-8\", status: \"yes\" },\n            { skillId: \"motor-gross-9\", status: \"yes\" },\n            { skillId: \"motor-gross-10\", status: \"no\", progressStatus: 'implemented', targetCompletionDate: '2024-07-20' }, // Overdue example\n        ]\n    }\n];\n\nexport let MOCK_USERS_DATA: User[] = [\n  { id: \"user1\", name: \"مدير النظام\", email: \"<EMAIL>\", role: \"super_admin\", avatarUrl: \"https://placehold.co/40x40.png?text=م ن\", specialization: \"إدارة عامة\" },\n  { id: \"user2\", name: \"أخصائي النطق\", email: \"<EMAIL>\", role: \"specialist\", avatarUrl: \"https://placehold.co/40x40.png?text=أ ن\", specialization: \"علاج النطق واللغة\" },\n  { id: \"user3\", name: \"معلمة الروضة\", email: \"<EMAIL>\", role: \"educator\", avatarUrl: \"https://placehold.co/40x40.png?text=م ر\", specialization: \"تعليم الطفولة المبكرة\" },\n  { id: \"user4\", name: \"مشاهد تجريبي\", email: \"<EMAIL>\", role: \"viewer\", avatarUrl: \"https://placehold.co/40x40.png?text=م ت\", specialization: \"مراقبة\" },\n  { id: \"user5\", name: \"مدير وحدة التدخل المبكر\", email: \"<EMAIL>\", role: \"eiu_manager\", avatarUrl: \"https://placehold.co/40x40.png?text=موم\", specialization: \"إدارة برامج التدخل المبكر\" },\n  { id: \"user6\", name: \"مدير حالة تجريبي\", email: \"<EMAIL>\", role: \"case_manager\", avatarUrl: \"https://placehold.co/40x40.png?text=م ح\", specialization: \"إدارة حالات\" },\n];\n\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA,0QAA6C,yBAAyB;AAAtE;;AAEO,MAAM,WAAW;AAEjB,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,uBAA6F;IACxG;QAAE,OAAO;QAAO,OAAO;QAAO,QAAQ;IAAK;IAC3C;QAAE,OAAO;QAAM,OAAO;QAAM,QAAQ;IAAI;IACxC;QAAE,OAAO;QAAW,OAAO;QAAY,QAAQ;IAAI;CACpD;AAEM,MAAM,0BAA0G;IACrH;QAAE,OAAO;QAAW,OAAO;QAAgB,QAAQ;QAAK,YAAY;IAAyB;IAC7F;QAAE,OAAO;QAAe,OAAO;QAAqB,QAAQ;QAAM,YAAY;IAAyB;IACvG;QAAE,OAAO;QAAY,OAAO;QAAe,QAAQ;QAAM,YAAY;IAA0B;IAC/F;QAAE,OAAO;QAAa,OAAO;QAAc,QAAQ;QAAO,YAAY;IAA2B;CAClG;AAGM,MAAM,oBAA0D;IACrE;QAAE,OAAO;QAAe,OAAO;IAAiC;IAChE;QAAE,OAAO;QAAe,OAAO;IAA0B;IACzD;QAAE,OAAO;QAAgB,OAAO;IAAc;IAC9C;QAAE,OAAO;QAAc,OAAO;IAAW;IACzC;QAAE,OAAO;QAAY,OAAO;IAAS;IACrC;QAAE,OAAO;QAAU,OAAO;IAAQ;CACnC;AAEM,MAAM,0BAAqG;IAChH;QAAE,OAAO;QAAW,WAAW;QAAG,WAAW;IAAG;IAChD;QAAE,OAAO;QAAW,WAAW;QAAI,WAAW;IAAG;IACjD;QAAE,OAAO;QAAa,WAAW;QAAI,WAAW;IAAG;IACnD;QAAE,OAAO;QAAa,WAAW;QAAI,WAAW;IAAG;IACnD;QAAE,OAAO;QAAa,WAAW;QAAI,WAAW;IAAG;IACnD;QAAE,OAAO;QAAa,WAAW;QAAI,WAAW;IAAG;CACpD;AAGM,MAAM,uBAAsC;IACjD,WAAW;QACT,WAAW;QACX,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,cAAc;QACd,qBAAqB;QACrB,aAAa;QACb,sBAAsB;QACtB,aAAa;QACb,cAAc;IAChB;IACA,uBAAuB;QACrB,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,mCAAmC;QACnC,2BAA2B;QAC3B,cAAc;QACd,0BAA0B;QAC1B,0BAA0B;IAC5B;IACA,wBAAwB;QACtB,cAAc;QACd,qBAAqB;QACrB,eAAe;QACf,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;IACrB;AACF;AAGO,MAAM,yBAA6C;IACxD;QACE,IAAI;QACJ,MAAM;QACN,eAAe;YACb;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAuC,mBAAmB;oBAAoG;oBAClO;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyC,mBAAmB;oBAAwF;oBACxN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA0E,mBAAmB;oBAA+N;oBAChY;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA8D,mBAAmB;oBAA8L;oBACrV;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAiD,mBAAmB;oBAAgQ;oBAC1Y;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA8B,mBAAmB;oBAA2O;oBAClW;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAsE,mBAAmB;oBAAgP;oBAChZ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA8D,mBAAmB;oBAA+P;oBACvZ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAsE,mBAAmB;oBAAgP;oBAChZ;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAA+C,mBAAmB;oBAA8J;oBACzS;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwC,mBAAmB;oBAAyF;oBAC3N;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0C,mBAAmB;oBAAkO;oBACtW;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA8D,mBAAmB;oBAAiQ;oBACzZ;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwD,mBAAmB;oBAAmO;iBACtX;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA8D,mBAAmB;oBAAqF;oBAC1O;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyD,mBAAmB;oBAAuI;oBACvR;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyD,mBAAmB;oBAA4I;oBAC5R;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAgG,mBAAmB;oBAAgN;oBACzY;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA4E,mBAAmB;oBAA6I;oBAClT;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA0E,mBAAmB;oBAA0H;oBAC7R;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAoD,mBAAmB;oBAAuO;oBACrX;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAoD,mBAAmB;oBAAsM;oBAClV;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAyF,mBAAmB;oBAAiO;oBAClZ;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAyC,mBAAmB;oBAAmK;oBACtS;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAAwR;oBAC7Y;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmC,mBAAmB;oBAAsT;oBACnb;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwB,mBAAmB;oBAAiL;oBACnS;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqE,mBAAmB;oBAAsL;iBACtV;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA4D,mBAAmB;oBAA4H;oBAC9Q;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA8C,mBAAmB;oBAA4L;oBAChU;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA2F,mBAAmB;oBAAkM;oBACrX;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA+C,mBAAmB;oBAAsK;oBAC7S;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA6C,mBAAmB;oBAAwK;oBAC7S;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAsF,mBAAmB;oBAAuM;oBACtX;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAyD,mBAAmB;oBAAkR;oBACpa;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAuG,mBAAmB;oBAA6N;oBAC7Z;wBAAE,IAAI;wBAAY,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAkE,mBAAmB;oBAAoP;oBAC7Y;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0C,mBAAmB;oBAAuE;oBAC1M;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAiD,mBAAmB;oBAAkL;oBAC5T;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA+C,mBAAmB;oBAA+I;oBACvR;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAAwM;oBAC5T;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsD,mBAAmB;oBAAoO;oBACnX;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwC,mBAAmB;oBAA+N;oBAChW;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAAmG;oBACvN;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAA6G;oBACjO;wBAAE,IAAI;wBAAa,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA6B,mBAAmB;oBAAmL;iBAC1S;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAqC,mBAAmB;oBAA8H;oBAC1P;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAwD,mBAAmB;oBAAyK;oBAC1T;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA8D,mBAAmB;oBAA6N;oBACrX;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA+E,mBAAmB;oBAAyK;oBAClV;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAwC,mBAAmB;oBAAoS;oBACpa;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAA+C,mBAAmB;oBAAgQ;oBACvY;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAmD,mBAAmB;oBAA6K;oBACxT;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAc,mBAAmB;oBAAuH;oBAC7N;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAmC,mBAAmB;oBAAsR;iBAClZ;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyD,mBAAmB;oBAA2I;oBAC3R;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAmE,mBAAmB;oBAAqI;oBACjS;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA2G,mBAAmB;oBAAyL;oBAC9X;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAyD,mBAAmB;oBAAmS;oBACtb;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAsE,mBAAmB;oBAA+L;oBAC7V;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAA0D,mBAAmB;oBAAmM;oBACrV;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAqC,mBAAmB;oBAA+O;oBAC5W;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAuE,mBAAmB;oBAA6J;oBAC5T;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAkC,mBAAmB;oBAAqN;oBAC/U;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmF,mBAAmB;oBAAsM;iBACpX;YACH;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,eAAe;YACb;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACJ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyC,mBAAmB;oBAAkI;oBAClQ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA8C,mBAAmB;oBAAmH;oBAC1P;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAgC,mBAAmB;oBAA8I;oBACvQ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA+B,mBAAmB;oBAAqJ;oBAC7Q;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA0D,mBAAmB;oBAA2L;oBAC9U;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAmE,mBAAmB;oBAAiQ;oBAC9Z;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAmC,mBAAmB;oBAAoJ;oBACjR;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAwF,mBAAmB;oBAAiF;oBACnQ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAA4B,mBAAmB;oBAA4H;oBAChP;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAiC,mBAAmB;oBAAmF;oBAC9M;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA6B,mBAAmB;oBAA8K;oBACrS;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqC,mBAAmB;oBAA+M;oBAC9U;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuC,mBAAmB;oBAAoF;oBACrN;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA4C,mBAAmB;oBAAmK;iBAC5S;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACJ;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAgC,mBAAmB;oBAAgL;oBACxS;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA8B,mBAAmB;oBAA6L;oBACnT;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAmD,mBAAmB;oBAAkS;oBAC7a;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA4C,mBAAmB;oBAAkK;oBACxS;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAgD,mBAAmB;oBAAuK;oBAClT;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAoC,mBAAmB;oBAAmJ;oBAClR;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAsD,mBAAmB;oBAA6M;oBAC9V;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAmE,mBAAmB;oBAA2M;oBACvW;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAuD,mBAAmB;oBAA4H;oBAC5Q;wBAAE,IAAI;wBAAe,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA4B,mBAAmB;oBAA+F;oBACtN;wBAAE,IAAI;wBAAe,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA+B,mBAAmB;oBAAwJ;oBAClR;wBAAE,IAAI;wBAAe,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAyF,mBAAmB;oBAAsN;iBAC7Y;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACJ;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAsC,mBAAmB;oBAA6H;oBAC3P;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAkC,mBAAmB;oBAAiH;oBAC7O;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA0C,mBAAmB;oBAA4J;oBACjS;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA2B,mBAAmB;oBAA2I;oBACjQ;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAwD,mBAAmB;oBAAgH;oBACjQ;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAyC,mBAAmB;oBAA+E;oBACjN;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAsC,mBAAmB;oBAA+G;oBAC9O;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAA6B,mBAAmB;oBAA2F;oBACjN;wBAAE,IAAI;wBAAc,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAgC,mBAAmB;oBAA8O;oBACvW;wBAAE,IAAI;wBAAe,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAiC,mBAAmB;oBAA6F;oBACzN;wBAAE,IAAI;wBAAe,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAiD,mBAAmB;oBAA2K;oBACvT;wBAAE,IAAI;wBAAe,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAyC,mBAAmB;oBAAuL;iBAC9T;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAsE,mBAAmB;oBAA0G;oBACvQ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAwD,mBAAmB;oBAAkI;oBACnR;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAgD,mBAAmB;oBAA0F;oBACpO;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAsB,mBAAmB;oBAA4H;oBAC1O;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAiD,mBAAmB;oBAAsJ;oBAC/R;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAiH,mBAAmB;oBAA6O;oBACtb;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAsE,mBAAmB;oBAAiN;iBAChX;YACH;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,eAAe;YACb;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA8D,mBAAmB;oBAA0E;oBACjO;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyC,mBAAmB;oBAAmH;oBACrP;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAiD,mBAAmB;oBAAoI;oBAC9Q;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA0E,mBAAmB;oBAA+K;oBACpV;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAyC,mBAAmB;oBAAsG;oBAC1O;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAgD,mBAAmB;oBAAqH;oBAChQ;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAwC,mBAAmB;oBAAoI;oBACvQ;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAmF,mBAAmB;oBAAwI;oBACvT;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA8D,mBAAmB;oBAA+J;oBACzT;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAwC,mBAAmB;oBAA0F;oBAChO;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA4E,mBAAmB;oBAA4L;oBACpW;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAyD,mBAAmB;oBAAgS;oBACrb;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2D,mBAAmB;oBAAiL;oBACxU;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAkD,mBAAmB;oBAAmL;oBACjU;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA6C,mBAAmB;oBAAqI;oBAC9Q;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAoB,mBAAmB;oBAAgD;oBAChK;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqD,mBAAmB;oBAAwN;iBAC1W;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAwD,mBAAmB;oBAAgJ;oBAC/R;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA4C,mBAAmB;oBAAmK;oBACxS;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA6F,mBAAmB;oBAAgF;oBACtQ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA2F,mBAAmB;oBAA2J;oBAC/U;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAwD,mBAAmB;oBAA2G;oBAC5P;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA8C,mBAAmB;oBAAsG;oBAC9O;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA4B,mBAAmB;oBAAuJ;oBAC7Q;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA6B,mBAAmB;oBAAkL;oBACzS;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAgC,mBAAmB;oBAAoH;oBAC5O;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmD,mBAAmB;oBAAyI;oBACtR;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsC,mBAAmB;oBAAmI;oBACnQ;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAAsH;oBAC3O;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmC,mBAAmB;oBAAmJ;oBAChR;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuC,mBAAmB;oBAAqP;iBACvX;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAmC,mBAAmB;oBAA+E;oBACzM;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA+B,mBAAmB;oBAA+I;oBACrQ;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAgD,mBAAmB;oBAAyI;oBAChR;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAyD,mBAAmB;oBAAmV;oBACre;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAyB,mBAAmB;oBAAgH;oBAClO;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAsC,mBAAmB;oBAA+J;oBAC/R;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA4E,mBAAmB;oBAAqS;oBAC3c;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA+C,mBAAmB;oBAAmK;oBAC5S;wBAAE,IAAI;wBAAa,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAsF,mBAAmB;oBAA8T;oBAC5e;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA4C,mBAAmB;oBAAkM;oBACxU;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwD,mBAAmB;oBAAgP;oBAClY;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqC,mBAAmB;oBAAwF;oBACvN;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0C,mBAAmB;oBAAkK;oBACtS;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0D,mBAAmB;oBAAgH;oBACpQ;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmC,mBAAmB;oBAAqI;oBAClQ;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuE,mBAAmB;oBAAkM;oBACnW;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0C,mBAAmB;oBAAwH;oBAC5P;wBAAE,IAAI;wBAAc,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAoB,mBAAmB;oBAAuO;iBACtV;YACH;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,eAAe;YACb;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAwE,mBAAmB;oBAAoH;oBACvR;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAwD,mBAAmB;oBAA8J;oBACjT;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAiD,mBAAmB;oBAAoI;oBAChR;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAuF,mBAAmB;oBAAgO;oBAClZ;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA6B,mBAAmB;oBAAuK;oBAC/R;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAiB,mBAAmB;oBAAwH;oBACpO;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA+D,mBAAmB;oBAAiM;oBAC3V;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA6C,mBAAmB;oBAA2M;oBACnV;wBAAE,IAAI;wBAAiB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA+C,mBAAmB;oBAA0L;oBACpU;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAc,UAAU;wBAAkD,mBAAmB;oBAAyN;oBAC1W;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAc,UAAU;wBAAuB,mBAAmB;oBAA2L;oBACjT;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAc,UAAU;wBAA+B,mBAAmB;oBAAkT;oBAChb;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAc,UAAU;wBAAmC,mBAAmB;oBAAqQ;oBACvY;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAc,UAAU;wBAAc,mBAAmB;oBAAmL;oBAChS;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAc,UAAU;wBAAoB,mBAAmB;oBAAoL;oBACvS;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAA2D,mBAAmB;oBAAkL;oBAC7U;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAA2C,mBAAmB;oBAA0S;oBACrb;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAoD,mBAAmB;oBAAkN;oBACtW;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAsC,mBAAmB;oBAA6b;oBACnkB;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAsC,mBAAmB;oBAAyL;oBAC/T;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAmG,mBAAmB;oBAA6X;oBAChkB;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsE,mBAAmB;oBAA0e;oBAC9oB;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0C,mBAAmB;oBAA2Z;oBACniB;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAgE,mBAAmB;oBAA0L;oBACxV;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmD,mBAAmB;oBAAoY;oBACrhB;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAiD,mBAAmB;oBAAmM;oBAClV;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAe,mBAAmB;oBAAwK;oBACrR;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuC,mBAAmB;oBAAoI;oBACzQ;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAkE,mBAAmB;oBAAsJ;oBACtT;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmF,mBAAmB;oBAAoK;oBACrV;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAiE,mBAAmB;oBAAsK;oBACrU;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsC,mBAAmB;oBAAgN;oBACpV;wBAAE,IAAI;wBAAkB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAoE,mBAAmB;oBAAoI;iBACvS;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAqD,mBAAmB;oBAA0N;oBACzW;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAmD,mBAAmB;oBAAuN;oBACpW;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA2B,mBAAmB;oBAAuL;oBAC5S;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAoB,mBAAmB;oBAAoJ;oBAClQ;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAuH,mBAAmB;oBAAqN;oBACxa;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAoC,mBAAmB;oBAA4I;oBAC5Q;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA2C,mBAAmB;oBAAwI;oBAChR;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA+C,mBAAmB;oBAA0M;oBACtV;wBAAE,IAAI;wBAAgB,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAqC,mBAAmB;oBAAgX;oBAClf;wBAAE,IAAI;wBAAiB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwC,mBAAmB;oBAAqM;oBAC1U;wBAAE,IAAI;wBAAiB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsB,mBAAmB;oBAAoN;oBACvU;wBAAE,IAAI;wBAAiB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAAqH;oBAC7O;wBAAE,IAAI;wBAAiB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqB,mBAAmB;oBAAwI;oBAC1P;wBAAE,IAAI;wBAAiB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwC,mBAAmB;oBAA6E;iBACnN;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAoF,mBAAmB;oBAAgK;oBAChV;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA8E,mBAAmB;oBAAyK;oBACnV;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA8B,mBAAmB;oBAAwK;oBAClS;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAwE,mBAAmB;oBAAmM;oBACvW;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAA6F,mBAAmB;oBAAwJ;oBACjV;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAwC,mBAAmB;oBAAiN;oBACvV;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAyD,mBAAmB;oBAAoJ;oBAC3S;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAqD,mBAAmB;oBAAuU;oBAC1d;wBAAE,IAAI;wBAAkB,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA+F,mBAAmB;oBAA6R;oBAC3d;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAA+E,mBAAmB;oBAA6K;oBAC7V;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAqC,mBAAmB;oBAAoM;oBAC1U;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAA+D,mBAAmB;oBAA+Y;oBAC/iB;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuG,mBAAmB;oBAA0S;oBAChf;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqG,mBAAmB;oBAAyM;oBAC7Y;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwE,mBAAmB;oBAAmK;oBAC1U;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuC,mBAAmB;oBAA0T;oBAChc;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAwH,mBAAmB;oBAA2M;oBACla;wBAAE,IAAI;wBAAmB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAkF,mBAAmB;oBAAkO;iBACpZ;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAiI,mBAAmB;oBAAoP;oBACld;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAyC,mBAAmB;oBAAgQ;oBACtY;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAwE,mBAAmB;oBAA8M;oBACnX;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA2B,mBAAmB;oBAAkI;oBAC5P;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAAoD,mBAAmB;oBAAmO;oBACtX;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA6D,mBAAmB;oBAA6R;oBACzb;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA6D,mBAAmB;oBAA+N;oBAC3X;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA4E,mBAAmB;oBAAuJ;oBAClU;wBAAE,IAAI;wBAAmB,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA8C,mBAAmB;oBAAmM;oBACjV;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAmE,mBAAmB;oBAAgM;oBACrW;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAe,UAAU;wBAAwC,mBAAmB;oBAAqJ;oBAC/R;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA8C,mBAAmB;oBAA8P;oBAC5Y;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAkC,mBAAmB;oBAAsM;oBACxU;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAgH,mBAAmB;oBAAiK;oBACjX;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA6B,mBAAmB;oBAAkM;oBAC/T;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA4C,mBAAmB;oBAA4K;oBACxT;wBAAE,IAAI;wBAAoB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsC,mBAAmB;oBAA4M;iBACnV;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,QAAQ;oBACN;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAY,UAAU;wBAAqD,mBAAmB;oBAAsJ;oBACpS;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAc,UAAU;wBAA2B,mBAAmB;oBAAkJ;oBACxQ;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAqC,mBAAmB;oBAAsJ;oBACvR;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAwB,mBAAmB;oBAAwL;oBAC5S;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA2B,mBAAmB;oBAA+J;oBACtR;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAAqC,mBAAmB;oBAAiH;oBAClP;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAe,UAAU;wBAA0D,mBAAmB;oBAA2K;oBACjU;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAA6C,mBAAmB;oBAAkO;oBACzW;wBAAE,IAAI;wBAAe,YAAY;wBAAK,UAAU;wBAAa,UAAU;wBAAmC,mBAAmB;oBAAsI;oBACnQ;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAoH,mBAAmB;oBAAoP;oBACpc;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAmC,mBAAmB;oBAAgJ;oBAC/Q;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAsC,mBAAmB;oBAAqI;oBACvQ;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAqF,mBAAmB;oBAAiL;oBAClW;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAuD,mBAAmB;oBAA8J;oBACjT;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA2B,mBAAmB;oBAA2G;oBAClO;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAA0B,mBAAmB;oBAAkJ;oBACxQ;wBAAE,IAAI;wBAAgB,YAAY;wBAAM,UAAU;wBAAa,UAAU;wBAAyC,mBAAmB;oBAAmN;iBACzV;YACH;SACD;IACH;CACD;AAGM,IAAI,qBAA8B;IACvC;QACE,IAAI;QACJ,eAAe;QACf,MAAM;QACN,WAAW;QACX,gBAAgB;QAChB,gBAAgB;QAChB,WAAW;QACX,QAAQ;QACR,cAAc;YACZ;gBACE,IAAI;gBACJ,MAAM,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;gBACrC,eAAe;gBACf,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;YACA;gBACE,IAAI;gBACJ,MAAM,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;gBACrC,eAAe;gBACf,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;SACD;QACD,WAAW;YACP,WAAW;gBACP,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,QAAQ;gBACR,cAAc;gBACd,qBAAqB;gBACrB,aAAa;gBACb,sBAAsB;gBACtB,aAAa;gBACb,cAAc;YAClB;YACA,uBAAuB;gBACnB,sBAAsB;gBACtB,mBAAmB;gBACnB,mCAAmC;gBACnC,cAAc;gBACd,0BAA0B;YAC9B;YACA,wBAAwB;gBACpB,cAAc;gBACd,qBAAqB;gBACrB,eAAe;gBACf,qBAAqB;gBACrB,mBAAmB;gBACnB,kBAAkB;gBAClB,mBAAmB;YACvB;QACJ;IACF;IACA;QAAE,IAAI;QAAU,eAAe;QAAe,MAAM;QAAa,WAAW;QAAc,gBAAgB;QAAc,gBAAgB;QAAU,WAAW;QAA4C,QAAQ;QAAU,WAAW;IAAqB;IAC3P;QAAE,IAAI;QAAU,eAAe;QAAe,MAAM;QAAY,WAAW;QAAc,gBAAgB;QAAc,gBAAgB;QAAW,WAAW;QAA4C,QAAQ;QAAQ,WAAW;IAAqB;CAC1P;AAEM,IAAI,wBAAsC;IAC7C;QACI,IAAI;QACJ,SAAS;QACT,gBAAgB;QAChB,gBAAgB;YACZ;gBACE,SAAS;gBACT,QAAQ;gBACR,gBAAgB;gBAChB,yBAAyB;gBACzB,sBAAsB;gBACtB,eAAe;YACjB;YACA;gBACE,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,gBAAgB;gBAChB,yBAAyB;gBACzB,sBAAsB;gBACtB,eAAe;YACjB;YACA;gBACE,SAAS;gBACT,QAAQ;YACV;YACC;gBACC,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,gBAAgB;YAClB;YACC;gBACC,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,SAAS;gBACT,QAAQ;YACV;YACC,mFAAmF;YACpF;gBAAE,SAAS;gBAAc,QAAQ;YAAM;YACvC;gBAAE,SAAS;gBAAc,QAAQ;YAAM;YACvC;gBAAE,SAAS;gBAAc,QAAQ;YAAM;YACvC;gBAAE,SAAS;gBAAc,QAAQ;YAAK;YACtC;gBAAE,SAAS;gBAAc,QAAQ;YAAK;YACtC;gBAAE,SAAS;gBAAc,QAAQ;YAAK;YAEtC;gBAAE,SAAS;gBAAe,QAAQ;YAAK;YACvC;gBAAE,SAAS;gBAAe,QAAQ;YAAK;YACvC;gBAAE,SAAS;gBAAe,QAAQ;YAAS;YAC3C;gBAAE,SAAS;gBAAe,QAAQ;YAAI;YAEtC;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAiB,QAAQ;YAAM;YAC1C;gBAAE,SAAS;gBAAkB,QAAQ;gBAAM,gBAAgB;gBAAe,sBAAsB;YAAa;SAChH;IACL;CACH;AAEM,IAAI,kBAA0B;IACnC;QAAE,IAAI;QAAS,MAAM;QAAe,OAAO;QAAqB,MAAM;QAAe,WAAW;QAA2C,gBAAgB;IAAa;IACxK;QAAE,IAAI;QAAS,MAAM;QAAgB,OAAO;QAA0B,MAAM;QAAc,WAAW;QAA2C,gBAAgB;IAAoB;IACpL;QAAE,IAAI;QAAS,MAAM;QAAgB,OAAO;QAAuB,MAAM;QAAY,WAAW;QAA2C,gBAAgB;IAAwB;IACnL;QAAE,IAAI;QAAS,MAAM;QAAgB,OAAO;QAAsB,MAAM;QAAU,WAAW;QAA2C,gBAAgB;IAAS;IACjK;QAAE,IAAI;QAAS,MAAM;QAA2B,OAAO;QAA2B,MAAM;QAAe,WAAW;QAA2C,gBAAgB;IAA4B;IACzM;QAAE,IAAI;QAAS,MAAM;QAAoB,OAAO;QAA2B,MAAM;QAAgB,WAAW;QAA2C,gBAAgB;IAAc;CACtL", "debugId": null}}, {"offset": {"line": 4168, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/layout/AppHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from 'next/link';\nimport { SidebarTrigger } from '@/components/ui/sidebar';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { <PERSON> } from 'lucide-react'; // Using <PERSON> as a placeholder logo icon\nimport { APP_NAME } from '@/lib/constants';\n\nexport default function AppHeader() {\n  return (\n    <header className=\"sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-card px-4 shadow-sm md:px-6\">\n      <div className=\"md:hidden\">\n        <SidebarTrigger />\n      </div>\n      <div className=\"flex w-full items-center justify-between\">\n        <Link href=\"/\" className=\"flex items-center gap-2 text-lg font-semibold md:text-base\">\n          <Brain className=\"h-6 w-6 text-primary\" />\n          <span className=\"font-bold\">{APP_NAME}</span>\n        </Link>\n        <div className=\"flex items-center gap-4\">\n          {/* Placeholder for future elements like search or notifications */}\n          <Avatar>\n            <AvatarImage src=\"https://placehold.co/40x40.png\" alt=\"المستخدم\" data-ai-hint=\"user avatar\" />\n            <AvatarFallback>م</AvatarFallback>\n          </Avatar>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA,0VAAsC,yCAAyC;AAC/E;AAPA;;;;;;;AASe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sIAAA,CAAA,iBAAc;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAa,0HAAA,CAAA,WAAQ;;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,qIAAA,CAAA,cAAW;oCAAC,KAAI;oCAAiC,KAAI;oCAAW,gBAAa;;;;;;8CAC9E,6LAAC,qIAAA,CAAA,iBAAc;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;KArBwB", "debugId": null}}, {"offset": {"line": 4284, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/layout/AppSidebar.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Home, Users, ClipboardList, Lightbulb, BarChart3, Settings, UserCog, Database } from 'lucide-react'; // Changed UsersCog to UserCog, Added Database\nimport { cn } from '@/lib/utils';\nimport {\n  SidebarMenu,\n  SidebarMenuItem,\n  SidebarMenuButton,\n} from '@/components/ui/sidebar';\n\nconst navItems = [\n  { href: '/', label: 'لوحة التحكم', icon: Home },\n  { href: '/children', label: 'الأطفال', icon: Users },\n  { href: '/users', label: 'إدارة المستخدمين', icon: UserCog },\n  { href: '/data-management', label: 'إدارة البيانات', icon: Database }, // Added Data Management link\n  { href: '/settings', label: 'الإعدادات', icon: Settings },\n  // More items can be added later, e.g., for assessments overview, reports\n  // { href: '/assessments', label: 'التقييمات', icon: ClipboardList },\n  // { href: '/learning-plans', label: 'خطط التعلم', icon: Lightbulb },\n  // { href: '/reports', label: 'التقارير', icon: BarChart3 },\n];\n\nexport default function AppSidebar() {\n  const pathname = usePathname();\n\n  return (\n    <nav className=\"flex flex-col h-full\">\n      <SidebarMenu className=\"flex-1\">\n        {navItems.map((item) => (\n          <SidebarMenuItem key={item.href}>\n            <Link href={item.href} passHref legacyBehavior>\n              <SidebarMenuButton\n                className={cn(\n                  'w-full justify-start',\n                  pathname === item.href || (item.href !== \"/\" && pathname.startsWith(item.href))\n                    ? 'bg-sidebar-accent text-sidebar-accent-foreground font-semibold'\n                    : 'hover:bg-sidebar-accent/80'\n                )}\n                isActive={pathname === item.href || (item.href !== \"/\" && pathname.startsWith(item.href))}\n                tooltip={{children: item.label, side: \"left\", align:\"center\"}}\n              >\n                <item.icon className=\"h-5 w-5\" />\n                <span className=\"group-data-[collapsible=icon]:hidden\">{item.label}</span>\n              </SidebarMenuButton>\n            </Link>\n          </SidebarMenuItem>\n        ))}\n      </SidebarMenu>\n      {/* Optional: Add settings or user profile link at the bottom */}\n      {/* <SidebarMenu className=\"mt-auto\">\n        <SidebarMenuItem>\n           <Link href=\"/settings\" passHref legacyBehavior>\n              <SidebarMenuButton\n                className={cn(\n                  'w-full justify-start',\n                  pathname === \"/settings\"\n                    ? 'bg-sidebar-accent text-sidebar-accent-foreground font-semibold'\n                    : 'hover:bg-sidebar-accent/80'\n                )}\n                isActive={pathname === \"/settings\"}\n                tooltip={{children: \"الإعدادات\", side: \"left\", align:\"center\"}}\n              >\n                <Settings className=\"h-5 w-5\" />\n                 <span className=\"group-data-[collapsible=icon]:hidden\">الإعدادات</span>\n              </SidebarMenuButton>\n            </Link>\n        </SidebarMenuItem>\n      </SidebarMenu> */}\n    </nav>\n  );\n}\n\n    "], "names": [], "mappings": ";;;;AAGA;AACA;AACA,wVAA8G,8CAA8C;AAA5J;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAYA,MAAM,WAAW;IACf;QAAE,MAAM;QAAK,OAAO;QAAe,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC9C;QAAE,MAAM;QAAa,OAAO;QAAW,MAAM,uMAAA,CAAA,QAAK;IAAC;IACnD;QAAE,MAAM;QAAU,OAAO;QAAoB,MAAM,+MAAA,CAAA,UAAO;IAAC;IAC3D;QAAE,MAAM;QAAoB,OAAO;QAAkB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACpE;QAAE,MAAM;QAAa,OAAO;QAAa,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAKzD;AAEc,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sIAAA,CAAA,cAAW;YAAC,WAAU;sBACpB,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,sIAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,KAAK,IAAI;wBAAE,QAAQ;wBAAC,cAAc;kCAC5C,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wBACA,aAAa,KAAK,IAAI,IAAK,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI,IACzE,mEACA;4BAEN,UAAU,aAAa,KAAK,IAAI,IAAK,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BACvF,SAAS;gCAAC,UAAU,KAAK,KAAK;gCAAE,MAAM;gCAAQ,OAAM;4BAAQ;;8CAE5D,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAK,WAAU;8CAAwC,KAAK,KAAK;;;;;;;;;;;;;;;;;mBAblD,KAAK,IAAI;;;;;;;;;;;;;;;AAyCzC;GAhDwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 4412, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/layout/MainLayout.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React from 'react';\nimport {\n  Sidebar,\n  <PERSON>bar<PERSON>ontent,\n  <PERSON>barInset,\n  SidebarHeader,\n  SidebarFooter,\n} from '@/components/ui/sidebar';\nimport AppHeader from '@/components/layout/AppHeader';\nimport AppSidebar from '@/components/layout/AppSidebar';\n\ntype MainLayoutProps = {\n  children: React.ReactNode;\n};\n\nexport default function MainLayout({ children }: MainLayoutProps) {\n  return (\n    <>\n      <Sidebar>\n        <SidebarHeader>\n          {/* Placeholder for potential sidebar header content if needed */}\n        </SidebarHeader>\n        <SidebarContent>\n          <AppSidebar />\n        </SidebarContent>\n        <SidebarFooter>\n          {/* Placeholder for potential sidebar footer content if needed */}\n        </SidebarFooter>\n      </Sidebar>\n      <SidebarInset>\n        <AppHeader />\n        <main className=\"flex-1 p-4 sm:p-6 lg:p-8 bg-background\">\n          {children}\n        </main>\n        <footer className=\"p-4 text-center text-xs text-muted-foreground border-t\">\n          تصميم احمد الخمايسه\n        </footer>\n      </SidebarInset>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AAOA;AACA;AAXA;;;;;AAiBe,SAAS,WAAW,EAAE,QAAQ,EAAmB;IAC9D,qBACE;;0BACE,6LAAC,sIAAA,CAAA,UAAO;;kCACN,6LAAC,sIAAA,CAAA,gBAAa;;;;;kCAGd,6LAAC,sIAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,6IAAA,CAAA,UAAU;;;;;;;;;;kCAEb,6LAAC,sIAAA,CAAA,gBAAa;;;;;;;;;;;0BAIhB,6LAAC,sIAAA,CAAA,eAAY;;kCACX,6LAAC,4IAAA,CAAA,UAAS;;;;;kCACV,6LAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,6LAAC;wBAAO,WAAU;kCAAyD;;;;;;;;;;;;;;AAMnF;KAzBwB", "debugId": null}}]}