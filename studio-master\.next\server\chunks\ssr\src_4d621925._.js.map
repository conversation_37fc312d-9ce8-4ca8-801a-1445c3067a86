{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item.dataKey || item.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQpC,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE;IAChD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,8OAAC;YACC,cAAY;YACZ,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ypBACA;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,8OAAC,mKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;AAEA,MAAM,eAAe,uJAAA,CAAA,UAAyB;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAWzC,CACE,EACE,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACT,EACD;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;YACjC,OAAO;QACT;QAEA,MAAM,CAAC,KAAK,GAAG;QACf,MAAM,MAAM,GAAG,YAAY,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,SAAS;QACjE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;QAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;QAElB,IAAI,gBAAgB;YAClB,qBACE,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAC/B,eAAe,OAAO;;;;;;QAG7B;QAEA,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,qBAAO,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;sBAAkB;;;;;;IAC7D,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,8OAAC;wBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,8OAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,8OAAC;oCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,8OAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;AAEF,oBAAoB,WAAW,GAAG;AAElC,MAAM,cAAc,sJAAA,CAAA,SAAwB;AAE5C,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAQxC,CACE,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,gBAAgB,QAAQ,EAAE,OAAO,EAAE,EAC3E;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,8OAAC,WAAW,IAAI;;;;6CAEhB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;AAEF,mBAAmB,WAAW,GAAG;AAEjC,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/AgeDistributionChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ChartConfig } from \"@/components/ui/chart\";\nimport { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, YAxis, Tooltip, Cell } from \"recharts\";\nimport { ChartContainer, ChartTooltip, ChartTooltipContent } from \"@/components/ui/chart\";\n\ninterface AgeDistributionDataPoint {\n  label: string;\n  minMonths: number;\n  maxMonths: number;\n  count: number;\n  fill: string;\n}\n\ninterface AgeDistributionChartProps {\n  data: AgeDistributionDataPoint[];\n  config: ChartConfig;\n}\n\nexport default function AgeDistributionChart({ data, config }: AgeDistributionChartProps) {\n  if (!data || data.length === 0) {\n    return <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض توزيع الأعمار.</p>;\n  }\n\n  return (\n    <div className=\"h-[300px] w-full\">\n      <ChartContainer config={config} className=\"w-full h-full\">\n        <BarChart accessibilityLayer data={data} layout=\"vertical\" margin={{ right: 20, left: 20 }}>\n          <CartesianGrid horizontal={false} />\n          <XAxis type=\"number\" dataKey=\"count\" allowDecimals={false} stroke=\"hsl(var(--muted-foreground))\" fontSize={12} />\n          <YAxis\n            type=\"category\"\n            dataKey=\"label\"\n            stroke=\"hsl(var(--muted-foreground))\"\n            fontSize={12}\n            width={80}\n            tickLine={false}\n            axisLine={false}\n          />\n          <ChartTooltip\n            cursor={{ fill: 'hsl(var(--muted))' }}\n            content={<ChartTooltipContent />}\n          />\n          <Bar dataKey=\"count\" radius={4}>\n            {data.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.fill} />\n            ))}\n          </Bar>\n        </BarChart>\n      </ChartContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAmBe,SAAS,qBAAqB,EAAE,IAAI,EAAE,MAAM,EAA6B;IACtF,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBAAO,8OAAC;YAAE,WAAU;sBAAyC;;;;;;IAC/D;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAQ,WAAU;sBACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gBAAC,kBAAkB;gBAAC,MAAM;gBAAM,QAAO;gBAAW,QAAQ;oBAAE,OAAO;oBAAI,MAAM;gBAAG;;kCACvF,8OAAC,6JAAA,CAAA,gBAAa;wBAAC,YAAY;;;;;;kCAC3B,8OAAC,qJAAA,CAAA,QAAK;wBAAC,MAAK;wBAAS,SAAQ;wBAAQ,eAAe;wBAAO,QAAO;wBAA+B,UAAU;;;;;;kCAC3G,8OAAC,qJAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,UAAU;;;;;;kCAEZ,8OAAC,iIAAA,CAAA,eAAY;wBACX,QAAQ;4BAAE,MAAM;wBAAoB;wBACpC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;;;;;;;;;;kCAE/B,8OAAC,mJAAA,CAAA,MAAG;wBAAC,SAAQ;wBAAQ,QAAQ;kCAC1B,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,8OAAC,oJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,IAAI;+BAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/ProgressOverviewChart.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Tooltip as RechartsTooltip, Legend as RechartsLegend } from \"recharts\";\nimport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  type ChartConfig\n} from \"@/components/ui/chart\";\n\ninterface ProgressOverviewChartProps {\n  mastered: number;\n  implemented: number;\n  pending: number;\n}\n\nexport default function ProgressOverviewChart({ mastered, implemented, pending }: ProgressOverviewChartProps) {\n  const chartData = [\n    { name: 'أهداف متقنة', value: mastered, fill: 'hsl(var(--chart-2))' },\n    { name: 'أهداف قيد التنفيذ', value: implemented, fill: 'hsl(var(--chart-1))' },\n    { name: 'أهداف معلقة/بدون تقييم', value: pending, fill: 'hsl(var(--chart-4))' },\n  ].filter(item => item.value > 0); // Filter out zero values to avoid empty slices\n\n  if (chartData.length === 0) {\n    return <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض مخطط تقدم الأهداف.</p>;\n  }\n\n  const chartConfig = chartData.reduce((acc, item) => {\n    acc[item.name] = { label: item.name, color: item.fill };\n    return acc;\n  }, {} as ChartConfig);\n\n\n  return (\n    <div className=\"h-[300px] w-full\">\n      <ChartContainer config={chartConfig} className=\"w-full h-full\">\n        <PieChart>\n          <ChartTooltip content={<ChartTooltipContent nameKey=\"name\" hideLabel />} />\n          <Pie\n            data={chartData}\n            dataKey=\"value\"\n            nameKey=\"name\"\n            cx=\"50%\"\n            cy=\"50%\"\n            outerRadius={100}\n            labelLine={false}\n            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {\n              const RADIAN = Math.PI / 180;\n              const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n              const x = cx + radius * Math.cos(-midAngle * RADIAN);\n              const y = cy + radius * Math.sin(-midAngle * RADIAN);\n              if (chartData[index].value === 0) return null; // Don't render label for zero value slices\n              return (\n                <text\n                  x={x}\n                  y={y}\n                  fill=\"hsl(var(--primary-foreground))\"\n                  textAnchor={x > cx ? 'start' : 'end'}\n                  dominantBaseline=\"central\"\n                  fontSize=\"12px\"\n                >\n                  {`${(percent * 100).toFixed(0)}%`}\n                </text>\n              );\n            }}\n          >\n            {chartData.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.fill} />\n            ))}\n          </Pie>\n          <ChartLegend\n            content={<ChartLegendContent nameKey=\"name\" wrapperStyle={{paddingTop: 20}} />}\n            verticalAlign=\"bottom\"\n            align=\"center\"\n          />\n        </PieChart>\n      </ChartContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAHA;;;;AAkBe,SAAS,sBAAsB,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAA8B;IAC1G,MAAM,YAAY;QAChB;YAAE,MAAM;YAAe,OAAO;YAAU,MAAM;QAAsB;QACpE;YAAE,MAAM;YAAqB,OAAO;YAAa,MAAM;QAAsB;QAC7E;YAAE,MAAM;YAA0B,OAAO;YAAS,MAAM;QAAsB;KAC/E,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,IAAI,+CAA+C;IAEjF,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBAAO,8OAAC;YAAE,WAAU;sBAAyC;;;;;;IAC/D;IAEA,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK;QACzC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,IAAI;YAAE,OAAO,KAAK,IAAI;QAAC;QACtD,OAAO;IACT,GAAG,CAAC;IAGJ,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAa,WAAU;sBAC7C,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kCACP,8OAAC,iIAAA,CAAA,eAAY;wBAAC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;4BAAC,SAAQ;4BAAO,SAAS;;;;;;;;;;;kCACpE,8OAAC,+IAAA,CAAA,MAAG;wBACF,MAAM;wBACN,SAAQ;wBACR,SAAQ;wBACR,IAAG;wBACH,IAAG;wBACH,aAAa;wBACb,WAAW;wBACX,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;4BAC1E,MAAM,SAAS,KAAK,EAAE,GAAG;4BACzB,MAAM,SAAS,cAAc,CAAC,cAAc,WAAW,IAAI;4BAC3D,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,2CAA2C;4BAC1F,qBACE,8OAAC;gCACC,GAAG;gCACH,GAAG;gCACH,MAAK;gCACL,YAAY,IAAI,KAAK,UAAU;gCAC/B,kBAAiB;gCACjB,UAAS;0CAER,GAAG,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;wBAGvC;kCAEC,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,IAAI;+BAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,8OAAC,iIAAA,CAAA,cAAW;wBACV,uBAAS,8OAAC,iIAAA,CAAA,qBAAkB;4BAAC,SAAQ;4BAAO,cAAc;gCAAC,YAAY;4BAAE;;;;;;wBACzE,eAAc;wBACd,OAAM;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/GenderDistributionChart.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from \"recharts\";\nimport {\n  Chart<PERSON>ontainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  type ChartConfig\n} from \"@/components/ui/chart\";\n\ninterface GenderDistributionChartProps {\n  maleCount: number;\n  femaleCount: number;\n}\n\nexport default function GenderDistributionChart({ maleCount, femaleCount }: GenderDistributionChartProps) {\n  const chartData = [\n    { name: 'ذكور', value: maleCount, fill: 'hsl(var(--chart-1))' }, // Primary blue\n    { name: 'إناث', value: femaleCount, fill: 'hsl(var(--chart-3))' }, // Tealish or another contrasting color\n  ].filter(item => item.value > 0);\n\n  if (chartData.length === 0) {\n    return <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض توزيع الجنس.</p>;\n  }\n\n  const chartConfig = chartData.reduce((acc, item) => {\n    acc[item.name] = { label: item.name, color: item.fill };\n    return acc;\n  }, {} as ChartConfig);\n\n  return (\n    <div className=\"h-[300px] w-full\">\n      <ChartContainer config={chartConfig} className=\"w-full h-full\">\n        <PieChart>\n          <ChartTooltip content={<ChartTooltipContent nameKey=\"name\" hideLabel />} />\n          <Pie\n            data={chartData}\n            dataKey=\"value\"\n            nameKey=\"name\"\n            cx=\"50%\"\n            cy=\"50%\"\n            outerRadius={80} // Adjusted outerRadius for better fit with label\n            labelLine={false}\n            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {\n              const RADIAN = Math.PI / 180;\n              // Position label slightly outside the pie slice for clarity\n              const radius = outerRadius + 15; \n              const x = cx + radius * Math.cos(-midAngle * RADIAN);\n              const y = cy + radius * Math.sin(-midAngle * RADIAN);\n              if (chartData[index].value === 0) return null;\n              return (\n                <text\n                  x={x}\n                  y={y}\n                  fill=\"hsl(var(--foreground))\" // Use foreground for better visibility\n                  textAnchor={x > cx ? 'start' : 'end'}\n                  dominantBaseline=\"central\"\n                  fontSize=\"12px\"\n                >\n                  {`${name}: ${(percent * 100).toFixed(0)}%`}\n                </text>\n              );\n            }}\n          >\n            {chartData.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.fill} />\n            ))}\n          </Pie>\n          <ChartLegend\n            content={<ChartLegendContent nameKey=\"name\" wrapperStyle={{paddingTop: 10}} />}\n            verticalAlign=\"bottom\"\n            align=\"center\"\n          />\n        </PieChart>\n      </ChartContainer>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAHA;;;;AAiBe,SAAS,wBAAwB,EAAE,SAAS,EAAE,WAAW,EAAgC;IACtG,MAAM,YAAY;QAChB;YAAE,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAsB;QAC9D;YAAE,MAAM;YAAQ,OAAO;YAAa,MAAM;QAAsB;KACjE,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG;IAE9B,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBAAO,8OAAC;YAAE,WAAU;sBAAyC;;;;;;IAC/D;IAEA,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK;QACzC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,IAAI;YAAE,OAAO,KAAK,IAAI;QAAC;QACtD,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAa,WAAU;sBAC7C,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kCACP,8OAAC,iIAAA,CAAA,eAAY;wBAAC,uBAAS,8OAAC,iIAAA,CAAA,sBAAmB;4BAAC,SAAQ;4BAAO,SAAS;;;;;;;;;;;kCACpE,8OAAC,+IAAA,CAAA,MAAG;wBACF,MAAM;wBACN,SAAQ;wBACR,SAAQ;wBACR,IAAG;wBACH,IAAG;wBACH,aAAa;wBACb,WAAW;wBACX,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;4BAC1E,MAAM,SAAS,KAAK,EAAE,GAAG;4BACzB,4DAA4D;4BAC5D,MAAM,SAAS,cAAc;4BAC7B,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO;4BACzC,qBACE,8OAAC;gCACC,GAAG;gCACH,GAAG;gCACH,MAAK,yBAAyB,uCAAuC;;gCACrE,YAAY,IAAI,KAAK,UAAU;gCAC/B,kBAAiB;gCACjB,UAAS;0CAER,GAAG,KAAK,EAAE,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;wBAGhD;kCAEC,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,IAAI;+BAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,8OAAC,iIAAA,CAAA,cAAW;wBACV,uBAAS,8OAAC,iIAAA,CAAA,qBAAkB;4BAAC,SAAQ;4BAAO,cAAc;gCAAC,YAAY;4BAAE;;;;;;wBACzE,eAAc;wBACd,OAAM;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/storage.ts"], "sourcesContent": ["import type { Child, Assessment, User, LearningPlan } from './types';\nimport { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, MOCK_USERS_DATA } from './constants';\n\n// Storage keys\nconst STORAGE_KEYS = {\n  CHILDREN: 'portage_plus_children',\n  ASSESSMENTS: 'portage_plus_assessments',\n  USERS: 'portage_plus_users',\n  LEARNING_PLANS: 'portage_plus_learning_plans',\n  APP_VERSION: 'portage_plus_version',\n  LAST_SYNC: 'portage_plus_last_sync',\n} as const;\n\nconst APP_VERSION = '1.0.0';\n\n// Check if we're in a browser environment\nconst isBrowser = typeof window !== 'undefined';\n\n// Initialize storage with default data if empty\nexport function initializeStorage(): void {\n  if (!isBrowser) return;\n\n  try {\n    // Check if this is the first time or if we need to migrate data\n    const currentVersion = localStorage.getItem(STORAGE_KEYS.APP_VERSION);\n    const existingChildren = localStorage.getItem(STORAGE_KEYS.CHILDREN);\n\n    if (!currentVersion || !existingChildren) {\n      // First time setup - populate with mock data\n      localStorage.setItem(STORAGE_KEYS.CHILDREN, JSON.stringify(MOCK_CHILDREN_DATA));\n      localStorage.setItem(STORAGE_KEYS.ASSESSMENTS, JSON.stringify(MOCK_ASSESSMENTS_DATA));\n      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(MOCK_USERS_DATA));\n      localStorage.setItem(STORAGE_KEYS.LEARNING_PLANS, JSON.stringify([]));\n      localStorage.setItem(STORAGE_KEYS.APP_VERSION, APP_VERSION);\n      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());\n      \n      console.log('✅ Local storage initialized with default data');\n    }\n  } catch (error) {\n    console.error('❌ Failed to initialize storage:', error);\n  }\n}\n\n// Generic storage functions\nfunction getFromStorage<T>(key: string, defaultValue: T): T {\n  if (!isBrowser) return defaultValue;\n\n  try {\n    const item = localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`❌ Failed to get ${key} from storage:`, error);\n    return defaultValue;\n  }\n}\n\nfunction setToStorage<T>(key: string, value: T): boolean {\n  if (!isBrowser) return false;\n\n  try {\n    localStorage.setItem(key, JSON.stringify(value));\n    localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());\n    return true;\n  } catch (error) {\n    console.error(`❌ Failed to set ${key} to storage:`, error);\n    return false;\n  }\n}\n\n// Children storage functions\nexport function getChildren(): Child[] {\n  return getFromStorage(STORAGE_KEYS.CHILDREN, []);\n}\n\nexport function getChildById(id: string): Child | undefined {\n  const children = getChildren();\n  return children.find(child => child.id === id);\n}\n\nexport function saveChild(child: Child): boolean {\n  const children = getChildren();\n  const existingIndex = children.findIndex(c => c.id === child.id);\n  \n  if (existingIndex >= 0) {\n    children[existingIndex] = child;\n  } else {\n    children.unshift(child); // Add new children at the beginning\n  }\n  \n  return setToStorage(STORAGE_KEYS.CHILDREN, children);\n}\n\nexport function deleteChild(childId: string): boolean {\n  const children = getChildren();\n  const filteredChildren = children.filter(c => c.id !== childId);\n  \n  // Also delete related assessments and learning plans\n  const assessments = getAssessments();\n  const filteredAssessments = assessments.filter(a => a.childId !== childId);\n  setToStorage(STORAGE_KEYS.ASSESSMENTS, filteredAssessments);\n  \n  const learningPlans = getLearningPlans();\n  const filteredPlans = learningPlans.filter(p => p.childId !== childId);\n  setToStorage(STORAGE_KEYS.LEARNING_PLANS, filteredPlans);\n  \n  return setToStorage(STORAGE_KEYS.CHILDREN, filteredChildren);\n}\n\n// Assessment storage functions\nexport function getAssessments(): Assessment[] {\n  return getFromStorage(STORAGE_KEYS.ASSESSMENTS, []);\n}\n\nexport function getAssessmentsByChildId(childId: string): Assessment[] {\n  const assessments = getAssessments();\n  return assessments.filter(assessment => assessment.childId === childId);\n}\n\nexport function getAssessmentById(id: string): Assessment | undefined {\n  const assessments = getAssessments();\n  return assessments.find(assessment => assessment.id === id);\n}\n\nexport function saveAssessment(assessment: Assessment): boolean {\n  const assessments = getAssessments();\n  const existingIndex = assessments.findIndex(a => a.id === assessment.id);\n  \n  if (existingIndex >= 0) {\n    assessments[existingIndex] = assessment;\n  } else {\n    assessments.unshift(assessment); // Add new assessments at the beginning\n  }\n  \n  return setToStorage(STORAGE_KEYS.ASSESSMENTS, assessments);\n}\n\nexport function deleteAssessment(assessmentId: string): boolean {\n  const assessments = getAssessments();\n  const filteredAssessments = assessments.filter(a => a.id !== assessmentId);\n  \n  // Also delete related learning plans\n  const learningPlans = getLearningPlans();\n  const filteredPlans = learningPlans.filter(p => p.assessmentId !== assessmentId);\n  setToStorage(STORAGE_KEYS.LEARNING_PLANS, filteredPlans);\n  \n  return setToStorage(STORAGE_KEYS.ASSESSMENTS, filteredAssessments);\n}\n\n// User storage functions\nexport function getUsers(): User[] {\n  return getFromStorage(STORAGE_KEYS.USERS, []);\n}\n\nexport function getUserById(id: string): User | undefined {\n  const users = getUsers();\n  return users.find(user => user.id === id);\n}\n\nexport function saveUser(user: User): boolean {\n  const users = getUsers();\n  const existingIndex = users.findIndex(u => u.id === user.id);\n  \n  if (existingIndex >= 0) {\n    users[existingIndex] = user;\n  } else {\n    users.push(user);\n  }\n  \n  return setToStorage(STORAGE_KEYS.USERS, users);\n}\n\n// Learning Plans storage functions\nexport function getLearningPlans(): LearningPlan[] {\n  return getFromStorage(STORAGE_KEYS.LEARNING_PLANS, []);\n}\n\nexport function getLearningPlansByChildId(childId: string): LearningPlan[] {\n  const plans = getLearningPlans();\n  return plans.filter(plan => plan.childId === childId);\n}\n\nexport function saveLearningPlan(plan: LearningPlan): boolean {\n  const plans = getLearningPlans();\n  const existingIndex = plans.findIndex(p => p.id === plan.id);\n  \n  if (existingIndex >= 0) {\n    plans[existingIndex] = plan;\n  } else {\n    plans.unshift(plan);\n  }\n  \n  return setToStorage(STORAGE_KEYS.LEARNING_PLANS, plans);\n}\n\n// Data management functions\nexport function exportAllData() {\n  if (!isBrowser) return null;\n\n  return {\n    version: APP_VERSION,\n    exportedAt: new Date().toISOString(),\n    data: {\n      children: getChildren(),\n      assessments: getAssessments(),\n      users: getUsers(),\n      learningPlans: getLearningPlans(),\n    }\n  };\n}\n\nexport function importAllData(data: any): boolean {\n  if (!isBrowser) return false;\n\n  try {\n    if (data.data) {\n      if (data.data.children) setToStorage(STORAGE_KEYS.CHILDREN, data.data.children);\n      if (data.data.assessments) setToStorage(STORAGE_KEYS.ASSESSMENTS, data.data.assessments);\n      if (data.data.users) setToStorage(STORAGE_KEYS.USERS, data.data.users);\n      if (data.data.learningPlans) setToStorage(STORAGE_KEYS.LEARNING_PLANS, data.data.learningPlans);\n      \n      localStorage.setItem(STORAGE_KEYS.APP_VERSION, data.version || APP_VERSION);\n      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());\n      \n      return true;\n    }\n    return false;\n  } catch (error) {\n    console.error('❌ Failed to import data:', error);\n    return false;\n  }\n}\n\nexport function clearAllData(): boolean {\n  if (!isBrowser) return false;\n\n  try {\n    Object.values(STORAGE_KEYS).forEach(key => {\n      localStorage.removeItem(key);\n    });\n    return true;\n  } catch (error) {\n    console.error('❌ Failed to clear data:', error);\n    return false;\n  }\n}\n\n// Get storage info\nexport function getStorageInfo() {\n  if (!isBrowser) return null;\n\n  return {\n    version: localStorage.getItem(STORAGE_KEYS.APP_VERSION),\n    lastSync: localStorage.getItem(STORAGE_KEYS.LAST_SYNC),\n    childrenCount: getChildren().length,\n    assessmentsCount: getAssessments().length,\n    usersCount: getUsers().length,\n    learningPlansCount: getLearningPlans().length,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA,eAAe;AACf,MAAM,eAAe;IACnB,UAAU;IACV,aAAa;IACb,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,WAAW;AACb;AAEA,MAAM,cAAc;AAEpB,0CAA0C;AAC1C,MAAM,YAAY,gBAAkB;AAG7B,SAAS;IACd,wCAAgB;;AAqBlB;AAEA,4BAA4B;AAC5B,SAAS,eAAkB,GAAW,EAAE,YAAe;IACrD,wCAAgB,OAAO;;AASzB;AAEA,SAAS,aAAgB,GAAW,EAAE,KAAQ;IAC5C,wCAAgB,OAAO;;AAUzB;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,QAAQ,EAAE,EAAE;AACjD;AAEO,SAAS,aAAa,EAAU;IACrC,MAAM,WAAW;IACjB,OAAO,SAAS,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC7C;AAEO,SAAS,UAAU,KAAY;IACpC,MAAM,WAAW;IACjB,MAAM,gBAAgB,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;IAE/D,IAAI,iBAAiB,GAAG;QACtB,QAAQ,CAAC,cAAc,GAAG;IAC5B,OAAO;QACL,SAAS,OAAO,CAAC,QAAQ,oCAAoC;IAC/D;IAEA,OAAO,aAAa,aAAa,QAAQ,EAAE;AAC7C;AAEO,SAAS,YAAY,OAAe;IACzC,MAAM,WAAW;IACjB,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEvD,qDAAqD;IACrD,MAAM,cAAc;IACpB,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IAClE,aAAa,aAAa,WAAW,EAAE;IAEvC,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IAC9D,aAAa,aAAa,cAAc,EAAE;IAE1C,OAAO,aAAa,aAAa,QAAQ,EAAE;AAC7C;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,WAAW,EAAE,EAAE;AACpD;AAEO,SAAS,wBAAwB,OAAe;IACrD,MAAM,cAAc;IACpB,OAAO,YAAY,MAAM,CAAC,CAAA,aAAc,WAAW,OAAO,KAAK;AACjE;AAEO,SAAS,kBAAkB,EAAU;IAC1C,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,CAAA,aAAc,WAAW,EAAE,KAAK;AAC1D;AAEO,SAAS,eAAe,UAAsB;IACnD,MAAM,cAAc;IACpB,MAAM,gBAAgB,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;IAEvE,IAAI,iBAAiB,GAAG;QACtB,WAAW,CAAC,cAAc,GAAG;IAC/B,OAAO;QACL,YAAY,OAAO,CAAC,aAAa,uCAAuC;IAC1E;IAEA,OAAO,aAAa,aAAa,WAAW,EAAE;AAChD;AAEO,SAAS,iBAAiB,YAAoB;IACnD,MAAM,cAAc;IACpB,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE7D,qCAAqC;IACrC,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;IACnE,aAAa,aAAa,cAAc,EAAE;IAE1C,OAAO,aAAa,aAAa,WAAW,EAAE;AAChD;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,KAAK,EAAE,EAAE;AAC9C;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AAEO,SAAS,SAAS,IAAU;IACjC,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IAE3D,IAAI,iBAAiB,GAAG;QACtB,KAAK,CAAC,cAAc,GAAG;IACzB,OAAO;QACL,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,aAAa,aAAa,KAAK,EAAE;AAC1C;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,cAAc,EAAE,EAAE;AACvD;AAEO,SAAS,0BAA0B,OAAe;IACvD,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;AAC/C;AAEO,SAAS,iBAAiB,IAAkB;IACjD,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IAE3D,IAAI,iBAAiB,GAAG;QACtB,KAAK,CAAC,cAAc,GAAG;IACzB,OAAO;QACL,MAAM,OAAO,CAAC;IAChB;IAEA,OAAO,aAAa,aAAa,cAAc,EAAE;AACnD;AAGO,SAAS;IACd,wCAAgB,OAAO;;AAYzB;AAEO,SAAS,cAAc,IAAS;IACrC,wCAAgB,OAAO;;AAmBzB;AAEO,SAAS;IACd,wCAAgB,OAAO;;AAWzB;AAGO,SAAS;IACd,wCAAgB,OAAO;;AAUzB", "debugId": null}}, {"offset": {"line": 966, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/hooks/use-storage.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport type { Child, Assessment, User, LearningPlan } from '@/lib/types';\nimport {\n  initializeStorage,\n  getChildren,\n  getChildById,\n  saveChild,\n  deleteChild,\n  getAssessments,\n  getAssessmentsByChildId,\n  getAssessmentById,\n  saveAssessment,\n  deleteAssessment,\n  getUsers,\n  getUserById,\n  saveUser,\n  getLearningPlans,\n  getLearningPlansByChildId,\n  saveLearningPlan,\n  exportAllData,\n  importAllData,\n  clearAllData,\n  getStorageInfo,\n} from '@/lib/storage';\n\n// Custom hook for children management\nexport function useChildren() {\n  const [children, setChildren] = useState<Child[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshChildren = useCallback(() => {\n    setChildren(getChildren());\n  }, []);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshChildren();\n    setLoading(false);\n  }, [refreshChildren]);\n\n  const addChild = useCallback((child: Child) => {\n    const success = saveChild(child);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const updateChild = useCallback((child: Child) => {\n    const success = saveChild(child);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const removeChild = useCallback((childId: string) => {\n    const success = deleteChild(childId);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const getChild = useCallback((childId: string) => {\n    return getChildById(childId);\n  }, []);\n\n  return {\n    children,\n    loading,\n    addChild,\n    updateChild,\n    removeChild,\n    getChild,\n    refreshChildren,\n  };\n}\n\n// Custom hook for assessments management\nexport function useAssessments(childId?: string) {\n  const [assessments, setAssessments] = useState<Assessment[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshAssessments = useCallback(() => {\n    if (childId) {\n      setAssessments(getAssessmentsByChildId(childId));\n    } else {\n      setAssessments(getAssessments());\n    }\n  }, [childId]);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshAssessments();\n    setLoading(false);\n  }, [refreshAssessments]);\n\n  const addAssessment = useCallback((assessment: Assessment) => {\n    const success = saveAssessment(assessment);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const updateAssessment = useCallback((assessment: Assessment) => {\n    const success = saveAssessment(assessment);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const removeAssessment = useCallback((assessmentId: string) => {\n    const success = deleteAssessment(assessmentId);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const getAssessment = useCallback((assessmentId: string) => {\n    return getAssessmentById(assessmentId);\n  }, []);\n\n  return {\n    assessments,\n    loading,\n    addAssessment,\n    updateAssessment,\n    removeAssessment,\n    getAssessment,\n    refreshAssessments,\n  };\n}\n\n// Custom hook for users management\nexport function useUsers() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshUsers = useCallback(() => {\n    setUsers(getUsers());\n  }, []);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshUsers();\n    setLoading(false);\n  }, [refreshUsers]);\n\n  const addUser = useCallback((user: User) => {\n    const success = saveUser(user);\n    if (success) {\n      refreshUsers();\n    }\n    return success;\n  }, [refreshUsers]);\n\n  const updateUser = useCallback((user: User) => {\n    const success = saveUser(user);\n    if (success) {\n      refreshUsers();\n    }\n    return success;\n  }, [refreshUsers]);\n\n  const removeUser = useCallback((userId: string) => {\n    // For now, we'll implement a simple filter-based delete\n    // In a real app, you might want to add a deleteUser function to storage.ts\n    const currentUsers = getUsers();\n    const filteredUsers = currentUsers.filter(u => u.id !== userId);\n\n    try {\n      localStorage.setItem('portage_plus_users', JSON.stringify(filteredUsers));\n      refreshUsers();\n      return true;\n    } catch (error) {\n      console.error('Failed to delete user:', error);\n      return false;\n    }\n  }, [refreshUsers]);\n\n  const getUser = useCallback((userId: string) => {\n    return getUserById(userId);\n  }, []);\n\n  return {\n    users,\n    loading,\n    addUser,\n    updateUser,\n    removeUser,\n    getUser,\n    refreshUsers,\n  };\n}\n\n// Custom hook for learning plans management\nexport function useLearningPlans(childId?: string) {\n  const [learningPlans, setLearningPlans] = useState<LearningPlan[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshLearningPlans = useCallback(() => {\n    if (childId) {\n      setLearningPlans(getLearningPlansByChildId(childId));\n    } else {\n      setLearningPlans(getLearningPlans());\n    }\n  }, [childId]);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshLearningPlans();\n    setLoading(false);\n  }, [refreshLearningPlans]);\n\n  const addLearningPlan = useCallback((plan: LearningPlan) => {\n    const success = saveLearningPlan(plan);\n    if (success) {\n      refreshLearningPlans();\n    }\n    return success;\n  }, [refreshLearningPlans]);\n\n  const updateLearningPlan = useCallback((plan: LearningPlan) => {\n    const success = saveLearningPlan(plan);\n    if (success) {\n      refreshLearningPlans();\n    }\n    return success;\n  }, [refreshLearningPlans]);\n\n  return {\n    learningPlans,\n    loading,\n    addLearningPlan,\n    updateLearningPlan,\n    refreshLearningPlans,\n  };\n}\n\n// Custom hook for data management\nexport function useDataManagement() {\n  const [storageInfo, setStorageInfo] = useState<any>(null);\n\n  const refreshStorageInfo = useCallback(() => {\n    setStorageInfo(getStorageInfo());\n  }, []);\n\n  useEffect(() => {\n    refreshStorageInfo();\n  }, [refreshStorageInfo]);\n\n  const exportData = useCallback(() => {\n    return exportAllData();\n  }, []);\n\n  const importData = useCallback((data: any) => {\n    const success = importAllData(data);\n    if (success) {\n      refreshStorageInfo();\n    }\n    return success;\n  }, [refreshStorageInfo]);\n\n  const clearData = useCallback(() => {\n    const success = clearAllData();\n    if (success) {\n      refreshStorageInfo();\n    }\n    return success;\n  }, [refreshStorageInfo]);\n\n  return {\n    storageInfo,\n    exportData,\n    importData,\n    clearData,\n    refreshStorageInfo,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAwBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IACxB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAgB;IAEpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;QAC1B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;QAC1B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,OAAO,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE;IACtB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,eAAe,OAAgB;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,SAAS;YACX,eAAe,CAAA,GAAA,qHAAA,CAAA,0BAAuB,AAAD,EAAE;QACzC,OAAO;YACL,eAAe,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC9B;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAmB;IAEvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;QAC/B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;QAC/B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,OAAO,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,SAAS,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAClB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,wDAAwD;QACxD,2EAA2E;QAC3E,MAAM,eAAe,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;QAC5B,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAExD,IAAI;YACF,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAC1D;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,OAAO,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;IACrB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,iBAAiB,OAAgB;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,SAAS;YACX,iBAAiB,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD,EAAE;QAC7C,OAAO;YACL,iBAAiB,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD;QAClC;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAqB;IAEzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAqB;IAEzB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAqB;IAEzB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,eAAe,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,OAAO,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD;IACrB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE;QAC9B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QAC3B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/page.tsx"], "sourcesContent": ["\n'use client';\n\nimport Link from 'next/link';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';\nimport { Users, FilePlus2, Activity, BarChart3 as BarChartIcon, UserPlus, UserMinus, TrendingUp, CheckCircle, UserRoundPlus, AlertTriangle, CalendarClock, Info } from 'lucide-react'; // Renamed BarChart3 to BarChartIcon to avoid conflict\nimport Image from 'next/image';\nimport { APP_NAME, AGE_DISTRIBUTION_GROUPS } from '@/lib/constants';\nimport type { Child, Assessment } from '@/lib/types';\nimport { calculateAge, formatDate } from '@/lib/utils';\nimport { differenceInDays, parseISO, isWithinInterval, subDays, addMonths, isPast, isValid } from 'date-fns';\nimport type { ChartConfig } from \"@/components/ui/chart\"; // Keep for ageChartConfig type\nimport AgeDistribution<PERSON>hart from '@/components/dashboard/AgeDistributionChart';\nimport ProgressOverviewChart from '@/components/dashboard/ProgressOverviewChart';\nimport GenderDistributionChart from '@/components/dashboard/GenderDistributionChart'; // Added import\nimport { useChildren, useAssessments } from '@/hooks/use-storage';\n\n\n// Helper to get the latest assessment for a child\nconst getLatestAssessmentForChild = (childId: string, assessments: Assessment[]): Assessment | undefined => {\n  return assessments\n    .filter(a => a.childId === childId)\n    .sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];\n};\n\nexport default function DashboardPage() {\n  const { children, loading: childrenLoading } = useChildren();\n  const { assessments, loading: assessmentsLoading } = useAssessments();\n\n  const loading = childrenLoading || assessmentsLoading;\n\n  const getChildNameById = (childId: string): string | undefined => {\n    const child = children.find(c => c.id === childId);\n    return child?.name;\n  };\n\n  if (loading) {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-muted-foreground\">جاري تحميل بيانات لوحة التحكم...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Calculate analytics data\n  const totalChildren = children.length;\n  const maleChildren = children.filter(c => c.gender === 'male').length;\n  const femaleChildren = children.filter(c => c.gender === 'female').length;\n\n  const today = new Date();\n  const newChildrenCount = children.filter(c => {\n    try {\n      const enrollmentDate = parseISO(c.enrollmentDate);\n      return differenceInDays(today, enrollmentDate) <= 30;\n    } catch (e) {\n      return false;\n    }\n  }).length;\n\n  const servicesEndedCount = children.filter(c => {\n    const age = calculateAge(c.birthDate);\n    return age.years >= 6;\n  }).length;\n\n  const ageDistributionData = AGE_DISTRIBUTION_GROUPS.map(group => ({ ...group, count: 0, fill: `hsl(var(--chart-${(AGE_DISTRIBUTION_GROUPS.indexOf(group) % 5) + 1}))` }));\n  children.forEach(child => {\n    const age = calculateAge(child.birthDate);\n    const ageInMonths = age.years * 12 + age.months;\n    const groupFound = ageDistributionData.find(group => ageInMonths >= group.minMonths && ageInMonths < group.maxMonths);\n    if (groupFound) {\n      groupFound.count++;\n    }\n  });\n\n  const ageChartConfig = {\n    count: {\n      label: \"عدد الأطفال\",\n    },\n    // Dynamically add labels for each age group to the config for the tooltip\n    ...ageDistributionData.reduce((acc, group) => {\n      acc[group.label] = { label: group.label, color: group.fill };\n      return acc;\n    }, {} as Record<string, { label: string; color: string }>)\n  } satisfies ChartConfig;\n\n\n  let childrenWithMasteredGoals = 0;\n  let childrenWithImplementedGoalsOnly = 0;\n  let childrenWithPendingOrNoTrackableGoals = 0;\n\n  children.forEach(child => {\n    const latestAssessment = getLatestAssessmentForChild(child.id, assessments);\n    if (latestAssessment) {\n      const hasMastered = latestAssessment.assessedSkills.some(s => s.progressStatus === 'mastered');\n      const hasImplemented = latestAssessment.assessedSkills.some(s => s.progressStatus === 'implemented');\n      const hasTrackableGoals = latestAssessment.assessedSkills.some(s => s.status === 'no' || s.status === 'unclear');\n\n      if (hasMastered) {\n        childrenWithMasteredGoals++;\n      } else if (hasImplemented) {\n        childrenWithImplementedGoalsOnly++;\n      } else if (hasTrackableGoals) {\n        childrenWithPendingOrNoTrackableGoals++;\n      } else {\n         childrenWithPendingOrNoTrackableGoals++;\n      }\n    } else {\n      childrenWithPendingOrNoTrackableGoals++;\n    }\n  });\n\n  // Recent Activity Data\n  const sevenDaysAgo = subDays(today, 7);\n  const recentlyAddedChildren = children.filter(child => {\n    try {\n      const enrollmentDate = parseISO(child.enrollmentDate);\n      return isWithinInterval(enrollmentDate, { start: sevenDaysAgo, end: today });\n    } catch (e) { return false; }\n  }).sort((a,b) => parseISO(b.enrollmentDate).getTime() - parseISO(a.enrollmentDate).getTime());\n\n  const recentAssessments = assessments.filter(assessment => {\n     try {\n      const assessmentDate = parseISO(assessment.assessmentDate);\n      return isWithinInterval(assessmentDate, { start: sevenDaysAgo, end: today });\n    } catch (e) { return false; }\n  }).sort((a,b) => parseISO(b.assessmentDate).getTime() - parseISO(a.assessmentDate).getTime());\n\n  // --- Logic for Alerts and Notifications Card ---\n  const overdueReassessmentChildren: Child[] = [];\n  const dueSoonReassessmentChildren: { child: Child; dueDate: string }[] = [];\n  const serviceCompletionChildren: Child[] = [];\n  const initialAssessmentNeededChildren: Child[] = [];\n\n  children.forEach(child => {\n    const age = calculateAge(child.birthDate, today.toISOString());\n    let isServiceCompleted = false;\n    if (age.years >= 6) {\n      serviceCompletionChildren.push(child);\n      isServiceCompleted = true;\n    }\n\n    // Only process other alerts if service is not completed\n    if (!isServiceCompleted) {\n      const latestAssessment = getLatestAssessmentForChild(child.id, assessments);\n      if (latestAssessment) {\n        try {\n          const lastAssessmentDate = parseISO(latestAssessment.assessmentDate);\n          if (isValid(lastAssessmentDate)) {\n            const reassessmentDueDate = addMonths(lastAssessmentDate, 4);\n            const formattedDueDate = formatDate(reassessmentDueDate.toISOString());\n\n            if (isPast(reassessmentDueDate)) {\n              overdueReassessmentChildren.push(child);\n            } else {\n              const daysUntilReassessment = differenceInDays(reassessmentDueDate, today);\n              if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {\n                dueSoonReassessmentChildren.push({ child, dueDate: formattedDueDate });\n              }\n            }\n          }\n        } catch (error) {\n          // console.error(\"Error processing assessment date for alerts:\", error);\n        }\n      } else {\n        initialAssessmentNeededChildren.push(child);\n      }\n    }\n  });\n\n\n  return (\n    <div className=\"container mx-auto py-8\">\n      <div className=\"mb-12 text-center\">\n        <h1 className=\"text-4xl font-bold tracking-tight text-primary sm:text-5xl\">\n          مرحباً بكم في {APP_NAME}\n        </h1>\n        <p className=\"mt-4 text-lg leading-8 text-foreground/80\">\n          أداتك الشاملة لتقييم وتنمية الطفولة المبكرة.\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3\">\n        <Card className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Users className=\"h-6 w-6 text-accent\" />\n              إدارة الأطفال\n            </CardTitle>\n            <CardDescription>\n              الوصول إلى ملفات تعريف الأطفال وإدارتها، وتتبع التقدم، وعرض سجل التقييمات.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Image\n              src=\"https://placehold.co/600x400.png\"\n              alt=\"أطفال يلعبون\"\n              width={600}\n              height={400}\n              className=\"rounded-md mb-4\"\n              data-ai-hint=\"children playing\"\n            />\n            <Link href=\"/children\" passHref>\n              <Button className=\"w-full\">\n                الانتقال إلى ملفات تعريف الأطفال\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n\n        {/* Alerts and Notifications Card */}\n        <Card className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CalendarClock className=\"h-6 w-6 text-accent\" />\n              التنبيهات والإشعارات\n            </CardTitle>\n            <CardDescription>\n              متابعة التقييمات الهامة وحالة الأطفال.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-3 text-sm max-h-96 overflow-y-auto\">\n            <Image\n              src=\"https://placehold.co/600x200.png\"\n              alt=\"تنبيهات\"\n              width={600}\n              height={200}\n              className=\"rounded-md mb-3\"\n              data-ai-hint=\"notification bell\"\n            />\n\n            {serviceCompletionChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-yellow-600 dark:text-yellow-400 mb-1 flex items-center gap-1\">\n                  <Info className=\"h-4 w-4\" />\n                  اكتمال الخدمة (محتمل):\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {serviceCompletionChildren.map(c => (\n                    <li key={`completed-${c.id}`}>\n                      <Link href={`/children/${c.id}`} className=\"text-primary hover:underline\">\n                        {c.name}\n                      </Link> - بلغ 6 سنوات أو أكثر.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {overdueReassessmentChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-destructive mb-1 flex items-center gap-1\">\n                  <AlertTriangle className=\"h-4 w-4\" />\n                  إعادة تقييم مطلوبة فورًا:\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {overdueReassessmentChildren.map(c => (\n                    <li key={`overdue-${c.id}`}>\n                      <Link href={`/children/${c.id}/assessment`} className=\"text-primary hover:underline\">\n                        {c.name}\n                      </Link> - آخر تقييم منذ أكثر من 4 أشهر.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {dueSoonReassessmentChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-orange-600 dark:text-orange-400 mb-1 flex items-center gap-1\">\n                  <CalendarClock className=\"h-4 w-4\" />\n                  إعادة تقييم قريبة:\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {dueSoonReassessmentChildren.map(item => (\n                    <li key={`due-soon-${item.child.id}`}>\n                      <Link href={`/children/${item.child.id}/assessment`} className=\"text-primary hover:underline\">\n                        {item.child.name}\n                      </Link> - الموعد المتوقع: {item.dueDate}.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {initialAssessmentNeededChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-blue-600 dark:text-blue-400 mb-1 flex items-center gap-1\">\n                  <FilePlus2 className=\"h-4 w-4\" />\n                  تقييم أولي مطلوب:\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {initialAssessmentNeededChildren.map(c => (\n                    <li key={`initial-${c.id}`}>\n                      <Link href={`/children/${c.id}/assessment/new`} className=\"text-primary hover:underline\">\n                        {c.name}\n                      </Link> - لا يوجد تقييم مسجل.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {overdueReassessmentChildren.length === 0 &&\n             dueSoonReassessmentChildren.length === 0 &&\n             initialAssessmentNeededChildren.length === 0 &&\n             serviceCompletionChildren.length === 0 && (\n              <p className=\"text-muted-foreground text-center py-4\">لا توجد تنبيهات أو إشعارات حاليًا.</p>\n            )}\n          </CardContent>\n        </Card>\n\n        <Card className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Activity className=\"h-6 w-6 text-accent\" />\n              النشاط الأخير (آخر 7 أيام)\n            </CardTitle>\n            <CardDescription>\n              نظرة عامة على أحدث الإضافات والتقييمات.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4 max-h-96 overflow-y-auto\">\n             <Image\n              src=\"https://placehold.co/600x200.png\"\n              alt=\"تقويم أو موجز النشاط\"\n              width={600}\n              height={200}\n              className=\"rounded-md mb-2\"\n              data-ai-hint=\"activity feed\"\n            />\n            {recentlyAddedChildren.length === 0 && recentAssessments.length === 0 ? (\n              <p className=\"text-muted-foreground text-center\">لا يوجد نشاط حديث لعرضه.</p>\n            ) : (\n              <>\n                {recentlyAddedChildren.length > 0 && (\n                  <div className=\"p-2 border-t\">\n                    <h4 className=\"font-semibold text-sm mb-1 flex items-center gap-1\"><UserRoundPlus className=\"h-4 w-4 text-green-500\" /> أطفال أضيفوا حديثًا:</h4>\n                    <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                      {recentlyAddedChildren.map(child => (\n                        <li key={child.id}>\n                          <Link href={`/children/${child.id}`} className=\"text-primary hover:underline\">\n                            {child.name}\n                          </Link> - أضيف في: {formatDate(child.enrollmentDate)}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                {recentAssessments.length > 0 && (\n                  <div className=\"p-2 border-t\">\n                    <h4 className=\"font-semibold text-sm mb-1 flex items-center gap-1\"><CheckCircle className=\"h-4 w-4 text-blue-500\" /> تقييمات مكتملة حديثًا:</h4>\n                    <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                      {recentAssessments.map(assessment => {\n                        const childName = getChildNameById(assessment.childId);\n                        return (\n                          <li key={assessment.id}>\n                            <Link href={`/children/${assessment.childId}/assessment/${assessment.id}`} className=\"text-primary hover:underline\">\n                              تقييم لـ {childName || 'طفل غير معروف'}\n                            </Link> - بتاريخ: {formatDate(assessment.assessmentDate)}\n                          </li>\n                        );\n                      })}\n                    </ul>\n                  </div>\n                )}\n              </>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Comprehensive Data Analysis Card */}\n      <Card className=\"mt-8 shadow-lg hover:shadow-xl transition-shadow duration-300 col-span-1 md:col-span-2 lg:col-span-3\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <BarChartIcon className=\"h-6 w-6 text-accent\" />\n            تحليل بيانات شامل\n          </CardTitle>\n          <CardDescription>\n            نظرة عامة على إحصائيات الأطفال والتقدم المحرز.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6 text-sm\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h4 className=\"font-semibold mb-2 text-primary flex items-center gap-1\"><Users className=\"h-5 w-5\" />نظرة عامة على الأطفال:</h4>\n              <ul className=\"list-disc list-inside space-y-1 pr-5\">\n                <li>إجمالي الأطفال المسجلين: {totalChildren}</li>\n                <li>عدد الذكور: {maleChildren}</li>\n                <li>عدد الإناث: {femaleChildren}</li>\n                <li><UserPlus className=\"inline h-4 w-4 mr-1 text-green-500\" /> أطفال جدد (آخر 30 يومًا): {newChildrenCount}</li>\n                <li><UserMinus className=\"inline h-4 w-4 mr-1 text-red-500\" /> أطفال انتهت خدماتهم (6+ سنوات): {servicesEndedCount}</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2 text-primary flex items-center gap-1\"><TrendingUp className=\"h-5 w-5\" />نظرة عامة على التقدم:</h4>\n              <p className=\"text-xs text-muted-foreground mb-1\">(بناءً على آخر تقييم لكل طفل)</p>\n              <ul className=\"list-disc list-inside space-y-1 pr-5\">\n                <li>أطفال لديهم أهداف أتقنوها: {childrenWithMasteredGoals}</li>\n                <li>أطفال لديهم أهداف قيد التنفيذ (فقط): {childrenWithImplementedGoalsOnly}</li>\n                <li>أطفال لديهم أهداف معلقة أو بدون تقييم: {childrenWithPendingOrNoTrackableGoals}</li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 pt-4\">\n            <div className=\"lg:col-span-1\">\n              <h4 className=\"font-semibold mb-3 text-primary text-center lg:text-right\">توزيع الأعمار:</h4>\n              <AgeDistributionChart data={ageDistributionData} config={ageChartConfig} />\n            </div>\n            <div className=\"lg:col-span-1\">\n              <h4 className=\"font-semibold mb-3 text-primary text-center lg:text-right\">نظرة عامة على تقدم الأهداف:</h4>\n              <ProgressOverviewChart\n                mastered={childrenWithMasteredGoals}\n                implemented={childrenWithImplementedGoalsOnly}\n                pending={childrenWithPendingOrNoTrackableGoals}\n              />\n            </div>\n             <div className=\"lg:col-span-1\">\n              <h4 className=\"font-semibold mb-3 text-primary text-center lg:text-right\">توزيع الأطفال حسب الجنس:</h4>\n              <GenderDistributionChart\n                maleCount={maleChildren}\n                femaleCount={femaleChildren}\n              />\n            </div>\n          </div>\n\n        </CardContent>\n      </Card>\n\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA,oVAAuL,sDAAsD;AAA7O;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA,mRAAsF,eAAe;AACrG;AAfA;;;;;;;;;;;;;;AAkBA,kDAAkD;AAClD,MAAM,8BAA8B,CAAC,SAAiB;IACpD,OAAO,YACJ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAC1B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,GAAG,CAAC,EAAE;AACnG;AAEe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IACzD,MAAM,EAAE,WAAW,EAAE,SAAS,kBAAkB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD;IAElE,MAAM,UAAU,mBAAmB;IAEnC,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,OAAO,OAAO;IAChB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,2BAA2B;IAC3B,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;IACrE,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAEzE,MAAM,QAAQ,IAAI;IAClB,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,IAAI;YACF,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc;YAChD,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,mBAAmB;QACpD,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF,GAAG,MAAM;IAET,MAAM,qBAAqB,SAAS,MAAM,CAAC,CAAA;QACzC,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,EAAE,SAAS;QACpC,OAAO,IAAI,KAAK,IAAI;IACtB,GAAG,MAAM;IAET,MAAM,sBAAsB,uHAAA,CAAA,0BAAuB,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;YAAE,GAAG,KAAK;YAAE,OAAO;YAAG,MAAM,CAAC,gBAAgB,EAAE,AAAC,uHAAA,CAAA,0BAAuB,CAAC,OAAO,CAAC,SAAS,IAAK,EAAE,EAAE,CAAC;QAAC,CAAC;IACvK,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS;QACxC,MAAM,cAAc,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM;QAC/C,MAAM,aAAa,oBAAoB,IAAI,CAAC,CAAA,QAAS,eAAe,MAAM,SAAS,IAAI,cAAc,MAAM,SAAS;QACpH,IAAI,YAAY;YACd,WAAW,KAAK;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;YACL,OAAO;QACT;QACA,0EAA0E;QAC1E,GAAG,oBAAoB,MAAM,CAAC,CAAC,KAAK;YAClC,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG;gBAAE,OAAO,MAAM,KAAK;gBAAE,OAAO,MAAM,IAAI;YAAC;YAC3D,OAAO;QACT,GAAG,CAAC,EAAsD;IAC5D;IAGA,IAAI,4BAA4B;IAChC,IAAI,mCAAmC;IACvC,IAAI,wCAAwC;IAE5C,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,mBAAmB,4BAA4B,MAAM,EAAE,EAAE;QAC/D,IAAI,kBAAkB;YACpB,MAAM,cAAc,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK;YACnF,MAAM,iBAAiB,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK;YACtF,MAAM,oBAAoB,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK;YAEtG,IAAI,aAAa;gBACf;YACF,OAAO,IAAI,gBAAgB;gBACzB;YACF,OAAO,IAAI,mBAAmB;gBAC5B;YACF,OAAO;gBACJ;YACH;QACF,OAAO;YACL;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IACpC,MAAM,wBAAwB,SAAS,MAAM,CAAC,CAAA;QAC5C,IAAI;YACF,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,cAAc;YACpD,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;gBAAE,OAAO;gBAAc,KAAK;YAAM;QAC5E,EAAE,OAAO,GAAG;YAAE,OAAO;QAAO;IAC9B,GAAG,IAAI,CAAC,CAAC,GAAE,IAAM,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO,KAAK,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO;IAE1F,MAAM,oBAAoB,YAAY,MAAM,CAAC,CAAA;QAC1C,IAAI;YACH,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,cAAc;YACzD,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;gBAAE,OAAO;gBAAc,KAAK;YAAM;QAC5E,EAAE,OAAO,GAAG;YAAE,OAAO;QAAO;IAC9B,GAAG,IAAI,CAAC,CAAC,GAAE,IAAM,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO,KAAK,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO;IAE1F,kDAAkD;IAClD,MAAM,8BAAuC,EAAE;IAC/C,MAAM,8BAAmE,EAAE;IAC3E,MAAM,4BAAqC,EAAE;IAC7C,MAAM,kCAA2C,EAAE;IAEnD,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS,EAAE,MAAM,WAAW;QAC3D,IAAI,qBAAqB;QACzB,IAAI,IAAI,KAAK,IAAI,GAAG;YAClB,0BAA0B,IAAI,CAAC;YAC/B,qBAAqB;QACvB;QAEA,wDAAwD;QACxD,IAAI,CAAC,oBAAoB;YACvB,MAAM,mBAAmB,4BAA4B,MAAM,EAAE,EAAE;YAC/D,IAAI,kBAAkB;gBACpB,IAAI;oBACF,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,cAAc;oBACnE,IAAI,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;wBAC/B,MAAM,sBAAsB,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;wBAC1D,MAAM,mBAAmB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,oBAAoB,WAAW;wBAEnE,IAAI,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,EAAE,sBAAsB;4BAC/B,4BAA4B,IAAI,CAAC;wBACnC,OAAO;4BACL,MAAM,wBAAwB,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,qBAAqB;4BACpE,IAAI,yBAAyB,MAAM,yBAAyB,GAAG;gCAC7D,4BAA4B,IAAI,CAAC;oCAAE;oCAAO,SAAS;gCAAiB;4BACtE;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;gBACd,wEAAwE;gBAC1E;YACF,OAAO;gBACL,gCAAgC,IAAI,CAAC;YACvC;QACF;IACF;IAGA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA6D;4BAC1D,uHAAA,CAAA,WAAQ;;;;;;;kCAEzB,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;0BAK3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG3C,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,gBAAa;;;;;;kDAEf,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,QAAQ;kDAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;kCAQjC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGnD,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,gBAAa;;;;;;oCAGd,0BAA0B,MAAM,GAAG,mBAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,8OAAC;gDAAG,WAAU;0DACX,0BAA0B,GAAG,CAAC,CAAA,kBAC7B,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;gEAAE,WAAU;0EACxC,EAAE,IAAI;;;;;;4DACF;;uDAHA,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;;;;;;;;;;;;;;;;oCAUnC,4BAA4B,MAAM,GAAG,mBACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGvC,8OAAC;gDAAG,WAAU;0DACX,4BAA4B,GAAG,CAAC,CAAA,kBAC/B,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC;gEAAE,WAAU;0EACnD,EAAE,IAAI;;;;;;4DACF;;uDAHA,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;;;;;;;;;;;;;;;;oCAUjC,4BAA4B,MAAM,GAAG,mBACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGvC,8OAAC;gDAAG,WAAU;0DACX,4BAA4B,GAAG,CAAC,CAAA,qBAC/B,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC;gEAAE,WAAU;0EAC5D,KAAK,KAAK,CAAC,IAAI;;;;;;4DACX;4DAAoB,KAAK,OAAO;4DAAC;;uDAHjC,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;oCAU3C,gCAAgC,MAAM,GAAG,mBACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGnC,8OAAC;gDAAG,WAAU;0DACX,gCAAgC,GAAG,CAAC,CAAA,kBACnC,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,eAAe,CAAC;gEAAE,WAAU;0EACvD,EAAE,IAAI;;;;;;4DACF;;uDAHA,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;;;;;;;;;;;;;;;;oCAUjC,4BAA4B,MAAM,KAAK,KACvC,4BAA4B,MAAM,KAAK,KACvC,gCAAgC,MAAM,KAAK,KAC3C,0BAA0B,MAAM,KAAK,mBACpC,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;kCAK5D,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG9C,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACpB,8OAAC,6HAAA,CAAA,UAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,gBAAa;;;;;;oCAEd,sBAAsB,MAAM,KAAK,KAAK,kBAAkB,MAAM,KAAK,kBAClE,8OAAC;wCAAE,WAAU;kDAAoC;;;;;6DAEjD;;4CACG,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EAAqD,8OAAC,4NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAA2B;;;;;;;kEACvH,8OAAC;wDAAG,WAAU;kEACX,sBAAsB,GAAG,CAAC,CAAA,sBACzB,8OAAC;;kFACC,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE;wEAAE,WAAU;kFAC5C,MAAM,IAAI;;;;;;oEACN;oEAAa,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,cAAc;;+DAH5C,MAAM,EAAE;;;;;;;;;;;;;;;;4CASxB,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EAAqD,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAA0B;;;;;;;kEACpH,8OAAC;wDAAG,WAAU;kEACX,kBAAkB,GAAG,CAAC,CAAA;4DACrB,MAAM,YAAY,iBAAiB,WAAW,OAAO;4DACrD,qBACE,8OAAC;;kFACC,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAM,CAAC,UAAU,EAAE,WAAW,OAAO,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE;wEAAE,WAAU;;4EAA+B;4EACxG,aAAa;;;;;;;oEAClB;oEAAY,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;;+DAHhD,WAAW,EAAE;;;;;wDAM1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,kNAAA,CAAA,YAAY;wCAAC,WAAU;;;;;;oCAAwB;;;;;;;0CAGlD,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEAA0D,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DACrG,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;4DAA0B;;;;;;;kEAC9B,8OAAC;;4DAAG;4DAAa;;;;;;;kEACjB,8OAAC;;4DAAG;4DAAa;;;;;;;kEACjB,8OAAC;;0EAAG,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAuC;4DAA4B;;;;;;;kEAC3F,8OAAC;;0EAAG,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAqC;4DAAkC;;;;;;;;;;;;;;;;;;;kDAGpG,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEAA0D,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAC1G,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;4DAA4B;;;;;;;kEAChC,8OAAC;;4DAAG;4DAAsC;;;;;;;kEAC1C,8OAAC;;4DAAG;4DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC,uJAAA,CAAA,UAAoB;gDAAC,MAAM;gDAAqB,QAAQ;;;;;;;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC,wJAAA,CAAA,UAAqB;gDACpB,UAAU;gDACV,aAAa;gDACb,SAAS;;;;;;;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC,0JAAA,CAAA,UAAuB;gDACtB,WAAW;gDACX,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7B", "debugId": null}}]}