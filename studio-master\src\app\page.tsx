
'use client';

import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Users, FilePlus2, Activity, BarChart3 as BarChartIcon, UserPlus, UserMinus, TrendingUp, CheckCircle, UserRoundPlus, AlertTriangle, CalendarClock, Info } from 'lucide-react'; // Renamed BarChart3 to BarChartIcon to avoid conflict
import Image from 'next/image';
import { APP_NAME, AGE_DISTRIBUTION_GROUPS } from '@/lib/constants';
import type { Child, Assessment } from '@/lib/types';
import { calculateAge, formatDate } from '@/lib/utils';
import { differenceInDays, parseISO, isWithinInterval, subDays, addMonths, isPast, isValid } from 'date-fns';
import type { ChartConfig } from "@/components/ui/chart"; // Keep for ageChartConfig type
import AgeDistribution<PERSON>hart from '@/components/dashboard/AgeDistributionChart';
import ProgressOverviewChart from '@/components/dashboard/ProgressOverviewChart';
import GenderDistributionChart from '@/components/dashboard/GenderDistributionChart'; // Added import
import { useChildren, useAssessments } from '@/hooks/use-storage';


// Helper to get the latest assessment for a child
const getLatestAssessmentForChild = (childId: string, assessments: Assessment[]): Assessment | undefined => {
  return assessments
    .filter(a => a.childId === childId)
    .sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];
};

export default function DashboardPage() {
  const { children, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments();

  const loading = childrenLoading || assessmentsLoading;

  const getChildNameById = (childId: string): string | undefined => {
    const child = children.find(c => c.id === childId);
    return child?.name;
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات لوحة التحكم...</p>
          </div>
        </div>
      </div>
    );
  }

  // Calculate analytics data
  const totalChildren = children.length;
  const maleChildren = children.filter(c => c.gender === 'male').length;
  const femaleChildren = children.filter(c => c.gender === 'female').length;

  const today = new Date();
  const newChildrenCount = children.filter(c => {
    try {
      const enrollmentDate = parseISO(c.enrollmentDate);
      return differenceInDays(today, enrollmentDate) <= 30;
    } catch (e) {
      return false;
    }
  }).length;

  const servicesEndedCount = children.filter(c => {
    const age = calculateAge(c.birthDate);
    return age.years >= 6;
  }).length;

  const ageDistributionData = AGE_DISTRIBUTION_GROUPS.map(group => ({ ...group, count: 0, fill: `hsl(var(--chart-${(AGE_DISTRIBUTION_GROUPS.indexOf(group) % 5) + 1}))` }));
  children.forEach(child => {
    const age = calculateAge(child.birthDate);
    const ageInMonths = age.years * 12 + age.months;
    const groupFound = ageDistributionData.find(group => ageInMonths >= group.minMonths && ageInMonths < group.maxMonths);
    if (groupFound) {
      groupFound.count++;
    }
  });

  const ageChartConfig = {
    count: {
      label: "عدد الأطفال",
    },
    // Dynamically add labels for each age group to the config for the tooltip
    ...ageDistributionData.reduce((acc, group) => {
      acc[group.label] = { label: group.label, color: group.fill };
      return acc;
    }, {} as Record<string, { label: string; color: string }>)
  } satisfies ChartConfig;


  let childrenWithMasteredGoals = 0;
  let childrenWithImplementedGoalsOnly = 0;
  let childrenWithPendingOrNoTrackableGoals = 0;

  children.forEach(child => {
    const latestAssessment = getLatestAssessmentForChild(child.id, assessments);
    if (latestAssessment) {
      const hasMastered = latestAssessment.assessedSkills.some(s => s.progressStatus === 'mastered');
      const hasImplemented = latestAssessment.assessedSkills.some(s => s.progressStatus === 'implemented');
      const hasTrackableGoals = latestAssessment.assessedSkills.some(s => s.status === 'no' || s.status === 'unclear');

      if (hasMastered) {
        childrenWithMasteredGoals++;
      } else if (hasImplemented) {
        childrenWithImplementedGoalsOnly++;
      } else if (hasTrackableGoals) {
        childrenWithPendingOrNoTrackableGoals++;
      } else {
         childrenWithPendingOrNoTrackableGoals++;
      }
    } else {
      childrenWithPendingOrNoTrackableGoals++;
    }
  });

  // Recent Activity Data
  const sevenDaysAgo = subDays(today, 7);
  const recentlyAddedChildren = children.filter(child => {
    try {
      const enrollmentDate = parseISO(child.enrollmentDate);
      return isWithinInterval(enrollmentDate, { start: sevenDaysAgo, end: today });
    } catch (e) { return false; }
  }).sort((a,b) => parseISO(b.enrollmentDate).getTime() - parseISO(a.enrollmentDate).getTime());

  const recentAssessments = assessments.filter(assessment => {
     try {
      const assessmentDate = parseISO(assessment.assessmentDate);
      return isWithinInterval(assessmentDate, { start: sevenDaysAgo, end: today });
    } catch (e) { return false; }
  }).sort((a,b) => parseISO(b.assessmentDate).getTime() - parseISO(a.assessmentDate).getTime());

  // --- Logic for Alerts and Notifications Card ---
  const overdueReassessmentChildren: Child[] = [];
  const dueSoonReassessmentChildren: { child: Child; dueDate: string }[] = [];
  const serviceCompletionChildren: Child[] = [];
  const initialAssessmentNeededChildren: Child[] = [];

  children.forEach(child => {
    const age = calculateAge(child.birthDate, today.toISOString());
    let isServiceCompleted = false;
    if (age.years >= 6) {
      serviceCompletionChildren.push(child);
      isServiceCompleted = true;
    }

    // Only process other alerts if service is not completed
    if (!isServiceCompleted) {
      const latestAssessment = getLatestAssessmentForChild(child.id, assessments);
      if (latestAssessment) {
        try {
          const lastAssessmentDate = parseISO(latestAssessment.assessmentDate);
          if (isValid(lastAssessmentDate)) {
            const reassessmentDueDate = addMonths(lastAssessmentDate, 4);
            const formattedDueDate = formatDate(reassessmentDueDate.toISOString());

            if (isPast(reassessmentDueDate)) {
              overdueReassessmentChildren.push(child);
            } else {
              const daysUntilReassessment = differenceInDays(reassessmentDueDate, today);
              if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {
                dueSoonReassessmentChildren.push({ child, dueDate: formattedDueDate });
              }
            }
          }
        } catch (error) {
          // console.error("Error processing assessment date for alerts:", error);
        }
      } else {
        initialAssessmentNeededChildren.push(child);
      }
    }
  });


  return (
    <div className="container mx-auto py-8">
      <div className="mb-12 text-center">
        <h1 className="text-4xl font-bold tracking-tight text-primary sm:text-5xl">
          مرحباً بكم في {APP_NAME}
        </h1>
        <p className="mt-4 text-lg leading-8 text-foreground/80">
          أداتك الشاملة لتقييم وتنمية الطفولة المبكرة.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-6 w-6 text-accent" />
              إدارة الأطفال
            </CardTitle>
            <CardDescription>
              الوصول إلى ملفات تعريف الأطفال وإدارتها، وتتبع التقدم، وعرض سجل التقييمات.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Image
              src="https://placehold.co/600x400.png"
              alt="أطفال يلعبون"
              width={600}
              height={400}
              className="rounded-md mb-4"
              data-ai-hint="children playing"
            />
            <Link href="/children" passHref>
              <Button className="w-full">
                الانتقال إلى ملفات تعريف الأطفال
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Alerts and Notifications Card */}
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CalendarClock className="h-6 w-6 text-accent" />
              التنبيهات والإشعارات
            </CardTitle>
            <CardDescription>
              متابعة التقييمات الهامة وحالة الأطفال.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 text-sm max-h-96 overflow-y-auto">
            <Image
              src="https://placehold.co/600x200.png"
              alt="تنبيهات"
              width={600}
              height={200}
              className="rounded-md mb-3"
              data-ai-hint="notification bell"
            />

            {serviceCompletionChildren.length > 0 && (
              <div className="p-2 border-t">
                <h4 className="font-semibold text-yellow-600 dark:text-yellow-400 mb-1 flex items-center gap-1">
                  <Info className="h-4 w-4" />
                  اكتمال الخدمة (محتمل):
                </h4>
                <ul className="list-disc list-inside space-y-1 pr-4 text-xs">
                  {serviceCompletionChildren.map(c => (
                    <li key={`completed-${c.id}`}>
                      <Link href={`/children/${c.id}`} className="text-primary hover:underline">
                        {c.name}
                      </Link> - بلغ 6 سنوات أو أكثر.
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {overdueReassessmentChildren.length > 0 && (
              <div className="p-2 border-t">
                <h4 className="font-semibold text-destructive mb-1 flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  إعادة تقييم مطلوبة فورًا:
                </h4>
                <ul className="list-disc list-inside space-y-1 pr-4 text-xs">
                  {overdueReassessmentChildren.map(c => (
                    <li key={`overdue-${c.id}`}>
                      <Link href={`/children/${c.id}/assessment`} className="text-primary hover:underline">
                        {c.name}
                      </Link> - آخر تقييم منذ أكثر من 4 أشهر.
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {dueSoonReassessmentChildren.length > 0 && (
              <div className="p-2 border-t">
                <h4 className="font-semibold text-orange-600 dark:text-orange-400 mb-1 flex items-center gap-1">
                  <CalendarClock className="h-4 w-4" />
                  إعادة تقييم قريبة:
                </h4>
                <ul className="list-disc list-inside space-y-1 pr-4 text-xs">
                  {dueSoonReassessmentChildren.map(item => (
                    <li key={`due-soon-${item.child.id}`}>
                      <Link href={`/children/${item.child.id}/assessment`} className="text-primary hover:underline">
                        {item.child.name}
                      </Link> - الموعد المتوقع: {item.dueDate}.
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {initialAssessmentNeededChildren.length > 0 && (
              <div className="p-2 border-t">
                <h4 className="font-semibold text-blue-600 dark:text-blue-400 mb-1 flex items-center gap-1">
                  <FilePlus2 className="h-4 w-4" />
                  تقييم أولي مطلوب:
                </h4>
                <ul className="list-disc list-inside space-y-1 pr-4 text-xs">
                  {initialAssessmentNeededChildren.map(c => (
                    <li key={`initial-${c.id}`}>
                      <Link href={`/children/${c.id}/assessment/new`} className="text-primary hover:underline">
                        {c.name}
                      </Link> - لا يوجد تقييم مسجل.
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {overdueReassessmentChildren.length === 0 &&
             dueSoonReassessmentChildren.length === 0 &&
             initialAssessmentNeededChildren.length === 0 &&
             serviceCompletionChildren.length === 0 && (
              <p className="text-muted-foreground text-center py-4">لا توجد تنبيهات أو إشعارات حاليًا.</p>
            )}
          </CardContent>
        </Card>

        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-6 w-6 text-accent" />
              النشاط الأخير (آخر 7 أيام)
            </CardTitle>
            <CardDescription>
              نظرة عامة على أحدث الإضافات والتقييمات.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 max-h-96 overflow-y-auto">
             <Image
              src="https://placehold.co/600x200.png"
              alt="تقويم أو موجز النشاط"
              width={600}
              height={200}
              className="rounded-md mb-2"
              data-ai-hint="activity feed"
            />
            {recentlyAddedChildren.length === 0 && recentAssessments.length === 0 ? (
              <p className="text-muted-foreground text-center">لا يوجد نشاط حديث لعرضه.</p>
            ) : (
              <>
                {recentlyAddedChildren.length > 0 && (
                  <div className="p-2 border-t">
                    <h4 className="font-semibold text-sm mb-1 flex items-center gap-1"><UserRoundPlus className="h-4 w-4 text-green-500" /> أطفال أضيفوا حديثًا:</h4>
                    <ul className="list-disc list-inside space-y-1 pr-4 text-xs">
                      {recentlyAddedChildren.map(child => (
                        <li key={child.id}>
                          <Link href={`/children/${child.id}`} className="text-primary hover:underline">
                            {child.name}
                          </Link> - أضيف في: {formatDate(child.enrollmentDate)}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {recentAssessments.length > 0 && (
                  <div className="p-2 border-t">
                    <h4 className="font-semibold text-sm mb-1 flex items-center gap-1"><CheckCircle className="h-4 w-4 text-blue-500" /> تقييمات مكتملة حديثًا:</h4>
                    <ul className="list-disc list-inside space-y-1 pr-4 text-xs">
                      {recentAssessments.map(assessment => {
                        const childName = getChildNameById(assessment.childId);
                        return (
                          <li key={assessment.id}>
                            <Link href={`/children/${assessment.childId}/assessment/${assessment.id}`} className="text-primary hover:underline">
                              تقييم لـ {childName || 'طفل غير معروف'}
                            </Link> - بتاريخ: {formatDate(assessment.assessmentDate)}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Comprehensive Data Analysis Card */}
      <Card className="mt-8 shadow-lg hover:shadow-xl transition-shadow duration-300 col-span-1 md:col-span-2 lg:col-span-3">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChartIcon className="h-6 w-6 text-accent" />
            تحليل بيانات شامل
          </CardTitle>
          <CardDescription>
            نظرة عامة على إحصائيات الأطفال والتقدم المحرز.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 text-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2 text-primary flex items-center gap-1"><Users className="h-5 w-5" />نظرة عامة على الأطفال:</h4>
              <ul className="list-disc list-inside space-y-1 pr-5">
                <li>إجمالي الأطفال المسجلين: {totalChildren}</li>
                <li>عدد الذكور: {maleChildren}</li>
                <li>عدد الإناث: {femaleChildren}</li>
                <li><UserPlus className="inline h-4 w-4 mr-1 text-green-500" /> أطفال جدد (آخر 30 يومًا): {newChildrenCount}</li>
                <li><UserMinus className="inline h-4 w-4 mr-1 text-red-500" /> أطفال انتهت خدماتهم (6+ سنوات): {servicesEndedCount}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2 text-primary flex items-center gap-1"><TrendingUp className="h-5 w-5" />نظرة عامة على التقدم:</h4>
              <p className="text-xs text-muted-foreground mb-1">(بناءً على آخر تقييم لكل طفل)</p>
              <ul className="list-disc list-inside space-y-1 pr-5">
                <li>أطفال لديهم أهداف أتقنوها: {childrenWithMasteredGoals}</li>
                <li>أطفال لديهم أهداف قيد التنفيذ (فقط): {childrenWithImplementedGoalsOnly}</li>
                <li>أطفال لديهم أهداف معلقة أو بدون تقييم: {childrenWithPendingOrNoTrackableGoals}</li>
              </ul>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 pt-4">
            <div className="lg:col-span-1">
              <h4 className="font-semibold mb-3 text-primary text-center lg:text-right">توزيع الأعمار:</h4>
              <AgeDistributionChart data={ageDistributionData} config={ageChartConfig} />
            </div>
            <div className="lg:col-span-1">
              <h4 className="font-semibold mb-3 text-primary text-center lg:text-right">نظرة عامة على تقدم الأهداف:</h4>
              <ProgressOverviewChart
                mastered={childrenWithMasteredGoals}
                implemented={childrenWithImplementedGoalsOnly}
                pending={childrenWithPendingOrNoTrackableGoals}
              />
            </div>
             <div className="lg:col-span-1">
              <h4 className="font-semibold mb-3 text-primary text-center lg:text-right">توزيع الأطفال حسب الجنس:</h4>
              <GenderDistributionChart
                maleCount={maleChildren}
                femaleCount={femaleChildren}
              />
            </div>
          </div>

        </CardContent>
      </Card>

    </div>
  );
}

