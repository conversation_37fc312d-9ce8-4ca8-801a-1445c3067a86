<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use <PERSON><PERSON>\Jetstream\Jetstream;
use <PERSON><PERSON>\Jetstream\TeamInvitation as JetstreamTeamInvitation;

class TeamInvitation extends JetstreamTeamInvitation
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'email',
        'role',
    ];

    /**
     * Get the team that the invitation belongs to.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Jetstream::teamModel());
    }
}
