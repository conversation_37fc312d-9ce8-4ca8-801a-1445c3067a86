
import type { Child, Assessment } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import AssessmentEditorClientPage from '@/components/assessment/AssessmentEditorClientPage';

async function getChildById(id: string): Promise<Child | undefined> {
  return MOCK_CHILDREN_DATA.find(child => child.id === id);
}

async function getAssessmentById(assessmentId: string): Promise<Assessment | undefined> {
  return MOCK_ASSESSMENTS_DATA.find(assessment => assessment.id === assessmentId);
}

export default async function EditAssessmentPage({ params }: { params: { childId: string, assessmentId: string } }) {
  const child = await getChildById(params.childId);
  const assessment = await getAssessmentById(params.assessmentId);

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على التقييم</h1>
        <Link href={`/children/${params.childId}/assessment`}>
          <Button variant="link">العودة إلى سجل تقييمات الطفل</Button>
        </Link>
      </div>
    );
  }

  return (
     <AssessmentEditorClientPage 
        child={child} 
        portageChecklist={PORTAGE_CHECKLIST_DATA} 
        existingAssessment={assessment} // Pass the assessment to edit
     />
  );
}
