
'use client';

import { useEffect, useState } from 'react';
import type { Child, Assessment } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import AssessmentEditorClientPage from '@/components/assessment/AssessmentEditorClientPage';
import { useChildren, useAssessments } from '@/hooks/use-storage';

export default function EditAssessmentPage({ params }: { params: { childId: string, assessmentId: string } }) {
  const { getChild, loading: childrenLoading } = useChildren();
  const { getAssessment, loading: assessmentsLoading } = useAssessments();

  const [child, setChild] = useState<Child | null>(null);
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Wait for hooks to be ready
    if (childrenLoading || assessmentsLoading) {
      return;
    }

    const childData = getChild(params.childId);
    const assessmentData = getAssessment(params.assessmentId);

    setChild(childData || null);
    setAssessment(assessmentData || null);
    setLoading(false);
  }, [params.childId, params.assessmentId, getChild, getAssessment, childrenLoading, assessmentsLoading]);

  if (loading || childrenLoading || assessmentsLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات التقييم...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على التقييم</h1>
        <Link href={`/children/${params.childId}/assessment`}>
          <Button variant="link">العودة إلى سجل تقييمات الطفل</Button>
        </Link>
      </div>
    );
  }

  return (
     <AssessmentEditorClientPage
        child={child}
        portageChecklist={PORTAGE_CHECKLIST_DATA}
        existingAssessment={assessment} // Pass the assessment to edit
     />
  );
}
