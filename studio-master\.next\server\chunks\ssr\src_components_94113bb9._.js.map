{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,2KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,2KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,2KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,2KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,2KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,2KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,2KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/users/UserListClient.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport type { User, UserRole } from '@/lib/types';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';\nimport { Label } from '@/components/ui/label';\nimport { useToast } from '@/hooks/use-toast';\nimport { PlusCircle, Edit, Trash2, UserCog, Search } from 'lucide-react';\nimport { MOCK_USERS_DATA, USER_ROLE_OPTIONS } from '@/lib/constants';\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport Image from 'next/image'; // For image preview\n\ninterface UserListClientProps {\n  initialUsers: User[];\n  roleOptions: { value: UserRole; label: string }[];\n}\n\nexport default function UserListClient({ initialUsers, roleOptions }: UserListClientProps) {\n  const [users, setUsers] = useState<User[]>(initialUsers.map(u => ({...u})));\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isFormOpen, setIsFormOpen] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const { toast } = useToast();\n\n  const [currentName, setCurrentName] = useState('');\n  const [currentEmail, setCurrentEmail] = useState('');\n  const [currentRole, setCurrentRole] = useState<UserRole | undefined>(undefined);\n  const [currentAvatarUrl, setCurrentAvatarUrl] = useState<string | null>(null);\n  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);\n  const [currentSpecialization, setCurrentSpecialization] = useState(''); // Added state for specialization\n\n  useEffect(() => {\n     MOCK_USERS_DATA.length = 0;\n     users.forEach(u => MOCK_USERS_DATA.push({...u}));\n  }, [users]);\n\n  useEffect(() => {\n    if (editingUser) {\n      setCurrentName(editingUser.name);\n      setCurrentEmail(editingUser.email);\n      setCurrentRole(editingUser.role);\n      setCurrentAvatarUrl(editingUser.avatarUrl || null);\n      setAvatarPreview(editingUser.avatarUrl || null);\n      setCurrentSpecialization(editingUser.specialization || ''); // Set specialization\n    } else {\n      setCurrentName('');\n      setCurrentEmail('');\n      setCurrentRole(undefined);\n      setCurrentAvatarUrl(null);\n      setAvatarPreview(null);\n      setCurrentSpecialization(''); // Reset specialization\n    }\n  }, [editingUser, isFormOpen]);\n\n  const filteredUsers = users.filter(user =>\n    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (user.specialization && user.specialization.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  const handleOpenForm = (user: User | null = null) => {\n    setEditingUser(user);\n    setIsFormOpen(true);\n  };\n\n  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files[0]) {\n      const file = e.target.files[0];\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setCurrentAvatarUrl(reader.result as string);\n        setAvatarPreview(reader.result as string);\n      };\n      reader.readAsDataURL(file);\n    } else {\n       if (editingUser && editingUser.avatarUrl) {\n        // don't clear if just cancelling file selection\n      } else {\n        setCurrentAvatarUrl(null);\n        setAvatarPreview(null);\n      }\n    }\n  };\n\n  const handleRemoveAvatar = () => {\n    setCurrentAvatarUrl(null);\n    setAvatarPreview(null);\n    const fileInput = document.getElementById('avatarFileUser') as HTMLInputElement;\n    if (fileInput) {\n        fileInput.value = \"\";\n    }\n  };\n\n  const handleFormSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!currentName || !currentEmail || !currentRole) {\n      toast({ title: \"خطأ\", description: \"يرجى ملء جميع الحقول المطلوبة (الاسم، البريد الإلكتروني، الدور).\", variant: \"destructive\" });\n      return;\n    }\n\n    let finalAvatarUrl = currentAvatarUrl;\n\n    if (currentAvatarUrl === null && editingUser && editingUser.avatarUrl && avatarPreview === null) {\n      const initials = currentName.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || 'م';\n      finalAvatarUrl = `https://placehold.co/40x40.png?text=${encodeURIComponent(initials)}`;\n    } else if (currentAvatarUrl === null) {\n        const initials = currentName.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || 'م';\n        finalAvatarUrl = `https://placehold.co/40x40.png?text=${encodeURIComponent(initials)}`;\n    }\n\n    if (editingUser) {\n      const updatedUserData: User = {\n        ...editingUser,\n        name: currentName,\n        email: currentEmail,\n        role: currentRole,\n        avatarUrl: finalAvatarUrl || undefined,\n        specialization: currentSpecialization || undefined, // Save specialization\n      };\n      setUsers(prev => prev.map(u => u.id === editingUser.id ? updatedUserData : u));\n      toast({ title: \"نجاح\", description: `تم تحديث بيانات ${currentName} بنجاح.` });\n    } else {\n      const newUser: User = {\n        id: `user-${Date.now()}`,\n        name: currentName,\n        email: currentEmail,\n        role: currentRole,\n        avatarUrl: finalAvatarUrl || undefined,\n        specialization: currentSpecialization || undefined, // Save specialization\n      };\n      setUsers(prev => [newUser, ...prev]);\n      toast({ title: \"نجاح\", description: `تمت إضافة ${currentName} بنجاح.` });\n    }\n    setIsFormOpen(false);\n    setEditingUser(null);\n  };\n\n  const handleDeleteUser = (userId: string) => {\n    setUsers(users.filter(user => user.id !== userId));\n    toast({ title: \"نجاح\", description: \"تم حذف المستخدم.\" });\n  };\n\n\n  return (\n    <div>\n      <div className=\"flex flex-col sm:flex-row justify-between items-center mb-6 gap-4\">\n        <h1 className=\"text-3xl font-bold text-primary flex items-center gap-2\">\n          <UserCog className=\"h-8 w-8\" />\n          إدارة المستخدمين\n        </h1>\n        <div className=\"flex gap-2 items-center w-full sm:w-auto\">\n           <div className=\"relative w-full sm:w-64\">\n            <Search className=\"absolute right-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground md:left-2.5\" />\n            <Input\n              type=\"search\"\n              placeholder=\"البحث بالاسم أو البريد أو التخصص...\"\n              className=\"pr-8 md:pl-8 w-full\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n          <Button onClick={() => handleOpenForm()}>\n            <PlusCircle className=\"ml-2 h-5 w-5\" /> إضافة مستخدم\n          </Button>\n        </div>\n      </div>\n\n      <Dialog open={isFormOpen} onOpenChange={(isOpen) => {\n        setIsFormOpen(isOpen);\n        if (!isOpen) setEditingUser(null);\n      }}>\n        <DialogContent className=\"sm:max-w-[480px]\">\n          <DialogHeader>\n            <DialogTitle>{editingUser ? 'تعديل بيانات المستخدم' : 'إضافة مستخدم جديد'}</DialogTitle>\n          </DialogHeader>\n          <form onSubmit={handleFormSubmit}>\n            <div className=\"grid gap-4 py-4\">\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"name\" className=\"text-right\">الاسم</Label>\n                <Input id=\"name\" value={currentName} onChange={(e) => setCurrentName(e.target.value)} className=\"col-span-3\" required />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"email\" className=\"text-right\">البريد الإلكتروني</Label>\n                <Input id=\"email\" type=\"email\" value={currentEmail} onChange={(e) => setCurrentEmail(e.target.value)} className=\"col-span-3\" required />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"role\" className=\"text-right\">الدور (الصلاحية)</Label>\n                <Select value={currentRole} onValueChange={(value) => setCurrentRole(value as UserRole)} required>\n                  <SelectTrigger id=\"role\" className=\"col-span-3\">\n                    <SelectValue placeholder=\"اختر دور المستخدم\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {roleOptions.map(option => (\n                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"specialization\" className=\"text-right\">التخصص</Label>\n                <Input id=\"specialization\" value={currentSpecialization} onChange={(e) => setCurrentSpecialization(e.target.value)} className=\"col-span-3\" />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"avatarFileUser\" className=\"text-right pt-2\">صورة شخصية</Label>\n                <div className=\"col-span-3 space-y-2\">\n                  <Input id=\"avatarFileUser\" type=\"file\" accept=\"image/*\" onChange={handleAvatarChange} className=\"col-span-3\" />\n                  {avatarPreview && (\n                    <div className=\"relative h-20 w-20\">\n                      <Image src={avatarPreview} alt=\"معاينة الصورة\" layout=\"fill\" objectFit=\"cover\" className=\"rounded-md\" data-ai-hint=\"user avatar\" />\n                    </div>\n                  )}\n                   {avatarPreview && (\n                     <Button type=\"button\" variant=\"ghost\" size=\"sm\" onClick={handleRemoveAvatar} className=\"text-red-500 hover:text-red-700\">\n                        إزالة الصورة\n                      </Button>\n                  )}\n                </div>\n              </div>\n            </div>\n            <DialogFooter>\n              <DialogClose asChild>\n                <Button type=\"button\" variant=\"outline\">إلغاء</Button>\n              </DialogClose>\n              <Button type=\"submit\">{editingUser ? 'حفظ التعديلات' : 'إضافة المستخدم'}</Button>\n            </DialogFooter>\n          </form>\n        </DialogContent>\n      </Dialog>\n\n      {filteredUsers.length > 0 ? (\n        <div className=\"border rounded-lg shadow-md overflow-hidden\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead className=\"w-[80px]\">الصورة</TableHead>\n                <TableHead>الاسم</TableHead>\n                <TableHead>البريد الإلكتروني</TableHead>\n                <TableHead>التخصص</TableHead>\n                <TableHead>الدور</TableHead>\n                <TableHead className=\"text-left\">الإجراءات</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredUsers.map(user => (\n                <TableRow key={user.id}>\n                  <TableCell>\n                    <Avatar className=\"h-10 w-10\">\n                      <AvatarImage src={user.avatarUrl} alt={user.name} data-ai-hint=\"user avatar\" />\n                      <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || 'م'}</AvatarFallback>\n                    </Avatar>\n                  </TableCell>\n                  <TableCell className=\"font-medium\">{user.name}</TableCell>\n                  <TableCell>{user.email}</TableCell>\n                  <TableCell>{user.specialization || '-'}</TableCell>\n                  <TableCell>{roleOptions.find(r => r.value === user.role)?.label || user.role}</TableCell>\n                  <TableCell className=\"text-left\">\n                    <Button variant=\"ghost\" size=\"icon\" onClick={() => handleOpenForm(user)} className=\"text-blue-500 hover:text-blue-700\">\n                      <Edit className=\"h-4 w-4\" />\n                       <span className=\"sr-only\">تعديل</span>\n                    </Button>\n                    <AlertDialog>\n                      <AlertDialogTrigger asChild>\n                        <Button variant=\"ghost\" size=\"icon\" className=\"text-red-500 hover:text-red-700\">\n                          <Trash2 className=\"h-4 w-4\" />\n                          <span className=\"sr-only\">حذف</span>\n                        </Button>\n                      </AlertDialogTrigger>\n                      <AlertDialogContent>\n                        <AlertDialogHeader>\n                          <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>\n                          <AlertDialogDescription>\n                            سيتم حذف المستخدم \"{user.name}\" نهائياً. لا يمكن التراجع عن هذا الإجراء.\n                          </AlertDialogDescription>\n                        </AlertDialogHeader>\n                        <AlertDialogFooter>\n                          <AlertDialogCancel>إلغاء</AlertDialogCancel>\n                          <AlertDialogAction onClick={() => handleDeleteUser(user.id)} className=\"bg-destructive hover:bg-destructive/90\">\n                            حذف\n                          </AlertDialogAction>\n                        </AlertDialogFooter>\n                      </AlertDialogContent>\n                    </AlertDialog>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </div>\n      ) : (\n        <div className=\"text-center py-12 border rounded-lg shadow-sm\">\n          <UserCog className=\"mx-auto h-12 w-12 text-muted-foreground\" />\n          <h3 className=\"mt-2 text-xl font-semibold\">لم يتم العثور على مستخدمين</h3>\n          <p className=\"mt-1 text-sm text-muted-foreground\">\n            {searchTerm ? \"حاول تعديل بحثك.\" : \"ابدأ بإضافة مستخدم جديد.\"}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,4NAAgC,oBAAoB;AAfpD;;;;;;;;;;;;;;;AAsBe,SAAS,eAAe,EAAE,YAAY,EAAE,WAAW,EAAuB;IACvF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,aAAa,GAAG,CAAC,CAAA,IAAK,CAAC;YAAC,GAAG,CAAC;QAAA,CAAC;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,iCAAiC;IAEzG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACP,uHAAA,CAAA,kBAAe,CAAC,MAAM,GAAG;QACzB,MAAM,OAAO,CAAC,CAAA,IAAK,uHAAA,CAAA,kBAAe,CAAC,IAAI,CAAC;gBAAC,GAAG,CAAC;YAAA;IAChD,GAAG;QAAC;KAAM;IAEV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,eAAe,YAAY,IAAI;YAC/B,gBAAgB,YAAY,KAAK;YACjC,eAAe,YAAY,IAAI;YAC/B,oBAAoB,YAAY,SAAS,IAAI;YAC7C,iBAAiB,YAAY,SAAS,IAAI;YAC1C,yBAAyB,YAAY,cAAc,IAAI,KAAK,qBAAqB;QACnF,OAAO;YACL,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,oBAAoB;YACpB,iBAAiB;YACjB,yBAAyB,KAAK,uBAAuB;QACvD;IACF,GAAG;QAAC;QAAa;KAAW;IAE5B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG3F,MAAM,iBAAiB,CAAC,OAAoB,IAAI;QAC9C,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,oBAAoB,OAAO,MAAM;gBACjC,iBAAiB,OAAO,MAAM;YAChC;YACA,OAAO,aAAa,CAAC;QACvB,OAAO;YACJ,IAAI,eAAe,YAAY,SAAS,EAAE;YACzC,gDAAgD;YAClD,OAAO;gBACL,oBAAoB;gBACpB,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,oBAAoB;QACpB,iBAAiB;QACjB,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,WAAW;YACX,UAAU,KAAK,GAAG;QACtB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,cAAc;QAChB,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,aAAa;YACjD,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAoE,SAAS;YAAc;YAC9H;QACF;QAEA,IAAI,iBAAiB;QAErB,IAAI,qBAAqB,QAAQ,eAAe,YAAY,SAAS,IAAI,kBAAkB,MAAM;YAC/F,MAAM,WAAW,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,GAAE,GAAG,WAAW,MAAM;YAChG,iBAAiB,CAAC,oCAAoC,EAAE,mBAAmB,WAAW;QACxF,OAAO,IAAI,qBAAqB,MAAM;YAClC,MAAM,WAAW,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,GAAE,GAAG,WAAW,MAAM;YAChG,iBAAiB,CAAC,oCAAoC,EAAE,mBAAmB,WAAW;QAC1F;QAEA,IAAI,aAAa;YACf,MAAM,kBAAwB;gBAC5B,GAAG,WAAW;gBACd,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,WAAW,kBAAkB;gBAC7B,gBAAgB,yBAAyB;YAC3C;YACA,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE,GAAG,kBAAkB;YAC3E,MAAM;gBAAE,OAAO;gBAAQ,aAAa,CAAC,gBAAgB,EAAE,YAAY,OAAO,CAAC;YAAC;QAC9E,OAAO;YACL,MAAM,UAAgB;gBACpB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,MAAM;gBACN,OAAO;gBACP,MAAM;gBACN,WAAW,kBAAkB;gBAC7B,gBAAgB,yBAAyB;YAC3C;YACA,SAAS,CAAA,OAAQ;oBAAC;uBAAY;iBAAK;YACnC,MAAM;gBAAE,OAAO;gBAAQ,aAAa,CAAC,UAAU,EAAE,YAAY,OAAO,CAAC;YAAC;QACxE;QACA,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1C,MAAM;YAAE,OAAO;YAAQ,aAAa;QAAmB;IACzD;IAGA,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGjC,8OAAC;wBAAI,WAAU;;0CACZ,8OAAC;gCAAI,WAAU;;kDACd,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAGjD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM;;kDACrB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAK7C,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAY,cAAc,CAAC;oBACvC,cAAc;oBACd,IAAI,CAAC,QAAQ,eAAe;gBAC9B;0BACE,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAE,cAAc,0BAA0B;;;;;;;;;;;sCAExD,8OAAC;4BAAK,UAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAO,WAAU;8DAAa;;;;;;8DAC7C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAO,OAAO;oDAAa,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAAG,WAAU;oDAAa,QAAQ;;;;;;;;;;;;sDAEvH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAa;;;;;;8DAC9C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAQ,MAAK;oDAAQ,OAAO;oDAAc,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAAG,WAAU;oDAAa,QAAQ;;;;;;;;;;;;sDAEvI,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAO,WAAU;8DAAa;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAa,eAAe,CAAC,QAAU,eAAe;oDAAoB,QAAQ;;sEAC/F,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,IAAG;4DAAO,WAAU;sEACjC,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;sEACX,YAAY,GAAG,CAAC,CAAA,uBACf,8OAAC,kIAAA,CAAA,aAAU;oEAAoB,OAAO,OAAO,KAAK;8EAAG,OAAO,KAAK;mEAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;sDAKrC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAiB,WAAU;8DAAa;;;;;;8DACvD,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAiB,OAAO;oDAAuB,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;oDAAG,WAAU;;;;;;;;;;;;sDAEhI,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAiB,WAAU;8DAAkB;;;;;;8DAC5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,IAAG;4DAAiB,MAAK;4DAAO,QAAO;4DAAU,UAAU;4DAAoB,WAAU;;;;;;wDAC/F,+BACC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gEAAC,KAAK;gEAAe,KAAI;gEAAgB,QAAO;gEAAO,WAAU;gEAAQ,WAAU;gEAAa,gBAAa;;;;;;;;;;;wDAGrH,+BACC,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAS,SAAQ;4DAAQ,MAAK;4DAAK,SAAS;4DAAoB,WAAU;sEAAkC;;;;;;;;;;;;;;;;;;;;;;;;8CAOlI,8OAAC,kIAAA,CAAA,eAAY;;sDACX,8OAAC,kIAAA,CAAA,cAAW;4CAAC,OAAO;sDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;0DAAU;;;;;;;;;;;sDAE1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAU,cAAc,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM9D,cAAc,MAAM,GAAG,kBACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;kDAChC,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAY;;;;;;;;;;;;;;;;;sCAGrC,8OAAC,iIAAA,CAAA,YAAS;sCACP,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC,iIAAA,CAAA,WAAQ;;sDACP,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDAAC,KAAK,KAAK,SAAS;wDAAE,KAAK,KAAK,IAAI;wDAAE,gBAAa;;;;;;kEAC/D,8OAAC,kIAAA,CAAA,iBAAc;kEAAE,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,GAAE,GAAG,WAAW,MAAM;;;;;;;;;;;;;;;;;sDAGlG,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAe,KAAK,IAAI;;;;;;sDAC7C,8OAAC,iIAAA,CAAA,YAAS;sDAAE,KAAK,KAAK;;;;;;sDACtB,8OAAC,iIAAA,CAAA,YAAS;sDAAE,KAAK,cAAc,IAAI;;;;;;sDACnC,8OAAC,iIAAA,CAAA,YAAS;sDAAE,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,KAAK,IAAI,GAAG,SAAS,KAAK,IAAI;;;;;;sDAC5E,8OAAC,iIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,SAAS,IAAM,eAAe;oDAAO,WAAU;;sEACjF,8OAAC,2MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE7B,8OAAC,2IAAA,CAAA,cAAW;;sEACV,8OAAC,2IAAA,CAAA,qBAAkB;4DAAC,OAAO;sEACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,SAAQ;gEAAQ,MAAK;gEAAO,WAAU;;kFAC5C,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;;;;;;sEAG9B,8OAAC,2IAAA,CAAA,qBAAkB;;8EACjB,8OAAC,2IAAA,CAAA,oBAAiB;;sFAChB,8OAAC,2IAAA,CAAA,mBAAgB;sFAAC;;;;;;sFAClB,8OAAC,2IAAA,CAAA,yBAAsB;;gFAAC;gFACF,KAAK,IAAI;gFAAC;;;;;;;;;;;;;8EAGlC,8OAAC,2IAAA,CAAA,oBAAiB;;sFAChB,8OAAC,2IAAA,CAAA,oBAAiB;sFAAC;;;;;;sFACnB,8OAAC,2IAAA,CAAA,oBAAiB;4EAAC,SAAS,IAAM,iBAAiB,KAAK,EAAE;4EAAG,WAAU;sFAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhC3G,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;qCA6C9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCACV,aAAa,qBAAqB;;;;;;;;;;;;;;;;;;AAM/C", "debugId": null}}]}