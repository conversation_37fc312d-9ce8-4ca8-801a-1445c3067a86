{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,wKAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,6LAAC,wKAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/flows/generate-learning-plan.ts"], "sourcesContent": ["\n'use server';\n\n/**\n * @fileOverview يقوم بإنشاء تقرير شامل ومخصص لطفل بناءً على نتائج التقييم وتحليل البيانات.\n *\n * - generateComprehensiveReport - دالة تقوم بإنشاء التقرير الشامل.\n * - GenerateComprehensiveReportInput - نوع الإدخال لدالة generateComprehensiveReport.\n * - GenerateComprehensiveReportOutput - نوع الإرجاع لدالة generateComprehensiveReport.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst GenerateComprehensiveReportInputSchema = z.object({\n  assessmentResults: z\n    .string()\n    .describe('ملخص لنتائج تقييم الطفل بما في ذلك المهارات المتقنة والتي تحتاج إلى تطوير وأي ملاحظات مهمة.'),\n  zoneOfProximalDevelopment: z\n    .string()\n    .describe(\n      'وصف لمنطقة التطور القريبة للطفل، والتي تشير إلى المهارات التي يكون الطفل مستعدًا لتعلمها بالتوجيه.'\n    ),\n  childName: z.string().describe('اسم الطفل.'),\n  ageInMonths: z.number().describe('عمر الطفل بالأشهر.'),\n  additionalFocus: z.string().optional().describe('أي معلومات أو توجيهات إضافية للمحلل ليأخذها في الاعتبار عند إنشاء التقرير.'),\n});\nexport type GenerateComprehensiveReportInput = z.infer<\n  typeof GenerateComprehensiveReportInputSchema\n>;\n\nconst GenerateComprehensiveReportOutputSchema = z.object({\n  executiveSummary: z.string().describe('ملخص تنفيذي موجز للحالة التنموية الحالية للطفل.'),\n  strengths: z.string().describe('وصف لنقاط القوة الملحوظة لدى الطفل بناءً على التقييم.'),\n  areasForDevelopment: z.string().describe('تحديد المجالات التي تحتاج إلى مزيد من الدعم والتطوير.'),\n  dataAnalysisHighlights: z.string().describe('أبرز الاستنتاجات والرؤى المستخلصة من تحليل بيانات التقييم، مثل الأنماط أو العلاقات بين المهارات المختلفة.'),\n  actionableRecommendations: z.string().describe('توصيات عملية ومحددة يمكن للأهل أو مقدمي الرعاية تطبيقها لدعم نمو الطفل.'),\n});\nexport type GenerateComprehensiveReportOutput = z.infer<\n  typeof GenerateComprehensiveReportOutputSchema\n>;\n\nexport async function generateComprehensiveReport(\n  input: GenerateComprehensiveReportInput\n): Promise<GenerateComprehensiveReportOutput> {\n  return generateComprehensiveReportFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'generateComprehensiveReportPrompt',\n  input: {schema: GenerateComprehensiveReportInputSchema},\n  output: {schema: GenerateComprehensiveReportOutputSchema},\n  prompt: `أنت خبير في تحليل بيانات تقييم نمو الطفولة المبكرة ومتخصص في إعداد تقارير شاملة وقابلة للتنفيذ بناءً على منهج بورتيج.\nمهمتك هي تحليل البيانات المقدمة عن الطفل \"{{{childName}}}\" (العمر: {{{ageInMonths}}} شهرًا) وإنشاء تقرير مفصل.\n\nالبيانات المتاحة:\n1.  **ملخص نتائج التقييم:**\n    {{{assessmentResults}}}\n\n2.  **منطقة التطور القريبة (ZPD):**\n    {{{zoneOfProximalDevelopment}}}\n\n3.  **تركيز إضافي من المستخدم (إن وجد):**\n    {{#if additionalFocus}}\n    {{{additionalFocus}}}\n    {{else}}\n    لا يوجد تركيز إضافي.\n    {{/if}}\n\nالرجاء إنشاء التقرير الشامل بحيث يتضمن الأقسام التالية:\n\n*   **الملخص التنفيذي:** قدم نظرة عامة موجزة ومكثفة عن الوضع النمائي الحالي للطفل.\n*   **نقاط القوة:** استعرض المهارات التي يتقنها الطفل والجوانب الإيجابية التي ظهرت في التقييم.\n*   **مجالات التطوير:** حدد المهارات التي تحتاج إلى دعم وتركيز، ووضح أهميتها في سياق النمو الشامل للطفل.\n*   **أبرز نتائج تحليل البيانات:** هذا قسم بالغ الأهمية. قم بتحليل معمق للبيانات المقدمة (نتائج التقييم، ZPD). ابحث عن أنماط، أو ارتباطات بين أداء الطفل في مجالات مختلفة، أو أي ملاحظات تحليلية مهمة. على سبيل المثال: \"يُظهر الطفل قوة واضحة في المهارات الحركية الدقيقة، بينما تحتاج مهارات التفاعل الاجتماعي إلى دعم أكبر.\" أو \"غالبية المهارات في الفئة العمرية X للتواصل لم يتم إتقانها بعد، مما يشير إلى أهمية التركيز على هذا الجانب.\" أو \"تحليل منطقة النمو القريبة يشير إلى استعداد الطفل للتعامل مع مهام حل المشكلات الأكثر تعقيدًا.\" يجب أن يكون هذا التحليل أكثر من مجرد سرد للمهارات، بل تفسير واستنتاج.\n*   **توصيات عملية:** بناءً على التحليل، قدم توصيات واضحة، محددة، وقابلة للتطبيق يمكن للأهل والمعلمين استخدامها لدعم تطور الطفل في مختلف المجالات. يجب أن تكون هذه التوصيات عملية وموجهة نحو الأنشطة اليومية والبيئة المحيطة بالطفل.\n\nاجعل التقرير منظماً وسهل القراءة.\n`,\n});\n\nconst generateComprehensiveReportFlow = ai.defineFlow(\n  {\n    name: 'generateComprehensiveReportFlow',\n    inputSchema: GenerateComprehensiveReportInputSchema,\n    outputSchema: GenerateComprehensiveReportOutputSchema,\n  },\n  async input => {\n    const {output} = await prompt(input);\n    if (!output) {\n        throw new Error(\"لم يتمكن الذكاء الاصطناعي من إنشاء التقرير الشامل.\");\n    }\n    return output;\n  }\n);\n\n// Ensure the file is renamed to generate-comprehensive-report.ts\n// Old name: generate-learning-plan.ts\n"], "names": [], "mappings": ";;;;;IA0CsB;CAsDtB,iEAAiE;CACjE,sCAAsC", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item.dataKey || item.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;;;AALA;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,MAAM,+BAAiB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WAQpC,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE;;IAChD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,6LAAC;YACC,cAAY;YACZ,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ypBACA;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,6LAAC,sKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;MA/BM;AAiCN,MAAM,eAAe,0JAAA,CAAA,UAAyB;AAE9C,MAAM,oCAAsB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAWzC,CACE,EACE,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACT,EACD;;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;gBACjC,OAAO;YACT;YAEA,MAAM,CAAC,KAAK,GAAG;YACf,MAAM,MAAM,GAAG,YAAY,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,SAAS;YACjE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;YAElB,IAAI,gBAAgB;gBAClB,qBACE,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;8BAC/B,eAAe,OAAO;;;;;;YAG7B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,qBAAO,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAAkB;;;;;;QAC7D;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,6LAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,6LAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;;QAzHqB;;;;QAAA;;;;AA2HvB,oBAAoB,WAAW,GAAG;AAElC,MAAM,cAAc,yJAAA,CAAA,SAAwB;AAE5C,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAQxC,CACE,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,gBAAgB,QAAQ,EAAE,OAAO,EAAE,EAC3E;;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,6LAAC,WAAW,IAAI;;;;6CAEhB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;;QA1CqB;;;;QAAA;;;;AA4CvB,mBAAmB,WAAW,GAAG;AAEjC,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/report/ReportChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { Assessment } from '@/lib/types';\nimport { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';\nimport { Bar, Bar<PERSON>hart, CartesianGrid, XAxis, YAxis, <PERSON>ltip, Legend, ResponsiveContainer } from \"recharts\";\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\nimport { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from \"@/components/ui/chart\";\nimport { formatDate } from '@/lib/utils';\n\ninterface ReportChartProps {\n  assessment: Assessment;\n  childName: string;\n}\n\ninterface ChartDataPoint {\n  dimensionName: string;\n  \"مهارات متقنة\": number;\n  \"مهارات تحتاج تطوير\": number;\n}\n\nconst getSkillDimensionName = (skillId: string): string | null => {\n  for (const dimension of PORTAGE_CHECKLIST_DATA) {\n    for (const subCategory of dimension.subCategories) {\n      const skill = subCategory.skills.find(s => s.id === skillId);\n      if (skill) return dimension.name;\n    }\n  }\n  return null;\n};\n\nexport default function ReportChart({ assessment, childName }: ReportChartProps) {\n  const chartData = PORTAGE_CHECKLIST_DATA.map(dimension => {\n    let achieved = 0;\n    let needsDevelopment = 0;\n\n    assessment.assessedSkills.forEach(assessedSkill => {\n      const skillDimensionName = getSkillDimensionName(assessedSkill.skillId);\n      if (skillDimensionName === dimension.name) {\n        if (assessedSkill.status === 'yes') {\n          achieved++;\n        } else { // 'no' or 'unclear'\n          needsDevelopment++;\n        }\n      }\n    });\n    return {\n      dimensionName: dimension.name,\n      \"مهارات متقنة\": achieved,\n      \"مهارات تحتاج تطوير\": needsDevelopment,\n    };\n  }).filter(d => d[\"مهارات متقنة\"] > 0 || d[\"مهارات تحتاج تطوير\"] > 0);\n\n  const chartConfig = {\n    \"مهارات متقنة\": {\n      label: \"مهارات متقنة\",\n      color: \"hsl(var(--chart-2))\", \n    },\n    \"مهارات تحتاج تطوير\": {\n      label: \"مهارات تحتاج تطوير\",\n      color: \"hsl(var(--chart-5))\", \n    },\n  } satisfies ChartConfig;\n\n  if (chartData.length === 0) {\n    return (\n      <Card className=\"mt-6\">\n        <CardHeader>\n          <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض الرسم البياني لهذا التقييم.</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card className=\"mt-8 shadow-lg\">\n      <CardHeader>\n        <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>\n        <CardDescription>\n          رسم بياني يوضح عدد المهارات المتقنة والتي تحتاج إلى تطوير لكل بُعد في تقييم {childName} بتاريخ {formatDate(assessment.assessmentDate)}.\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <ChartContainer config={chartConfig} className=\"min-h-[300px] w-full aspect-video\">\n          <BarChart \n            data={chartData} \n            layout=\"vertical\" \n            margin={{ right: 20, left: 20, top:5, bottom: 5 }}\n            barCategoryGap=\"20%\"\n          >\n            <CartesianGrid horizontal={false} strokeDasharray=\"3 3\" />\n            <XAxis type=\"number\" allowDecimals={false} stroke=\"hsl(var(--muted-foreground))\" fontSize={12} />\n            <YAxis \n              dataKey=\"dimensionName\" \n              type=\"category\" \n              tickLine={false} \n              axisLine={false}\n              width={180} \n              stroke=\"hsl(var(--muted-foreground))\"\n              tick={{fontSize: 12, fill: 'hsl(var(--foreground))', textAnchor: 'start', dx:5 }} // dx to move text slightly from axis\n              interval={0} // Show all ticks\n            />\n            <ChartTooltip \n                cursor={{fill: 'hsl(var(--muted))'}}\n                content={<ChartTooltipContent indicator=\"dot\" />} \n            />\n            <ChartLegend content={<ChartLegendContent wrapperStyle={{paddingTop: 20}} />} />\n            <Bar dataKey=\"مهارات متقنة\" fill=\"var(--color-مهارات متقنة)\" radius={[0, 4, 4, 0]} />\n            <Bar dataKey=\"مهارات تحتاج تطوير\" fill=\"var(--color-مهارات تحتاج تطوير)\" radius={[0, 4, 4, 0]} />\n          </BarChart>\n        </ChartContainer>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AAoBA,MAAM,wBAAwB,CAAC;IAC7B,KAAK,MAAM,aAAa,0HAAA,CAAA,yBAAsB,CAAE;QAC9C,KAAK,MAAM,eAAe,UAAU,aAAa,CAAE;YACjD,MAAM,QAAQ,YAAY,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,IAAI,OAAO,OAAO,UAAU,IAAI;QAClC;IACF;IACA,OAAO;AACT;AAEe,SAAS,YAAY,EAAE,UAAU,EAAE,SAAS,EAAoB;IAC7E,MAAM,YAAY,0HAAA,CAAA,yBAAsB,CAAC,GAAG,CAAC,CAAA;QAC3C,IAAI,WAAW;QACf,IAAI,mBAAmB;QAEvB,WAAW,cAAc,CAAC,OAAO,CAAC,CAAA;YAChC,MAAM,qBAAqB,sBAAsB,cAAc,OAAO;YACtE,IAAI,uBAAuB,UAAU,IAAI,EAAE;gBACzC,IAAI,cAAc,MAAM,KAAK,OAAO;oBAClC;gBACF,OAAO;oBACL;gBACF;YACF;QACF;QACA,OAAO;YACL,eAAe,UAAU,IAAI;YAC7B,gBAAgB;YAChB,sBAAsB;QACxB;IACF,GAAG,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,qBAAqB,GAAG;IAElE,MAAM,cAAc;QAClB,gBAAgB;YACd,OAAO;YACP,OAAO;QACT;QACA,sBAAsB;YACpB,OAAO;YACP,OAAO;QACT;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kCAAC;;;;;;;;;;;8BAEb,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;;;;;;IAI9D;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,mIAAA,CAAA,kBAAe;;4BAAC;4BAC8D;4BAAU;4BAAS,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;4BAAE;;;;;;;;;;;;;0BAG1I,6LAAC,mIAAA,CAAA,cAAW;0BACV,cAAA,6LAAC,oIAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,uJAAA,CAAA,WAAQ;wBACP,MAAM;wBACN,QAAO;wBACP,QAAQ;4BAAE,OAAO;4BAAI,MAAM;4BAAI,KAAI;4BAAG,QAAQ;wBAAE;wBAChD,gBAAe;;0CAEf,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,YAAY;gCAAO,iBAAgB;;;;;;0CAClD,6LAAC,wJAAA,CAAA,QAAK;gCAAC,MAAK;gCAAS,eAAe;gCAAO,QAAO;gCAA+B,UAAU;;;;;;0CAC3F,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,UAAU;gCACV,OAAO;gCACP,QAAO;gCACP,MAAM;oCAAC,UAAU;oCAAI,MAAM;oCAA0B,YAAY;oCAAS,IAAG;gCAAE;gCAC/E,UAAU;;;;;;0CAEZ,6LAAC,oIAAA,CAAA,eAAY;gCACT,QAAQ;oCAAC,MAAM;gCAAmB;gCAClC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;oCAAC,WAAU;;;;;;;;;;;0CAE5C,6LAAC,oIAAA,CAAA,cAAW;gCAAC,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;oCAAC,cAAc;wCAAC,YAAY;oCAAE;;;;;;;;;;;0CACvE,6LAAC,sJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAe,MAAK;gCAA4B,QAAQ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;;;;;;0CACjF,6LAAC,sJAAA,CAAA,MAAG;gCAAC,SAAQ;gCAAqB,MAAK;gCAAkC,QAAQ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzG;KAtFwB", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/learning-plan/LearningPlanGeneratorForm.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { useState, useMemo, useRef } from 'react';\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useForm } from \"react-hook-form\";\nimport { z } from \"zod\";\nimport type { Child, Assessment, CalculatedAge } from '@/lib/types';\nimport type { DimensionAnalysisData } from '@/app/children/[childId]/plan/page'; // Import the new type\nimport { Button } from '@/components/ui/button';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'; // For displaying structured analysis\nimport { useToast } from '@/hooks/use-toast';\nimport { generateComprehensiveReport, GenerateComprehensiveReportInput, GenerateComprehensiveReportOutput } from '@/ai/flows/generate-learning-plan'; // Corrected import path\nimport { calculateAge, formatDate } from '@/lib/utils';\nimport { Loader2, FileText, Printer } from 'lucide-react'; // Added FileText for icon & Printer\nimport { PORTAGE_CHECKLIST_DATA, SKILL_STATUS_OPTIONS } from '@/lib/constants';\nimport ReportChart from '@/components/report/ReportChart';\n\n\nconst formSchema = z.object({\n  additionalFocus: z.string().optional().describe(\"أي معلومات إضافية أو توجيهات للمحلل ليأخذها في الاعتبار عند إنشاء التقرير.\"),\n});\n\ninterface ComprehensiveReportGeneratorFormProps {\n  child: Child;\n  assessment?: Assessment; // Make assessment optional\n  structuredAnalysisData?: DimensionAnalysisData[]; // New prop for structured analysis\n}\n\nconst deriveAiInputs = (assessment: Assessment, childAgeInMonths: number, childName: string) => {\n  let assessmentResultsSummary = `ملخص تقييم لـ ${childName} أُجري بتاريخ ${formatDate(assessment.assessmentDate)}. العمر عند التقييم: ${childAgeInMonths} شهرًا.\\nالمهارات المقيمة وملاحظات رئيسية:\\n`;\n  let zpdSkills: string[] = [];\n\n  assessment.assessedSkills.forEach(assessedSkill => {\n    const skillDetail = PORTAGE_CHECKLIST_DATA.flatMap(dim => dim.subCategories.flatMap(sc => sc.skills)).find(s => s.id === assessedSkill.skillId);\n    if (skillDetail) {\n      const statusLabel = SKILL_STATUS_OPTIONS.find(s => s.value === assessedSkill.status)?.label || assessedSkill.status;\n      assessmentResultsSummary += `- المهارة: \"${skillDetail.behavior}\" (الفئة العمرية: ${skillDetail.ageRange}). الحالة: ${statusLabel}.${assessedSkill.notes ? ` ملاحظة: ${assessedSkill.notes}` : ''}\\n`;\n      \n      if (assessedSkill.status === 'no' || assessedSkill.status === 'unclear') {\n        zpdSkills.push(`\"${skillDetail.behavior}\" (الفئة العمرية: ${skillDetail.ageRange})`);\n      }\n    }\n  });\n\n  if (zpdSkills.length === 0) {\n    zpdSkills.push(\"لم يتم تحديد مهارات معينة بشكل واضح كأهداف مباشرة ضمن منطقة النمو القريبة من هذا التقييم، ولكن يمكن التركيز على المهارات التالية المناسبة للعمر والتي لم تُتقن بعد.\");\n  }\n  \n  const zoneOfProximalDevelopment = `المهارات التي يبدو الطفل مستعدًا لتعلمها أو تطويرها أكثر مع التوجيه والدعم (منطقة النمو القريبة) تشمل: ${zpdSkills.join('، ')}. يجب التركيز على الأنشطة التي تبني هذه المهارات أو المهارات التمهيدية لها.`;\n  \n  return { assessmentResults: assessmentResultsSummary, zoneOfProximalDevelopment };\n};\n\n\nexport default function ComprehensiveReportGeneratorForm({ child, assessment, structuredAnalysisData }: ComprehensiveReportGeneratorFormProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [generatedReport, setGeneratedReport] = useState<GenerateComprehensiveReportOutput | null>(null);\n  const { toast } = useToast();\n  const reportPrintContentRef = useRef<HTMLDivElement>(null);\n\n\n  const form = useForm<z.infer<typeof formSchema>>({\n    resolver: zodResolver(formSchema),\n    defaultValues: {\n      additionalFocus: \"\",\n    },\n  });\n  \n  const childAge: CalculatedAge | null = useMemo(() => {\n    if (!assessment) return null;\n    return calculateAge(child.birthDate, assessment.assessmentDate);\n  }, [child.birthDate, assessment]);\n\n  const childAgeInMonths = childAge ? childAge.years * 12 + childAge.months : 0;\n\n  const { assessmentResults, zoneOfProximalDevelopment } = useMemo(() => {\n      if (!assessment) return { assessmentResults: \"\", zoneOfProximalDevelopment: \"\" };\n      return deriveAiInputs(assessment, childAgeInMonths, child.name);\n    }, [assessment, childAgeInMonths, child.name]\n  );\n\n  async function onSubmit(values: z.infer<typeof formSchema>) {\n    if (!assessment) {\n        toast({ title: \"خطأ\", description: \"لا يوجد تقييم لتوليد التقرير بناءً عليه.\", variant: \"destructive\"});\n        return;\n    }\n    setIsLoading(true);\n    setGeneratedReport(null);\n\n    const aiInput: GenerateComprehensiveReportInput = {\n      assessmentResults: assessmentResults,\n      zoneOfProximalDevelopment,\n      childName: child.name,\n      ageInMonths: childAgeInMonths,\n      additionalFocus: values.additionalFocus,\n    };\n\n    try {\n      const result = await generateComprehensiveReport(aiInput);\n      setGeneratedReport(result);\n      toast({ title: \"نجاح!\", description: \"تم إنشاء التقرير الشامل.\" });\n    } catch (error) {\n      console.error(\"Error generating comprehensive report:\", error);\n      toast({\n        title: \"خطأ\",\n        description: \"فشل في إنشاء التقرير الشامل. يرجى المحاولة مرة أخرى.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  }\n\n  const getReportTextToCopy = () => {\n    if (!generatedReport || !assessment) return \"\";\n    return `\nالتقرير الشامل لـ ${child.name}\nتاريخ التقييم: ${formatDate(assessment.assessmentDate)}\nالعمر عند التقييم: ${childAge?.years} سنوات, ${childAge?.months} أشهر, ${childAge?.days} أيام\n\nالملخص التنفيذي:\n${generatedReport.executiveSummary}\n\nنقاط القوة:\n${generatedReport.strengths}\n\nمجالات التطوير:\n${generatedReport.areasForDevelopment}\n\nأبرز نتائج تحليل البيانات:\n${generatedReport.dataAnalysisHighlights}\n\nتوصيات عملية:\n${generatedReport.actionableRecommendations}\n    `.trim();\n  };\n\n  const handlePrintReport = () => {\n    if (!reportPrintContentRef.current || !assessment) {\n      toast({ title: \"خطأ في الطباعة\", description: \"لم يتم العثور على محتوى التقرير للطباعة.\", variant: \"destructive\" });\n      return;\n    }\n    const title = `التقرير الشامل لـ ${child.name} - تاريخ ${formatDate(assessment.assessmentDate)}`;\n    const printWindow = window.open('', '_blank');\n    if (printWindow) {\n      printWindow.document.write('<html><head><title>' + title + '</title>');\n      printWindow.document.write(`\n        <style>\n          body { font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif; direction: rtl; padding: 20px; margin: 0; }\n          h1, h2, h3, h4, h5, h6 { color: #333; margin-bottom: 0.5em; margin-top: 1em; }\n          p, pre { margin-bottom: 1em; line-height: 1.6; white-space: pre-wrap; font-family: inherit; }\n          pre { background-color: #f9f9f9; border: 1px solid #eee; padding: 10px; border-radius: 4px; }\n          .report-card-header { border-bottom: 1px solid #eee; margin-bottom: 15px; padding-bottom: 10px; }\n          .report-section h3, .analysis-section h3 { font-weight: bold; color: hsl(var(--primary)); margin-bottom: 0.5em; }\n          .analysis-section h4 { font-weight: medium; color: hsl(var(--accent)); margin-bottom: 0.25em; margin-top: 0.75em; }\n          .print-chart-container { page-break-inside: avoid; margin-top: 20px; border: 1px solid #ccc; padding:10px; border-radius: 5px; }\n          img { max-width: 100%; height: auto; } /* Ensure images (like charts if rendered as img) are responsive */\n          ul { list-style-position: inside; padding-right: 1.5rem; } /* RTL list styling */\n          .no-print { display: none !important; }\n          @media print {\n            body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }\n            @page { margin: 20mm; }\n            button, .no-print, .card-footer.no-print { display: none !important; }\n             /* Ensure structured analysis and AI report sections are not broken across pages if possible */\n            .report-section, .analysis-section > div { page-break-inside: avoid; }\n            .print-chart-container { display: block; page-break-inside: avoid; }\n          }\n        </style>\n      `);\n      printWindow.document.write('</head><body dir=\"rtl\">');\n      printWindow.document.write('<h1>' + title + '</h1>');\n      \n      const reportContentClone = reportPrintContentRef.current.cloneNode(true) as HTMLElement;\n      \n      reportContentClone.querySelectorAll('.no-print, form, button, .card-footer.no-print').forEach(el => el.remove());\n\n\n      printWindow.document.write(reportContentClone.innerHTML);\n      printWindow.document.write('</body></html>');\n      printWindow.document.close();\n      printWindow.focus();\n      printWindow.print();\n    } else {\n      toast({ title: \"خطأ\", description: \"يرجى السماح بالنوافذ المنبثقة لطباعة هذا المحتوى.\", variant: \"destructive\"});\n    }\n  };\n\n\n  return (\n    <div className=\"space-y-6\">\n      {!assessment ? (\n         <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle>إنشاء تقرير شامل</CardTitle>\n              <CardDescription>\n                لم يتم العثور على تقييمات لـ {child.name}. يتطلب إنشاء التقرير بيانات تقييم.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center p-8\">\n                <p className=\"mb-4 text-lg\">يرجى إكمال تقييم لـ {child.name} أولاً.</p>\n                <Link href={`/children/${child.id}/assessment/new`}>\n                  <Button>بدء تقييم جديد</Button>\n                </Link>\n              </div>\n            </CardContent>\n         </Card>\n      ) : (\n        <>\n          {/* Structured Analysis Display Card */}\n          {structuredAnalysisData && structuredAnalysisData.length > 0 && (\n            <Card className=\"shadow-lg\">\n              <CardHeader>\n                <CardTitle>تحليل مفصل لآخر تقييم</CardTitle>\n                <CardDescription>\n                  تاريخ التقييم: {formatDate(assessment.assessmentDate)}. يوضح هذا القسم خط الأساس، الخط السقفي، ومنطقة التعلم المقترح لكل بُعد ومجال فرعي.\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Accordion type=\"multiple\" className=\"w-full\" defaultValue={structuredAnalysisData.map(d => d.dimensionName)}>\n                  {structuredAnalysisData.map(dimensionData => (\n                    <AccordionItem value={dimensionData.dimensionName} key={dimensionData.dimensionName}>\n                      <AccordionTrigger className=\"text-xl font-semibold text-primary\">{dimensionData.dimensionName}</AccordionTrigger>\n                      <AccordionContent>\n                        {dimensionData.subCategories.map(subCategoryData => (\n                          <div key={subCategoryData.subCategoryName} className=\"mb-4 p-3 border rounded-md bg-muted/50\">\n                            <h4 className=\"text-lg font-medium text-accent mb-2\">{subCategoryData.subCategoryName}</h4>\n                            <p><strong>الخط القاعدي:</strong> {subCategoryData.baselineSkill ? `${subCategoryData.baselineSkill.itemNumber}. ${subCategoryData.baselineSkill.behavior}` : \"لم يتم تحديده\"}</p>\n                            <p><strong>الخط السقفي:</strong> {subCategoryData.ceilingSkill ? `${subCategoryData.ceilingSkill.itemNumber}. ${subCategoryData.ceilingSkill.behavior}` : \"لم يتم تحديده\"}</p>\n                            {subCategoryData.teachingRangeSkills.length > 0 ? (\n                              <div>\n                                <h5 className=\"font-semibold mt-2\">منطقة التعلم (المهارات المقترحة للتركيز عليها):</h5>\n                                <ul className=\"list-disc pr-5 mt-1 space-y-1 text-sm\">\n                                  {subCategoryData.teachingRangeSkills.map(skill => (\n                                    <li key={skill.skillId}>{skill.itemNumber}. {skill.behavior} ({skill.ageRange})</li>\n                                  ))}\n                                </ul>\n                              </div>\n                            ) : (\n                              <p className=\"mt-2 text-sm text-muted-foreground\">لا توجد مهارات محددة في منطقة التعلم بناءً على هذا التحليل.</p>\n                            )}\n                          </div>\n                        ))}\n                        {dimensionData.subCategories.length === 0 && <p className=\"text-sm text-muted-foreground\">لا توجد بيانات تحليل لهذه الفئة الفرعية ضمن هذا البعد في التقييم.</p>}\n                      </AccordionContent>\n                    </AccordionItem>\n                  ))}\n                </Accordion>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* AI Report Generation Form and Display */}\n          <Card className=\"shadow-lg\">\n            <CardHeader>\n              <CardTitle>إنشاء تقرير شامل مدعوم بالذكاء الاصطناعي</CardTitle>\n              <CardDescription>\n                بناءً على التقييم من تاريخ {formatDate(assessment.assessmentDate)}. يمكنك تحديد تركيز إضافي للتحليل إذا لزم الأمر.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n                <div>\n                  <Label htmlFor=\"additionalFocus\">تركيز/ملاحظات إضافية للتحليل (اختياري)</Label>\n                  <Textarea\n                    id=\"additionalFocus\"\n                    placeholder=\"مثال: يُظهر الطفل اهتمامًا قويًا بالحيوانات، أو، التركيز على مهارات التفاعل الاجتماعي بشكل خاص.\"\n                    {...form.register(\"additionalFocus\")}\n                    className=\"mt-1\"\n                  />\n                </div>\n                \n                <Button type=\"submit\" disabled={isLoading} className=\"w-full sm:w-auto no-print\">\n                  {isLoading ? (\n                    <>\n                      <Loader2 className=\"ml-2 h-4 w-4 animate-spin\" />\n                      جارٍ إنشاء التقرير...\n                    </>\n                  ) : (\n                    <>\n                      <FileText className=\"ml-2 h-4 w-4\" />\n                      إنشاء التقرير الشامل\n                    </>\n                  )}\n                </Button>\n              </form>\n\n              {generatedReport && (\n                <div ref={reportPrintContentRef} className=\"mt-6\"> {/* Main div for printing */}\n                  <div className=\"report-card-header no-print\"> {/* This header won't be printed */}\n                    <h2 className=\"text-2xl font-bold text-primary\">التقرير الشامل لـ {child.name}</h2>\n                    <p className=\"text-muted-foreground\">\n                      تم إنشاء هذا التقرير بواسطة الذكاء الاصطناعي بناءً على بيانات التقييم.\n                    </p>\n                  </div>\n                  <div className=\"space-y-4 mt-4\"> {/* Content to be printed */}\n                    <div className=\"report-section\">\n                      <h3 className=\"text-lg font-semibold text-primary mb-1\">الملخص التنفيذي</h3>\n                      <pre className=\"whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border\">{generatedReport.executiveSummary}</pre>\n                    </div>\n                    <div className=\"report-section\">\n                      <h3 className=\"text-lg font-semibold text-primary mb-1\">نقاط القوة</h3>\n                      <pre className=\"whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border\">{generatedReport.strengths}</pre>\n                    </div>\n                    <div className=\"report-section\">\n                      <h3 className=\"text-lg font-semibold text-primary mb-1\">مجالات التطوير</h3>\n                      <pre className=\"whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border\">{generatedReport.areasForDevelopment}</pre>\n                    </div>\n                    <div className=\"report-section\">\n                      <h3 className=\"text-lg font-semibold text-primary mb-1\">أبرز نتائج تحليل البيانات</h3>\n                      <pre className=\"whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border\">{generatedReport.dataAnalysisHighlights}</pre>\n                    </div>\n                    <div className=\"report-section\">\n                      <h3 className=\"text-lg font-semibold text-primary mb-1\">توصيات عملية</h3>\n                      <pre className=\"whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border\">{generatedReport.actionableRecommendations}</pre>\n                    </div>\n                  </div>\n                  <div className=\"print-chart-container mt-6\">\n                    <ReportChart assessment={assessment} childName={child.name} />\n                  </div>\n                </div>\n              )}\n            </CardContent>\n            {generatedReport && assessment && (\n                <CardFooter className=\"gap-2 no-print\">\n                    <Button variant=\"outline\" onClick={() => navigator.clipboard.writeText(getReportTextToCopy()).then(() => toast({description: \"تم نسخ التقرير إلى الحافظة!\"}))}>\n                    نسخ التقرير\n                    </Button>\n                    <Button variant=\"outline\" onClick={handlePrintReport} disabled={isLoading}>\n                    <Printer className=\"ml-2 h-4 w-4\" /> طباعة التقرير\n                    </Button>\n                </CardFooter>\n            )}\n          </Card>\n        </>\n      )}\n    </div>\n  );\n}\n\n// Ensure file is renamed to ComprehensiveReportGeneratorForm.tsx\n// Old name: LearningPlanGeneratorForm.tsx\n\n    "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA,+OAA0G,qCAAqC;AAC/I;AACA,mQAAsJ,wBAAwB;AAC9K;AACA,iXAA2D,oCAAoC;AAA/F;AAAA;AACA;AACA;;;AAlBA;;;;;;;;;;;;;;;;AAqBA,MAAM,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAClD;AAQA,MAAM,iBAAiB,CAAC,YAAwB,kBAA0B;IACxE,IAAI,2BAA2B,CAAC,cAAc,EAAE,UAAU,cAAc,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc,EAAE,qBAAqB,EAAE,iBAAiB,4CAA4C,CAAC;IACrM,IAAI,YAAsB,EAAE;IAE5B,WAAW,cAAc,CAAC,OAAO,CAAC,CAAA;QAChC,MAAM,cAAc,0HAAA,CAAA,yBAAsB,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,aAAa,CAAC,OAAO,CAAC,CAAA,KAAM,GAAG,MAAM,GAAG,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,OAAO;QAC9I,IAAI,aAAa;YACf,MAAM,cAAc,0HAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,cAAc,MAAM,GAAG,SAAS,cAAc,MAAM;YACnH,4BAA4B,CAAC,YAAY,EAAE,YAAY,QAAQ,CAAC,kBAAkB,EAAE,YAAY,QAAQ,CAAC,WAAW,EAAE,YAAY,CAAC,EAAE,cAAc,KAAK,GAAG,CAAC,SAAS,EAAE,cAAc,KAAK,EAAE,GAAG,GAAG,EAAE,CAAC;YAErM,IAAI,cAAc,MAAM,KAAK,QAAQ,cAAc,MAAM,KAAK,WAAW;gBACvE,UAAU,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,QAAQ,CAAC,kBAAkB,EAAE,YAAY,QAAQ,CAAC,CAAC,CAAC;YACrF;QACF;IACF;IAEA,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,UAAU,IAAI,CAAC;IACjB;IAEA,MAAM,4BAA4B,CAAC,uGAAuG,EAAE,UAAU,IAAI,CAAC,MAAM,2EAA2E,CAAC;IAE7O,OAAO;QAAE,mBAAmB;QAA0B;IAA0B;AAClF;AAGe,SAAS,iCAAiC,EAAE,KAAK,EAAE,UAAU,EAAE,sBAAsB,EAAyC;;IAC3I,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IACjG,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAGrD,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAA8B;QAC/C,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,iBAAiB;QACnB;IACF;IAEA,MAAM,WAAiC,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8DAAE;YAC7C,IAAI,CAAC,YAAY,OAAO;YACxB,OAAO,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS,EAAE,WAAW,cAAc;QAChE;6DAAG;QAAC,MAAM,SAAS;QAAE;KAAW;IAEhC,MAAM,mBAAmB,WAAW,SAAS,KAAK,GAAG,KAAK,SAAS,MAAM,GAAG;IAE5E,MAAM,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE;YAC7D,IAAI,CAAC,YAAY,OAAO;gBAAE,mBAAmB;gBAAI,2BAA2B;YAAG;YAC/E,OAAO,eAAe,YAAY,kBAAkB,MAAM,IAAI;QAChE;mDAAG;QAAC;QAAY;QAAkB,MAAM,IAAI;KAAC;IAG/C,eAAe,SAAS,MAAkC;QACxD,IAAI,CAAC,YAAY;YACb,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA4C,SAAS;YAAa;YACrG;QACJ;QACA,aAAa;QACb,mBAAmB;QAEnB,MAAM,UAA4C;YAChD,mBAAmB;YACnB;YACA,WAAW,MAAM,IAAI;YACrB,aAAa;YACb,iBAAiB,OAAO,eAAe;QACzC;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,8BAA2B,AAAD,EAAE;YACjD,mBAAmB;YACnB,MAAM;gBAAE,OAAO;gBAAS,aAAa;YAA2B;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,mBAAmB,CAAC,YAAY,OAAO;QAC5C,OAAO,CAAC;kBACM,EAAE,MAAM,IAAI,CAAC;eAChB,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc,EAAE;mBACpC,EAAE,UAAU,MAAM,QAAQ,EAAE,UAAU,OAAO,OAAO,EAAE,UAAU,KAAK;;;AAGxF,EAAE,gBAAgB,gBAAgB,CAAC;;;AAGnC,EAAE,gBAAgB,SAAS,CAAC;;;AAG5B,EAAE,gBAAgB,mBAAmB,CAAC;;;AAGtC,EAAE,gBAAgB,sBAAsB,CAAC;;;AAGzC,EAAE,gBAAgB,yBAAyB,CAAC;IACxC,CAAC,CAAC,IAAI;IACR;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,sBAAsB,OAAO,IAAI,CAAC,YAAY;YACjD,MAAM;gBAAE,OAAO;gBAAkB,aAAa;gBAA4C,SAAS;YAAc;YACjH;QACF;QACA,MAAM,QAAQ,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc,GAAG;QAChG,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,aAAa;YACf,YAAY,QAAQ,CAAC,KAAK,CAAC,wBAAwB,QAAQ;YAC3D,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;MAsB5B,CAAC;YACD,YAAY,QAAQ,CAAC,KAAK,CAAC;YAC3B,YAAY,QAAQ,CAAC,KAAK,CAAC,SAAS,QAAQ;YAE5C,MAAM,qBAAqB,sBAAsB,OAAO,CAAC,SAAS,CAAC;YAEnE,mBAAmB,gBAAgB,CAAC,kDAAkD,OAAO,CAAC,CAAA,KAAM,GAAG,MAAM;YAG7G,YAAY,QAAQ,CAAC,KAAK,CAAC,mBAAmB,SAAS;YACvD,YAAY,QAAQ,CAAC,KAAK,CAAC;YAC3B,YAAY,QAAQ,CAAC,KAAK;YAC1B,YAAY,KAAK;YACjB,YAAY,KAAK;QACnB,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAqD,SAAS;YAAa;QAChH;IACF;IAGA,qBACE,6LAAC;QAAI,WAAU;kBACZ,CAAC,2BACC,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACb,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,6LAAC,mIAAA,CAAA,kBAAe;;gCAAC;gCACe,MAAM,IAAI;gCAAC;;;;;;;;;;;;;8BAG7C,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAe;oCAAqB,MAAM,IAAI;oCAAC;;;;;;;0CAC5D,6LAAC;gCAAK,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,eAAe,CAAC;0CAChD,cAAA,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAMlB;;gBAEG,0BAA0B,uBAAuB,MAAM,GAAG,mBACzD,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;;wCAAC;wCACC,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;wCAAE;;;;;;;;;;;;;sCAG1D,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,wIAAA,CAAA,YAAS;gCAAC,MAAK;gCAAW,WAAU;gCAAS,cAAc,uBAAuB,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;0CACxG,uBAAuB,GAAG,CAAC,CAAA,8BAC1B,6LAAC,wIAAA,CAAA,gBAAa;wCAAC,OAAO,cAAc,aAAa;;0DAC/C,6LAAC,wIAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAAsC,cAAc,aAAa;;;;;;0DAC7F,6LAAC,wIAAA,CAAA,mBAAgB;;oDACd,cAAc,aAAa,CAAC,GAAG,CAAC,CAAA,gCAC/B,6LAAC;4DAA0C,WAAU;;8EACnD,6LAAC;oEAAG,WAAU;8EAAwC,gBAAgB,eAAe;;;;;;8EACrF,6LAAC;;sFAAE,6LAAC;sFAAO;;;;;;wEAAsB;wEAAE,gBAAgB,aAAa,GAAG,GAAG,gBAAgB,aAAa,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB,aAAa,CAAC,QAAQ,EAAE,GAAG;;;;;;;8EAC9J,6LAAC;;sFAAE,6LAAC;sFAAO;;;;;;wEAAqB;wEAAE,gBAAgB,YAAY,GAAG,GAAG,gBAAgB,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB,YAAY,CAAC,QAAQ,EAAE,GAAG;;;;;;;gEACzJ,gBAAgB,mBAAmB,CAAC,MAAM,GAAG,kBAC5C,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAAqB;;;;;;sFACnC,6LAAC;4EAAG,WAAU;sFACX,gBAAgB,mBAAmB,CAAC,GAAG,CAAC,CAAA,sBACvC,6LAAC;;wFAAwB,MAAM,UAAU;wFAAC;wFAAG,MAAM,QAAQ;wFAAC;wFAAG,MAAM,QAAQ;wFAAC;;mFAArE,MAAM,OAAO;;;;;;;;;;;;;;;yFAK5B,6LAAC;oEAAE,WAAU;8EAAqC;;;;;;;2DAd5C,gBAAgB,eAAe;;;;;oDAkB1C,cAAc,aAAa,CAAC,MAAM,KAAK,mBAAK,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;uCAtBtC,cAAc,aAAa;;;;;;;;;;;;;;;;;;;;;8BAgC7F,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;;wCAAC;wCACa,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;wCAAE;;;;;;;;;;;;;sCAGtE,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAK,UAAU,KAAK,YAAY,CAAC;oCAAW,WAAU;;sDACrD,6LAAC;;8DACC,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;8DACjC,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAG;oDACH,aAAY;oDACX,GAAG,KAAK,QAAQ,CAAC,kBAAkB;oDACpC,WAAU;;;;;;;;;;;;sDAId,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,UAAU;4CAAW,WAAU;sDAClD,0BACC;;kEACE,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;gCAO5C,iCACC,6LAAC;oCAAI,KAAK;oCAAuB,WAAU;;wCAAO;sDAChD,6LAAC;4CAAI,WAAU;;gDAA8B;8DAC3C,6LAAC;oDAAG,WAAU;;wDAAkC;wDAAmB,MAAM,IAAI;;;;;;;8DAC7E,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAIvC,6LAAC;4CAAI,WAAU;;gDAAiB;8DAC9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0C;;;;;;sEACxD,6LAAC;4DAAI,WAAU;sEAA6E,gBAAgB,gBAAgB;;;;;;;;;;;;8DAE9H,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0C;;;;;;sEACxD,6LAAC;4DAAI,WAAU;sEAA6E,gBAAgB,SAAS;;;;;;;;;;;;8DAEvH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0C;;;;;;sEACxD,6LAAC;4DAAI,WAAU;sEAA6E,gBAAgB,mBAAmB;;;;;;;;;;;;8DAEjI,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0C;;;;;;sEACxD,6LAAC;4DAAI,WAAU;sEAA6E,gBAAgB,sBAAsB;;;;;;;;;;;;8DAEpI,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA0C;;;;;;sEACxD,6LAAC;4DAAI,WAAU;sEAA6E,gBAAgB,yBAAyB;;;;;;;;;;;;;;;;;;sDAGzI,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,8IAAA,CAAA,UAAW;gDAAC,YAAY;gDAAY,WAAW,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;wBAKjE,mBAAmB,4BAChB,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;;8CAClB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,uBAAuB,IAAI,CAAC,IAAM,MAAM;gDAAC,aAAa;4CAA6B;8CAAK;;;;;;8CAG/J,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;oCAAmB,UAAU;;sDAChE,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD,EAEA,iEAAiE;CACjE,0CAA0C;GA/RlB;;QAGJ,+HAAA,CAAA,WAAQ;QAIb,iKAAA,CAAA,UAAO;;;KAPE", "debugId": null}}]}