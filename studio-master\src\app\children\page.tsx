import ChildListClient from '@/components/children/ChildListClient';
import { MOCK_CHILDREN_DATA } from '@/lib/constants'; // Using mock data for now

export default async function ChildrenPage() {
  // In a real app, fetch children data here or pass it to the client component
  // For now, we use mock data directly in the client component for simplicity in this scaffold.
  // const children = await getChildrenData(); 

  return (
    <div className="container mx-auto py-8">
      <ChildListClient initialChildren={MOCK_CHILDREN_DATA} />
    </div>
  );
}
