
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Tooltip as RechartsTooltip, Legend as RechartsLegend } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig
} from "@/components/ui/chart";

interface ProgressOverviewChartProps {
  mastered: number;
  implemented: number;
  pending: number;
}

export default function ProgressOverviewChart({ mastered, implemented, pending }: ProgressOverviewChartProps) {
  const chartData = [
    { name: 'أهداف متقنة', value: mastered, fill: 'hsl(var(--chart-2))' },
    { name: 'أهداف قيد التنفيذ', value: implemented, fill: 'hsl(var(--chart-1))' },
    { name: 'أهداف معلقة/بدون تقييم', value: pending, fill: 'hsl(var(--chart-4))' },
  ].filter(item => item.value > 0); // Filter out zero values to avoid empty slices

  if (chartData.length === 0) {
    return <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض مخطط تقدم الأهداف.</p>;
  }

  const chartConfig = chartData.reduce((acc, item) => {
    acc[item.name] = { label: item.name, color: item.fill };
    return acc;
  }, {} as ChartConfig);


  return (
    <div className="h-[300px] w-full">
      <ChartContainer config={chartConfig} className="w-full h-full">
        <PieChart>
          <ChartTooltip content={<ChartTooltipContent nameKey="name" hideLabel />} />
          <Pie
            data={chartData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            outerRadius={100}
            labelLine={false}
            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {
              const RADIAN = Math.PI / 180;
              const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
              const x = cx + radius * Math.cos(-midAngle * RADIAN);
              const y = cy + radius * Math.sin(-midAngle * RADIAN);
              if (chartData[index].value === 0) return null; // Don't render label for zero value slices
              return (
                <text
                  x={x}
                  y={y}
                  fill="hsl(var(--primary-foreground))"
                  textAnchor={x > cx ? 'start' : 'end'}
                  dominantBaseline="central"
                  fontSize="12px"
                >
                  {`${(percent * 100).toFixed(0)}%`}
                </text>
              );
            }}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Pie>
          <ChartLegend
            content={<ChartLegendContent nameKey="name" wrapperStyle={{paddingTop: 20}} />}
            verticalAlign="bottom"
            align="center"
          />
        </PieChart>
      </ChartContainer>
    </div>
  );
}
