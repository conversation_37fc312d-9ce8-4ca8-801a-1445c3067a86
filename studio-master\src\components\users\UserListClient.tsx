
"use client";

import React, { useState, useEffect } from 'react';
import type { User, UserRole } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Edit, Trash2, UserCog, Search } from 'lucide-react';
import { MOCK_USERS_DATA, USER_ROLE_OPTIONS } from '@/lib/constants';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Image from 'next/image'; // For image preview

interface UserListClientProps {
  initialUsers: User[];
  roleOptions: { value: UserRole; label: string }[];
}

export default function UserListClient({ initialUsers, roleOptions }: UserListClientProps) {
  const [users, setUsers] = useState<User[]>(initialUsers.map(u => ({...u})));
  const [searchTerm, setSearchTerm] = useState('');
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const { toast } = useToast();

  const [currentName, setCurrentName] = useState('');
  const [currentEmail, setCurrentEmail] = useState('');
  const [currentRole, setCurrentRole] = useState<UserRole | undefined>(undefined);
  const [currentAvatarUrl, setCurrentAvatarUrl] = useState<string | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [currentSpecialization, setCurrentSpecialization] = useState(''); // Added state for specialization

  useEffect(() => {
     MOCK_USERS_DATA.length = 0;
     users.forEach(u => MOCK_USERS_DATA.push({...u}));
  }, [users]);

  useEffect(() => {
    if (editingUser) {
      setCurrentName(editingUser.name);
      setCurrentEmail(editingUser.email);
      setCurrentRole(editingUser.role);
      setCurrentAvatarUrl(editingUser.avatarUrl || null);
      setAvatarPreview(editingUser.avatarUrl || null);
      setCurrentSpecialization(editingUser.specialization || ''); // Set specialization
    } else {
      setCurrentName('');
      setCurrentEmail('');
      setCurrentRole(undefined);
      setCurrentAvatarUrl(null);
      setAvatarPreview(null);
      setCurrentSpecialization(''); // Reset specialization
    }
  }, [editingUser, isFormOpen]);

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.specialization && user.specialization.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleOpenForm = (user: User | null = null) => {
    setEditingUser(user);
    setIsFormOpen(true);
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setCurrentAvatarUrl(reader.result as string);
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
       if (editingUser && editingUser.avatarUrl) {
        // don't clear if just cancelling file selection
      } else {
        setCurrentAvatarUrl(null);
        setAvatarPreview(null);
      }
    }
  };

  const handleRemoveAvatar = () => {
    setCurrentAvatarUrl(null);
    setAvatarPreview(null);
    const fileInput = document.getElementById('avatarFileUser') as HTMLInputElement;
    if (fileInput) {
        fileInput.value = "";
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentName || !currentEmail || !currentRole) {
      toast({ title: "خطأ", description: "يرجى ملء جميع الحقول المطلوبة (الاسم، البريد الإلكتروني، الدور).", variant: "destructive" });
      return;
    }

    let finalAvatarUrl = currentAvatarUrl;

    if (currentAvatarUrl === null && editingUser && editingUser.avatarUrl && avatarPreview === null) {
      const initials = currentName.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || 'م';
      finalAvatarUrl = `https://placehold.co/40x40.png?text=${encodeURIComponent(initials)}`;
    } else if (currentAvatarUrl === null) {
        const initials = currentName.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || 'م';
        finalAvatarUrl = `https://placehold.co/40x40.png?text=${encodeURIComponent(initials)}`;
    }

    if (editingUser) {
      const updatedUserData: User = {
        ...editingUser,
        name: currentName,
        email: currentEmail,
        role: currentRole,
        avatarUrl: finalAvatarUrl || undefined,
        specialization: currentSpecialization || undefined, // Save specialization
      };
      setUsers(prev => prev.map(u => u.id === editingUser.id ? updatedUserData : u));
      toast({ title: "نجاح", description: `تم تحديث بيانات ${currentName} بنجاح.` });
    } else {
      const newUser: User = {
        id: `user-${Date.now()}`,
        name: currentName,
        email: currentEmail,
        role: currentRole,
        avatarUrl: finalAvatarUrl || undefined,
        specialization: currentSpecialization || undefined, // Save specialization
      };
      setUsers(prev => [newUser, ...prev]);
      toast({ title: "نجاح", description: `تمت إضافة ${currentName} بنجاح.` });
    }
    setIsFormOpen(false);
    setEditingUser(null);
  };

  const handleDeleteUser = (userId: string) => {
    setUsers(users.filter(user => user.id !== userId));
    toast({ title: "نجاح", description: "تم حذف المستخدم." });
  };


  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <h1 className="text-3xl font-bold text-primary flex items-center gap-2">
          <UserCog className="h-8 w-8" />
          إدارة المستخدمين
        </h1>
        <div className="flex gap-2 items-center w-full sm:w-auto">
           <div className="relative w-full sm:w-64">
            <Search className="absolute right-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground md:left-2.5" />
            <Input
              type="search"
              placeholder="البحث بالاسم أو البريد أو التخصص..."
              className="pr-8 md:pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button onClick={() => handleOpenForm()}>
            <PlusCircle className="ml-2 h-5 w-5" /> إضافة مستخدم
          </Button>
        </div>
      </div>

      <Dialog open={isFormOpen} onOpenChange={(isOpen) => {
        setIsFormOpen(isOpen);
        if (!isOpen) setEditingUser(null);
      }}>
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>{editingUser ? 'تعديل بيانات المستخدم' : 'إضافة مستخدم جديد'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">الاسم</Label>
                <Input id="name" value={currentName} onChange={(e) => setCurrentName(e.target.value)} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">البريد الإلكتروني</Label>
                <Input id="email" type="email" value={currentEmail} onChange={(e) => setCurrentEmail(e.target.value)} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">الدور (الصلاحية)</Label>
                <Select value={currentRole} onValueChange={(value) => setCurrentRole(value as UserRole)} required>
                  <SelectTrigger id="role" className="col-span-3">
                    <SelectValue placeholder="اختر دور المستخدم" />
                  </SelectTrigger>
                  <SelectContent>
                    {roleOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="specialization" className="text-right">التخصص</Label>
                <Input id="specialization" value={currentSpecialization} onChange={(e) => setCurrentSpecialization(e.target.value)} className="col-span-3" />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="avatarFileUser" className="text-right pt-2">صورة شخصية</Label>
                <div className="col-span-3 space-y-2">
                  <Input id="avatarFileUser" type="file" accept="image/*" onChange={handleAvatarChange} className="col-span-3" />
                  {avatarPreview && (
                    <div className="relative h-20 w-20">
                      <Image src={avatarPreview} alt="معاينة الصورة" layout="fill" objectFit="cover" className="rounded-md" data-ai-hint="user avatar" />
                    </div>
                  )}
                   {avatarPreview && (
                     <Button type="button" variant="ghost" size="sm" onClick={handleRemoveAvatar} className="text-red-500 hover:text-red-700">
                        إزالة الصورة
                      </Button>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">إلغاء</Button>
              </DialogClose>
              <Button type="submit">{editingUser ? 'حفظ التعديلات' : 'إضافة المستخدم'}</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {filteredUsers.length > 0 ? (
        <div className="border rounded-lg shadow-md overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px]">الصورة</TableHead>
                <TableHead>الاسم</TableHead>
                <TableHead>البريد الإلكتروني</TableHead>
                <TableHead>التخصص</TableHead>
                <TableHead>الدور</TableHead>
                <TableHead className="text-left">الإجراءات</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map(user => (
                <TableRow key={user.id}>
                  <TableCell>
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={user.avatarUrl} alt={user.name} data-ai-hint="user avatar" />
                      <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('').substring(0,2).toUpperCase() || 'م'}</AvatarFallback>
                    </Avatar>
                  </TableCell>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.specialization || '-'}</TableCell>
                  <TableCell>{roleOptions.find(r => r.value === user.role)?.label || user.role}</TableCell>
                  <TableCell className="text-left">
                    <Button variant="ghost" size="icon" onClick={() => handleOpenForm(user)} className="text-blue-500 hover:text-blue-700">
                      <Edit className="h-4 w-4" />
                       <span className="sr-only">تعديل</span>
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon" className="text-red-500 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">حذف</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
                          <AlertDialogDescription>
                            سيتم حذف المستخدم "{user.name}" نهائياً. لا يمكن التراجع عن هذا الإجراء.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>إلغاء</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleDeleteUser(user.id)} className="bg-destructive hover:bg-destructive/90">
                            حذف
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-12 border rounded-lg shadow-sm">
          <UserCog className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-xl font-semibold">لم يتم العثور على مستخدمين</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            {searchTerm ? "حاول تعديل بحثك." : "ابدأ بإضافة مستخدم جديد."}
          </p>
        </div>
      )}
    </div>
  );
}

