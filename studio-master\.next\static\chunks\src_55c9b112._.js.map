{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/hooks/use-storage.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport type { Child, Assessment, User, LearningPlan } from '@/lib/types';\nimport {\n  initializeStorage,\n  getChildren,\n  getChildById,\n  saveChild,\n  deleteChild,\n  getAssessments,\n  getAssessmentsByChildId,\n  getAssessmentById,\n  saveAssessment,\n  deleteAssessment,\n  getUsers,\n  getUserById,\n  saveUser,\n  getLearningPlans,\n  getLearningPlansByChildId,\n  saveLearningPlan,\n  exportAllData,\n  importAllData,\n  clearAllData,\n  getStorageInfo,\n} from '@/lib/storage';\n\n// Custom hook for children management\nexport function useChildren() {\n  const [children, setChildren] = useState<Child[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshChildren = useCallback(() => {\n    setChildren(getChildren());\n  }, []);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshChildren();\n    setLoading(false);\n  }, [refreshChildren]);\n\n  const addChild = useCallback((child: Child) => {\n    const success = saveChild(child);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const updateChild = useCallback((child: Child) => {\n    const success = saveChild(child);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const removeChild = useCallback((childId: string) => {\n    const success = deleteChild(childId);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const getChild = useCallback((childId: string) => {\n    return getChildById(childId);\n  }, []);\n\n  return {\n    children,\n    loading,\n    addChild,\n    updateChild,\n    removeChild,\n    getChild,\n    refreshChildren,\n  };\n}\n\n// Custom hook for assessments management\nexport function useAssessments(childId?: string) {\n  const [assessments, setAssessments] = useState<Assessment[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshAssessments = useCallback(() => {\n    if (childId) {\n      setAssessments(getAssessmentsByChildId(childId));\n    } else {\n      setAssessments(getAssessments());\n    }\n  }, [childId]);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshAssessments();\n    setLoading(false);\n  }, [refreshAssessments]);\n\n  const addAssessment = useCallback((assessment: Assessment) => {\n    const success = saveAssessment(assessment);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const updateAssessment = useCallback((assessment: Assessment) => {\n    const success = saveAssessment(assessment);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const removeAssessment = useCallback((assessmentId: string) => {\n    const success = deleteAssessment(assessmentId);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const getAssessment = useCallback((assessmentId: string) => {\n    return getAssessmentById(assessmentId);\n  }, []);\n\n  return {\n    assessments,\n    loading,\n    addAssessment,\n    updateAssessment,\n    removeAssessment,\n    getAssessment,\n    refreshAssessments,\n  };\n}\n\n// Custom hook for users management\nexport function useUsers() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshUsers = useCallback(() => {\n    setUsers(getUsers());\n  }, []);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshUsers();\n    setLoading(false);\n  }, [refreshUsers]);\n\n  const addUser = useCallback((user: User) => {\n    const success = saveUser(user);\n    if (success) {\n      refreshUsers();\n    }\n    return success;\n  }, [refreshUsers]);\n\n  const updateUser = useCallback((user: User) => {\n    const success = saveUser(user);\n    if (success) {\n      refreshUsers();\n    }\n    return success;\n  }, [refreshUsers]);\n\n  const removeUser = useCallback((userId: string) => {\n    // For now, we'll implement a simple filter-based delete\n    // In a real app, you might want to add a deleteUser function to storage.ts\n    const currentUsers = getUsers();\n    const filteredUsers = currentUsers.filter(u => u.id !== userId);\n\n    try {\n      localStorage.setItem('portage_plus_users', JSON.stringify(filteredUsers));\n      refreshUsers();\n      return true;\n    } catch (error) {\n      console.error('Failed to delete user:', error);\n      return false;\n    }\n  }, [refreshUsers]);\n\n  const getUser = useCallback((userId: string) => {\n    return getUserById(userId);\n  }, []);\n\n  return {\n    users,\n    loading,\n    addUser,\n    updateUser,\n    removeUser,\n    getUser,\n    refreshUsers,\n  };\n}\n\n// Custom hook for learning plans management\nexport function useLearningPlans(childId?: string) {\n  const [learningPlans, setLearningPlans] = useState<LearningPlan[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshLearningPlans = useCallback(() => {\n    if (childId) {\n      setLearningPlans(getLearningPlansByChildId(childId));\n    } else {\n      setLearningPlans(getLearningPlans());\n    }\n  }, [childId]);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshLearningPlans();\n    setLoading(false);\n  }, [refreshLearningPlans]);\n\n  const addLearningPlan = useCallback((plan: LearningPlan) => {\n    const success = saveLearningPlan(plan);\n    if (success) {\n      refreshLearningPlans();\n    }\n    return success;\n  }, [refreshLearningPlans]);\n\n  const updateLearningPlan = useCallback((plan: LearningPlan) => {\n    const success = saveLearningPlan(plan);\n    if (success) {\n      refreshLearningPlans();\n    }\n    return success;\n  }, [refreshLearningPlans]);\n\n  return {\n    learningPlans,\n    loading,\n    addLearningPlan,\n    updateLearningPlan,\n    refreshLearningPlans,\n  };\n}\n\n// Custom hook for data management\nexport function useDataManagement() {\n  const [storageInfo, setStorageInfo] = useState<any>(null);\n\n  const refreshStorageInfo = useCallback(() => {\n    setStorageInfo(getStorageInfo());\n  }, []);\n\n  useEffect(() => {\n    refreshStorageInfo();\n  }, [refreshStorageInfo]);\n\n  const exportData = useCallback(() => {\n    return exportAllData();\n  }, []);\n\n  const importData = useCallback((data: any) => {\n    const success = importAllData(data);\n    if (success) {\n      refreshStorageInfo();\n    }\n    return success;\n  }, [refreshStorageInfo]);\n\n  const clearData = useCallback(() => {\n    const success = clearAllData();\n    if (success) {\n      refreshStorageInfo();\n    }\n    return success;\n  }, [refreshStorageInfo]);\n\n  return {\n    storageInfo,\n    exportData,\n    importData,\n    clearData,\n    refreshStorageInfo,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;;AAwBO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAClC,YAAY,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;QACxB;mDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;YAChB;YACA,WAAW;QACb;gCAAG;QAAC;KAAgB;IAEpB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC5B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE;YAC1B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;4CAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC/B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAAE;YAC1B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;+CAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC;YAC/B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;YAC5B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;+CAAG;QAAC;KAAgB;IAEpB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YAC5B,OAAO,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE;QACtB;4CAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAnDgB;AAsDT,SAAS,eAAe,OAAgB;;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,IAAI,SAAS;gBACX,eAAe,CAAA,GAAA,wHAAA,CAAA,0BAAuB,AAAD,EAAE;YACzC,OAAO;gBACL,eAAe,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;YAC9B;QACF;yDAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;YAChB;YACA,WAAW;QACb;mCAAG;QAAC;KAAmB;IAEvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;YAC/B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;oDAAG;QAAC;KAAmB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE;YAC/B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;uDAAG;QAAC;KAAmB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;uDAAG;QAAC;KAAmB;IAEvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,OAAO,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B;oDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAvDgB;AA0DT,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC/B,SAAS,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;QAClB;6CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;YAChB;YACA,WAAW;QACb;6BAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,CAAC;YAC3B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;wCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAC9B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;2CAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YAC9B,wDAAwD;YACxD,2EAA2E;YAC3E,MAAM,eAAe,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;YAC5B,MAAM,gBAAgB,aAAa,MAAM;kEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;YAExD,IAAI;gBACF,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;gBAC1D;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;YACT;QACF;2CAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,CAAC;YAC3B,OAAO,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD,EAAE;QACrB;wCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA3DgB;AA8DT,SAAS,iBAAiB,OAAgB;;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACvC,IAAI,SAAS;gBACX,iBAAiB,CAAA,GAAA,wHAAA,CAAA,4BAAyB,AAAD,EAAE;YAC7C,OAAO;gBACL,iBAAiB,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD;YAClC;QACF;6DAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;YAChB;YACA,WAAW;QACb;qCAAG;QAAC;KAAqB;IAEzB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACnC,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;wDAAG;QAAC;KAAqB;IAEzB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YACtC,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;2DAAG;QAAC;KAAqB;IAEzB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IAzCgB;AA4CT,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACrC,eAAe,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;QAC9B;4DAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAmB;IAEvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC7B,OAAO,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD;QACrB;oDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;oDAAG;QAAC;KAAmB;IAEvB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC5B,MAAM,UAAU,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;YAC3B,IAAI,SAAS;gBACX;YACF;YACA,OAAO;QACT;mDAAG;QAAC;KAAmB;IAEvB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;IAtCgB", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,YAAY,wKAAA,CAAA,OAAuB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,wKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,6LAAC,wKAAA,CAAA,UAA0B;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW;AAErE,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,wKAAA,CAAA,UAA0B;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,wKAAA,CAAA,UAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/assessment/AgeDisplay.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { calculateAge, formatDate } from '@/lib/utils';\nimport type { CalculatedAge } from '@/lib/types';\nimport { Skeleton } from '@/components/ui/skeleton';\n\ninterface AgeDisplayProps {\n  birthDate: string; // ISO date string\n  assessmentDate?: string; // Optional ISO date string, defaults to today\n  className?: string;\n  label?: string;\n}\n\nexport default function AgeDisplay({ birthDate, assessmentDate, className, label = \"العمر:\" }: AgeDisplayProps) {\n  const [age, setAge] = useState<CalculatedAge | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    try {\n      const calculated = calculateAge(birthDate, assessmentDate);\n      setAge(calculated);\n    } catch (error) {\n      // console.error(\"Error calculating age:\", error);\n      setAge(null); // Set to null or a default error state if needed\n    } finally {\n      setIsLoading(false);\n    }\n  }, [birthDate, assessmentDate]);\n\n  if (isLoading) {\n    return <Skeleton className={`h-5 w-32 ${className}`} />;\n  }\n\n  if (!age) {\n    return <span className={className}>تاريخ غير صالح</span>;\n  }\n\n  return (\n    <span className={className}>\n      {label}{' '}\n      {age.years > 0 && `${age.years}س `}\n      {age.months > 0 && `${age.months}ش `}\n      {`${age.days}ي`}\n      {! (age.years > 0 || age.months > 0 || age.days > 0) && assessmentDate && birthDate > assessmentDate! && '(تاريخ ميلاد مستقبلي)'}\n      {! (age.years > 0 || age.months > 0 || age.days > 0) && !assessmentDate && new Date(birthDate) > new Date() && '(تاريخ ميلاد مستقبلي)'}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAce,SAAS,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAmB;;IAC5G,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI;gBACF,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,WAAW;gBAC3C,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,kDAAkD;gBAClD,OAAO,OAAO,iDAAiD;YACjE,SAAU;gBACR,aAAa;YACf;QACF;+BAAG;QAAC;QAAW;KAAe;IAE9B,IAAI,WAAW;QACb,qBAAO,6LAAC,uIAAA,CAAA,WAAQ;YAAC,WAAW,CAAC,SAAS,EAAE,WAAW;;;;;;IACrD;IAEA,IAAI,CAAC,KAAK;QACR,qBAAO,6LAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBACE,6LAAC;QAAK,WAAW;;YACd;YAAO;YACP,IAAI,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC;YACnC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;YACd,CAAE,CAAC,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,kBAAkB,YAAY,kBAAmB;YACxG,CAAE,CAAC,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,kBAAkB,IAAI,KAAK,aAAa,IAAI,UAAU;;;;;;;AAGrH;GAlCwB;KAAA", "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 676, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1077, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\nimport { DayPicker } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: CalendarProps) {\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\"p-3\", className)}\n      classNames={{\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\n        month: \"space-y-4\",\n        caption: \"flex justify-center pt-1 relative items-center\",\n        caption_label: \"text-sm font-medium\",\n        nav: \"space-x-1 flex items-center\",\n        nav_button: cn(\n          buttonVariants({ variant: \"outline\" }),\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\n        ),\n        nav_button_previous: \"absolute left-1\",\n        nav_button_next: \"absolute right-1\",\n        table: \"w-full border-collapse space-y-1\",\n        head_row: \"flex\",\n        head_cell:\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\n        row: \"flex w-full mt-2\",\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n        day: cn(\n          buttonVariants({ variant: \"ghost\" }),\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\n        ),\n        day_range_end: \"day-range-end\",\n        day_selected:\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n        day_today: \"bg-accent text-accent-foreground\",\n        day_outside:\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\n        day_disabled: \"text-muted-foreground opacity-50\",\n        day_range_middle:\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n        day_hidden: \"invisible\",\n        ...classNames,\n      }}\n      components={{\n        IconLeft: ({ className, ...props }) => (\n          <ChevronLeft className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n        IconRight: ({ className, ...props }) => (\n          <ChevronRight className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = \"Calendar\"\n\nexport { Calendar }\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAWA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,6LAAC,iKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;Y<PERSON><PERSON>,MAAM;YACN,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,eAAe;YACf,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;YAE7D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;QAEhE;QACC,GAAG,KAAK;;;;;;AAGf;KAvDS;AAwDT,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/date-picker.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { format } from \"date-fns\"\nimport { arSA } from \"date-fns/locale\" // For Arabic locale\nimport { Calendar as CalendarIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Calendar } from \"@/components/ui/calendar\"\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\"\n\ninterface DatePickerProps {\n  date: Date | undefined;\n  setDate: (date: Date | undefined) => void;\n  buttonClassName?: string;\n  placeholder?: string;\n}\n\nexport function DatePicker({ date, setDate, buttonClassName, placeholder = \"اختر تاريخًا\" }: DatePickerProps) {\n  return (\n    <Popover>\n      <PopoverTrigger asChild>\n        <Button\n          variant={\"outline\"}\n          className={cn(\n            \"w-full justify-start text-right font-normal\", // Changed text-left to text-right for RTL\n            !date && \"text-muted-foreground\",\n            buttonClassName\n          )}\n        >\n          <CalendarIcon className=\"ml-2 h-4 w-4\" /> {/* Changed mr-2 to ml-2 for RTL */}\n          {date ? format(date, \"PPP\", { locale: arSA }) : <span>{placeholder}</span>}\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-auto p-0\">\n        <Calendar\n          mode=\"single\"\n          selected={date}\n          onSelect={setDate}\n          initialFocus\n          locale={arSA} // Set locale for Calendar\n          dir=\"rtl\" // Ensure calendar itself is RTL\n        />\n      </PopoverContent>\n    </Popover>\n  )\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA,mQAAuC,oBAAoB;AAC3D;AAEA;AACA;AACA;AACA;AAVA;;;;;;;;;AAuBO,SAAS,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,cAAc,EAAmB;IAC1G,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+CACA,CAAC,QAAQ,yBACT;;sCAGF,6LAAC,6MAAA,CAAA,WAAY;4BAAC,WAAU;;;;;;wBAAiB;wBACxC,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO;4BAAE,QAAQ,qJAAA,CAAA,OAAI;wBAAC,mBAAK,6LAAC;sCAAM;;;;;;;;;;;;;;;;;0BAG3D,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oBACP,MAAK;oBACL,UAAU;oBACV,UAAU;oBACV,YAAY;oBACZ,QAAQ,qJAAA,CAAA,OAAI;oBACZ,KAAI,MAAM,gCAAgC;;;;;;;;;;;;;;;;;AAKpD;KA5BgB", "debugId": null}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/flows/analyze-skill-for-daily-routine.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview Provides AI-driven analysis for integrating a child's skill into daily routines.\n *\n * - analyzeSkillForDailyRoutine - Function to generate routine integration suggestions for a skill.\n * - SkillAnalysisInput - Input type for the analysis.\n * - SkillAnalysisOutput - Output type for the analysis.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst SkillAnalysisInputSchema = z.object({\n  skillBehavior: z.string().describe('وصف سلوك المهارة أو الهدف المطلوب تحليله.'),\n  ageRange: z.string().describe('الفئة العمرية للطفل المستهدف بالمهارة.'),\n  childName: z.string().describe('اسم الطفل.'),\n});\nexport type SkillAnalysisInput = z.infer<typeof SkillAnalysisInputSchema>;\n\nconst SkillAnalysisOutputSchema = z.object({\n  mealtime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت الطعام، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  bathroom: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت استخدام الحمام، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  playtime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت اللعب، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  outings: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة عند الخروج من المنزل، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  bedtime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت النوم، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  toolsIntegration: z.string().describe(\"شرح لكيفية استخدام الأدوات المذكورة أو المقترحة في الأنشطة الروتينية، مقدم بلسان الطفل وباللهجة الأردنية.\"),\n  generalTips: z.string().describe(\"نصائح عامة للأهل أو مقدمي الرعاية لضمان قابلية تطبيق الاقتراحات بشكل واقعي وممتع للطفل، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n});\nexport type SkillAnalysisOutput = z.infer<typeof SkillAnalysisOutputSchema>;\n\nexport async function analyzeSkillForDailyRoutine(\n  input: SkillAnalysisInput\n): Promise<SkillAnalysisOutput> {\n  return analyzeSkillFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'analyzeSkillForDailyRoutinePrompt',\n  input: {schema: SkillAnalysisInputSchema},\n  output: {schema: SkillAnalysisOutputSchema},\n  prompt: `يا جماعة، أنا {{{childName}}} (عمري {{{ageRange}}}). بدي تساعدوني أتعلم شغلة جديدة ومهمة إلي، وهي: \"{{{skillBehavior}}}\".\n\nاحكوا معي بالعامية الأردنية، وخلوني أحكيلكم كيف بنقدر نخلي تعلم هالشغلة جزء من يومنا العادي، وبطريقة حلوة ومسلية.\n\nأعطوني أفكار واضحة لكل وقت من هالأوقات، كأني أنا اللي بقترحها عليكم:\n\n1.  **وقت الأكل (mealtime):** (يا ماما ويا بابا، وقت الأكل، كيف ممكن نخلي تعلم \"{{{skillBehavior}}}\" إشي زاكي وممتع؟ مثلاً، أنا جاي عبالي...)\n2.  **وقت الحمام (bathroom):** (لما أكون بالحمام، كيف بتقدروا تساعدوني بـ \"{{{skillBehavior}}}\"؟ بلكي بنقدر نعمل...)\n3.  **وقت اللعب (playtime):** (يا سلااام على وقت اللعب! عشان أتعلم \"{{{skillBehavior}}}\", شو الألعاب اللي بنقدر نلعبها سوا؟ أنا بخطر عبالي...)\n4.  **لما نطلع من البيت (outings):** (لما نطلع مشوار، كيف ممكن نستغل الفرص عشان أتدرب على \"{{{skillBehavior}}}\"؟ مثلاً، بالسوق أو لما نزور قرايبنا...)\n5.  **وقت النوم (bedtime):** (قبل ما أنام، في طريقة لطيفة نتذكر فيها \"{{{skillBehavior}}}\"؟ يمكن عن طريق قصة أو لعبة هادية؟)\n\n**الأدوات اللي بنستخدمها (toolsIntegration):** (إذا في أدوات معينة بتساعدني أتعلم \"{{{skillBehavior}}}\", اشرحولي كيف بنقدر نستخدمها بالأشياء اللي بنعملها كل يوم. أو إذا مافي أدوات معينة، شو ممكن نستخدم أشياء بسيطة من البيت؟)\n\n**نصايح إلكم (generalTips):** (يا أحسن أهل بالدنيا، عشان هالشغلات تكون ممتعة ومفيدة إلي، اتذكروا إني لساتني صغير (عمري {{{ageRange}}}) وبحب التشجيع واللعب. شو كمان شغلات ممكن تعملوها عشان يكون كل إشي سهل وحلو إلي وإلكم؟)\n\nتأكدوا إنه الجواب يكون كامل ومفصل لكل قسم، وكأنه طالع مني أنا، الطفل {{{childName}}}، وبحكي أردني.\n`,\n});\n\nconst analyzeSkillFlow = ai.defineFlow(\n  {\n    name: 'analyzeSkillFlow',\n    inputSchema: SkillAnalysisInputSchema,\n    outputSchema: SkillAnalysisOutputSchema,\n  },\n  async (input) => {\n    const {output} = await prompt(input);\n    if (!output) {\n        throw new Error(\"لم يتمكن الذكاء الاصطناعي من إنشاء تحليل للمهارة.\");\n    }\n    return output;\n  }\n);\n\n"], "names": [], "mappings": ";;;;;IA+BsB", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/flows/analyze-skill-for-preschool-routine.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview Provides AI-driven analysis for integrating a child's skill into preschool/kindergarten routines.\n *\n * - analyzeSkillForPreschoolRoutine - Function to generate routine integration suggestions for a skill in a preschool setting.\n * - PreschoolSkillAnalysisInput - Input type for the analysis.\n * - PreschoolSkillAnalysisOutput - Output type for the analysis.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst PreschoolSkillAnalysisInputSchema = z.object({\n  skillBehavior: z.string().describe('وصف سلوك المهارة أو الهدف المطلوب تحليله.'),\n  ageRange: z.string().describe('الفئة العمرية للطفل المستهدف بالمهارة.'),\n  childName: z.string().describe('اسم الطفل.'),\n});\nexport type PreschoolSkillAnalysisInput = z.infer<typeof PreschoolSkillAnalysisInputSchema>;\n\nconst PreschoolSkillAnalysisOutputSchema = z.object({\n  arrivalTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال وقت الوصول إلى الروضة/الحضانة.\"),\n  morningCircle: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال أنشطة الدائرة الصباحية.\"),\n  activityTransition: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال أوقات الانتقال بين الأنشطة.\"),\n  learningCenters: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في المراكز/أركان التعلم المختلفة.\"),\n  outdoorPlay: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة أثناء اللعب في الساحة الخارجية.\"),\n  preschoolBathroom: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت استخدام الحمام داخل الروضة/الحضانة.\"),\n  preschoolSnackTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت الوجبة الخفيفة/الغداء في الروضة/الحضانة.\"),\n  storyTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال وقت القصة.\"),\n  departureTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال وقت المغادرة من الروضة/الحضانة.\"),\n  preschoolToolsIntegration: z.string().describe(\"شرح لكيفية استخدام الأدوات المذكورة (إن وجدت في بيانات المهارة الأصلية) في هذه الأنشطة الروتينية أو اقتراح أدوات بسيطة مناسبة للروضة.\"),\n  teacherGeneralTips: z.string().describe(\"نصائح عامة للمعلمين لضمان قابلية تطبيق الاقتراحات بشكل واقعي وممتع في بيئة جماعية.\"),\n});\nexport type PreschoolSkillAnalysisOutput = z.infer<typeof PreschoolSkillAnalysisOutputSchema>;\n\nexport async function analyzeSkillForPreschoolRoutine(\n  input: PreschoolSkillAnalysisInput\n): Promise<PreschoolSkillAnalysisOutput> {\n  return analyzePreschoolSkillFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'analyzeSkillForPreschoolRoutinePrompt',\n  input: {schema: PreschoolSkillAnalysisInputSchema},\n  output: {schema: PreschoolSkillAnalysisOutputSchema},\n  prompt: `أنت خبير في تنمية الطفولة المبكرة وبرنامج بورتيج، متخصص في مساعدة معلمي الحضانات ورياض الأطفال على دمج الأهداف التعليمية للأطفال في أنشطتهم اليومية داخل بيئة الروضة/الحضانة بشكل عملي ومبتكر.\n\nالطفل: {{{childName}}}\nالمهارة المستهدفة (الهدف): \"{{{skillBehavior}}}\"\nالفئة العمرية للمهارة: {{{ageRange}}}\n\nالرجاء تقديم تحليل مفصل واقتراحات عملية حول كيفية دمج هذه المهارة في الروتين اليومي للطفل ضمن بيئة الحضانة أو الروضة. يجب أن تكون الاقتراحات قابلة للتطبيق بسهولة من قبل المعلمين.\n\nحلل الهدف وقدم اقتراحات محددة لكل من الأوقات والأنشطة التالية في الروضة/الحضانة:\n1.  **وقت الوصول:** (كيف يمكن استقبال الطفل ودمج المهارة خلال اللحظات الأولى في الروضة؟)\n2.  **الدائرة الصباحية:** (كيف يمكن دمج المهارة أثناء أنشطة الدائرة الصباحية مثل الأناشيد، عرض التاريخ، الطقس، أو مناقشة موضوع اليوم؟)\n3.  **الانتقال بين الأنشطة:** (كيف يمكن استغلال أوقات الانتقال لتعزيز المهارة بشكل سلس؟)\n4.  **المراكز/أركان التعلم:** (اقترح أنشطة محددة في أركان التعلم المختلفة - مثل ركن البناء، ركن القراءة، ركن الفن، ركن الاكتشاف - التي تعزز هذه المهارة)\n5.  **الساحة الخارجية:** (كيف يمكن تعزيز المهارة أثناء اللعب في الخارج؟)\n6.  **وقت الحمام (في الروضة):** (كيف يمكن دمج المهارة أثناء روتين استخدام الحمام في الروضة؟ سمِ هذا القسم preschoolBathroom)\n7.  **الوجبة الخفيفة/الغداء (في الروضة):** (كيف يمكن ممارسة أو تعزيز المهارة أثناء تناول الوجبات في الروضة؟ سمِ هذا القسم preschoolSnackTime)\n8.  **وقت القصة:** (هل هناك طرق لدمج المهارة أثناء سرد القصص أو الأنشطة المتعلقة بها؟)\n9.  **المغادرة:** (كيف يمكن تعزيز المهارة أو تلخيص ما تم تعلمه خلال الاستعداد للمغادرة؟)\n\nبالنسبة لـ \"استخدام الأدوات في الروضة\": اشرح كيف يمكن استخدام الأدوات المذكورة عادةً لهذه المهارة (حتى لو لم تُذكر صراحةً في الإدخال الحالي، يمكنك اقتراح أدوات شائعة أو بسيطة متوفرة في الروضة إذا كانت مناسبة) في هذه الأنشطة الروتينية. سمِ هذا القسم preschoolToolsIntegration.\n\nقدم \"نصائح عامة للتطبيق للمعلمين\": لضمان أن تكون الاقتراحات ممتعة وقابلة للتطبيق بشكل واقعي في بيئة جماعية، مع مراعاة الفئة العمرية. سمِ هذا القسم teacherGeneralTips.\n\nتأكد من أن الإجابة تكون شاملة ومفصلة لكل قسم.\n`,\n});\n\nconst analyzePreschoolSkillFlow = ai.defineFlow(\n  {\n    name: 'analyzePreschoolSkillFlow',\n    inputSchema: PreschoolSkillAnalysisInputSchema,\n    outputSchema: PreschoolSkillAnalysisOutputSchema,\n  },\n  async (input) => {\n    const {output} = await prompt(input);\n    if (!output) {\n        throw new Error(\"لم يتمكن الذكاء الاصطناعي من إنشاء تحليل للمهارة في سياق الروضة.\");\n    }\n    return output;\n  }\n);\n"], "names": [], "mappings": ";;;;;IAmCsB", "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/assessment/AnalyzableSkillItem.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport type { AssessedSkill, PortageSkillItem, SkillStatus, ProgressStatus } from '@/lib/types';\nimport { SKILL_STATUS_OPTIONS, PROGRESS_STATUS_OPTIONS, MOCK_ASSESSMENTS_DATA } from '@/lib/constants'; // Added MOCK_ASSESSMENTS_DATA\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter } from '@/components/ui/dialog';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { DatePicker } from '@/components/ui/date-picker';\nimport { useState, useRef, useEffect }from 'react';\nimport { useRouter } from 'next/navigation'; // Added useRouter\nimport { <PERSON>ader2, <PERSON>rkles, BookOpen, Printer, CalendarClock, CheckCircle, Edit3, AlertTriangle, Save } from 'lucide-react';\nimport { useToast } from '@/hooks/use-toast';\nimport { analyzeSkillForDailyRoutine, SkillAnalysisInput, SkillAnalysisOutput } from '@/ai/flows/analyze-skill-for-daily-routine';\nimport { analyzeSkillForPreschoolRoutine, PreschoolSkillAnalysisInput, PreschoolSkillAnalysisOutput } from '@/ai/flows/analyze-skill-for-preschool-routine';\nimport { formatDate } from '@/lib/utils';\nimport { parseISO, isPast, format } from 'date-fns'; // Added format\n\n// Combined type for the item prop\nexport type AnalyzableItemType = AssessedSkill & \n  (Omit<PortageSkillItem, 'id'> & { \n    skillId: string; \n    dimensionName: string; \n    subCategoryName: string; \n  });\n\n\ninterface AnalyzableSkillItemProps {\n  item: AnalyzableItemType;\n  childName: string;\n  assessmentId: string; // Added assessmentId to help find the assessment to update\n}\n\nexport default function AnalyzableSkillItem({ item, childName, assessmentId }: AnalyzableSkillItemProps) {\n  const router = useRouter();\n  const { toast } = useToast();\n\n  // AI Analysis States\n  const [isLoadingHome, setIsLoadingHome] = useState(false);\n  const [homeAnalysisData, setHomeAnalysisData] = useState<SkillAnalysisOutput | null>(null);\n  const [isHomeDialogOpen, setIsHomeDialogOpen] = useState(false);\n  const homeAnalysisContentRef = useRef<HTMLDivElement>(null);\n\n  const [isLoadingPreschool, setIsLoadingPreschool] = useState(false);\n  const [preschoolAnalysisData, setPreschoolAnalysisData] = useState<PreschoolSkillAnalysisOutput | null>(null);\n  const [isPreschoolDialogOpen, setIsPreschoolDialogOpen] = useState(false);\n  const preschoolAnalysisContentRef = useRef<HTMLDivElement>(null);\n\n  // Progress Tracking Dialog States\n  const [isProgressDialogOpen, setIsProgressDialogOpen] = useState(false);\n  const [currentProgressStatus, setCurrentProgressStatus] = useState<ProgressStatus | undefined>(item.progressStatus || 'pending');\n  const [currentImplementationStartDate, setCurrentImplementationStartDate] = useState<Date | undefined>(\n    item.implementationStartDate ? parseISO(item.implementationStartDate) : undefined\n  );\n  const [currentTargetCompletionDate, setCurrentTargetCompletionDate] = useState<Date | undefined>(\n    item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined\n  );\n  const [currentProgressNotes, setCurrentProgressNotes] = useState(item.progressNotes || '');\n\n  // Extend Deadline Dialog States\n  const [isExtendDeadlineDialogOpen, setIsExtendDeadlineDialogOpen] = useState(false);\n  const [newTargetDateForExtension, setNewTargetDateForExtension] = useState<Date | undefined>(\n    item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined\n  );\n\n  const [isTargetDateOverdue, setIsTargetDateOverdue] = useState(false);\n\n  const statusInfo = SKILL_STATUS_OPTIONS.find(s => s.value === item.status);\n  const progressStatusInfo = PROGRESS_STATUS_OPTIONS.find(ps => ps.value === item.progressStatus);\n\n  useEffect(() => {\n    if (item.targetCompletionDate) {\n      try {\n        const targetDate = parseISO(item.targetCompletionDate);\n        setIsTargetDateOverdue(isPast(targetDate) && item.progressStatus !== 'mastered');\n      } catch (e) {\n        setIsTargetDateOverdue(false);\n      }\n    } else {\n      setIsTargetDateOverdue(false);\n    }\n    // Update dialog fields if item prop changes\n    setCurrentProgressStatus(item.progressStatus || 'pending');\n    setCurrentImplementationStartDate(item.implementationStartDate ? parseISO(item.implementationStartDate) : undefined);\n    setCurrentTargetCompletionDate(item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined);\n    setCurrentProgressNotes(item.progressNotes || '');\n    setNewTargetDateForExtension(item.targetCompletionDate ? parseISO(item.targetCompletionDate) : undefined);\n\n  }, [item.targetCompletionDate, item.progressStatus, item.implementationStartDate, item.progressNotes]);\n\n\n  const handlePrint = (title: string, contentRef: React.RefObject<HTMLDivElement>) => {\n    if (!contentRef.current) {\n      toast({ title: \"خطأ في الطباعة\", description: \"لم يتم العثور على المحتوى للطباعة.\", variant: \"destructive\" });\n      return;\n    }\n    const printWindow = window.open('', '_blank');\n    if (printWindow) {\n      printWindow.document.write('<html><head><title>' + title + '</title>');\n      printWindow.document.write(`\n        <style>\n          body { font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif; direction: rtl; padding: 20px; margin: 0; }\n          h1, h2, h3, h4, h5, h6 { color: #333; margin-bottom: 0.5em; margin-top: 1em; }\n          p { margin-bottom: 1em; line-height: 1.6; white-space: pre-wrap; font-family: inherit; }\n          .dialog-description { color: #555; font-size: 0.9em; margin-bottom: 1.5em; }\n          .analysis-section h4 { font-weight: bold; color: hsl(var(--primary)); margin-bottom: 0.5em; }\n          .analysis-section p { white-space: pre-wrap; background-color: hsl(var(--muted) / 0.3); padding: 8px; border-radius: 4px; border: 1px solid hsl(var(--border)); color: hsl(var(--foreground) / 0.9); }\n          @media print {\n            body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }\n            @page { margin: 20mm; }\n            button, .no-print { display: none !important; }\n          }\n        </style>\n      `);\n      printWindow.document.write('</head><body dir=\"rtl\">');\n      printWindow.document.write('<h1>' + title + '</h1>');\n      const skillDescription = `المهارة: ${item.behavior} (${item.ageRange}) للطفل ${childName}`;\n      printWindow.document.write('<p class=\"dialog-description\">' + skillDescription + '</p>');\n      \n      const sections = contentRef.current.querySelectorAll('div > h4, div > p');\n      let currentSectionHtml = '';\n      sections.forEach(el => {\n          if(el.tagName.toLowerCase() === 'h4') {\n              if(currentSectionHtml) {\n                  printWindow.document.write('<div class=\"analysis-section\">' + currentSectionHtml + '</div>');\n                  currentSectionHtml = '';\n              }\n          }\n          currentSectionHtml += el.outerHTML;\n      });\n      if(currentSectionHtml) { \n          printWindow.document.write('<div class=\"analysis-section\">' + currentSectionHtml + '</div>');\n      }\n\n      printWindow.document.write('</body></html>');\n      printWindow.document.close();\n      printWindow.focus();\n      printWindow.print();\n    } else {\n      toast({ title: \"خطأ\", description: \"يرجى السماح بالنوافذ المنبثقة لطباعة هذا المحتوى.\", variant: \"destructive\"});\n    }\n  };\n\n  const handleAnalyzeSkillForHome = async () => {\n    setIsLoadingHome(true);\n    setHomeAnalysisData(null);\n    try {\n      const input: SkillAnalysisInput = {\n        skillBehavior: item.behavior,\n        ageRange: item.ageRange,\n        childName: childName,\n      };\n      const result = await analyzeSkillForDailyRoutine(input);\n      setHomeAnalysisData(result);\n      setIsHomeDialogOpen(true);\n    } catch (error) {\n      console.error(\"Error analyzing skill for home:\", error);\n      toast({\n        title: \"خطأ في تحليل خطة الأسرة\",\n        description: \"لم نتمكن من تحليل المهارة للروتين المنزلي في الوقت الحالي. يرجى المحاولة مرة أخرى.\",\n        variant: \"destructive\",\n      });\n      setIsHomeDialogOpen(false);\n    } finally {\n      setIsLoadingHome(false);\n    }\n  };\n\n  const handleAnalyzeSkillForPreschool = async () => {\n    setIsLoadingPreschool(true);\n    setPreschoolAnalysisData(null);\n    try {\n      const input: PreschoolSkillAnalysisInput = {\n        skillBehavior: item.behavior,\n        ageRange: item.ageRange,\n        childName: childName,\n      };\n      const result = await analyzeSkillForPreschoolRoutine(input);\n      setPreschoolAnalysisData(result);\n      setIsPreschoolDialogOpen(true);\n    } catch (error) {\n      console.error(\"Error analyzing skill for preschool:\", error);\n      toast({\n        title: \"خطأ في تحليل خطة الروضة\",\n        description: \"لم نتمكن من تحليل المهارة لروتين الروضة/الحضانة في الوقت الحالي. يرجى المحاولة مرة أخرى.\",\n        variant: \"destructive\",\n      });\n      setIsPreschoolDialogOpen(false);\n    } finally {\n      setIsLoadingPreschool(false);\n    }\n  };\n\n  const handleSaveProgress = () => {\n    const assessmentIndex = MOCK_ASSESSMENTS_DATA.findIndex(a => a.id === assessmentId);\n    if (assessmentIndex === -1) {\n      toast({ title: \"خطأ\", description: \"لم يتم العثور على التقييم.\", variant: \"destructive\" });\n      return;\n    }\n    const skillIndex = MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills.findIndex(s => s.skillId === item.skillId);\n    if (skillIndex === -1) {\n      toast({ title: \"خطأ\", description: \"لم يتم العثور على المهارة ضمن التقييم.\", variant: \"destructive\" });\n      return;\n    }\n\n    MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills[skillIndex] = {\n      ...MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills[skillIndex],\n      progressStatus: currentProgressStatus,\n      implementationStartDate: currentImplementationStartDate ? format(currentImplementationStartDate, 'yyyy-MM-dd') : undefined,\n      targetCompletionDate: currentTargetCompletionDate ? format(currentTargetCompletionDate, 'yyyy-MM-dd') : undefined,\n      progressNotes: currentProgressNotes,\n    };\n\n    toast({ title: \"نجاح\", description: \"تم تحديث تتبع التقدم للمهارة.\" });\n    setIsProgressDialogOpen(false);\n    router.refresh();\n  };\n\n  const handleSaveExtendedDeadline = () => {\n     if (!newTargetDateForExtension) {\n      toast({ title: \"خطأ\", description: \"يرجى تحديد تاريخ إنجاز مستهدف جديد.\", variant: \"destructive\" });\n      return;\n    }\n    const assessmentIndex = MOCK_ASSESSMENTS_DATA.findIndex(a => a.id === assessmentId);\n    if (assessmentIndex === -1) {\n      toast({ title: \"خطأ\", description: \"لم يتم العثور على التقييم.\", variant: \"destructive\" });\n      return;\n    }\n    const skillIndex = MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills.findIndex(s => s.skillId === item.skillId);\n    if (skillIndex === -1) {\n      toast({ title: \"خطأ\", description: \"لم يتم العثور على المهارة ضمن التقييم.\", variant: \"destructive\" });\n      return;\n    }\n\n    MOCK_ASSESSMENTS_DATA[assessmentIndex].assessedSkills[skillIndex].targetCompletionDate = format(newTargetDateForExtension, 'yyyy-MM-dd');\n    \n    toast({ title: \"نجاح\", description: \"تم تمديد الموعد المستهدف للمهارة.\" });\n    setIsExtendDeadlineDialogOpen(false);\n    router.refresh();\n  };\n\n\n  const isTrackableSkill = item.status === 'no' || item.status === 'unclear';\n\n  return (\n    <div className=\"p-3 border rounded-md bg-background hover:bg-muted/50 space-y-2\">\n      <div className=\"flex justify-between items-start\">\n        <div>\n          <p className=\"font-medium\">{item.itemNumber}. {item.behavior} <span className=\"text-xs text-muted-foreground\">({item.subCategoryName} - {item.ageRange})</span></p>\n          <p className=\"text-xs text-muted-foreground\">الطريقة: {item.applicationMethod}</p>\n          {item.tools && <p className=\"text-xs text-muted-foreground\">الأدوات: {item.tools}</p>}\n        </div>\n        <div className=\"flex flex-col items-end gap-1\">\n          {statusInfo && (\n            <Badge variant={item.status === 'yes' ? 'default' : item.status === 'no' ? 'destructive' : 'secondary'}\n              className={`whitespace-nowrap ${item.status === 'yes' ? 'bg-green-500 text-white' : item.status === 'no' ? 'bg-red-500 text-white' : 'bg-yellow-500 text-black'}`}>\n              {statusInfo.symbol} {statusInfo.label}\n            </Badge>\n          )}\n          {progressStatusInfo && (\n             <Badge className={`whitespace-nowrap ${progressStatusInfo.colorClass}`}>\n                {progressStatusInfo.symbol} {progressStatusInfo.label}\n             </Badge>\n          )}\n        </div>\n      </div>\n      \n      {item.notes && <p className=\"text-sm text-foreground/80 border-r-2 border-primary pr-2\"><strong>ملاحظات التقييم:</strong> {item.notes}</p>}\n      \n      {(item.progressStatus && item.progressStatus !== 'pending') && (\n        <div className=\"mt-2 p-2 border-t border-dashed space-y-1 text-xs text-muted-foreground\">\n          <p className=\"font-semibold text-primary\">متابعة الهدف:</p>\n          {item.implementationStartDate && <p><strong>تاريخ بدء التنفيذ:</strong> {formatDate(item.implementationStartDate)}</p>}\n          {item.targetCompletionDate && (\n            <p className={isTargetDateOverdue ? 'text-red-600 font-bold' : ''}>\n              <strong>تاريخ الإنجاز المستهدف:</strong> {formatDate(item.targetCompletionDate)}\n              {isTargetDateOverdue && <span className=\"mr-1\">(تجاوز الموعد <AlertTriangle className=\"inline h-3 w-3\"/>)</span>}\n            </p>\n          )}\n          {item.progressNotes && <p className=\"text-sm text-foreground/80 border-r-2 border-accent pr-2\"><strong>ملاحظات التقدم:</strong> {item.progressNotes}</p>}\n        </div>\n      )}\n\n      <div className=\"mt-2 flex flex-wrap gap-2 items-center\">\n        <Dialog open={isHomeDialogOpen} onOpenChange={(open) => {\n          setIsHomeDialogOpen(open);\n          if (!open) setHomeAnalysisData(null); \n        }}>\n          <DialogTrigger asChild>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleAnalyzeSkillForHome} disabled={isLoadingHome}>\n              {isLoadingHome && !homeAnalysisData ? <Loader2 className=\"ml-2 h-4 w-4 animate-spin\" /> : <Sparkles className=\"ml-2 h-4 w-4 text-yellow-500\" />}\n              خطة اسرية\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl max-h-[90vh] flex flex-col\">\n            <DialogHeader>\n              <DialogTitle>تحليل دمج المهارة في الروتين اليومي (الأسرة)</DialogTitle>\n              <DialogDescription>المهارة: {item.behavior} ({item.ageRange}) للطفل {childName}</DialogDescription>\n            </DialogHeader>\n            {isLoadingHome && !homeAnalysisData && <div className=\"flex-grow flex justify-center items-center p-8\"><Loader2 className=\"h-12 w-12 animate-spin text-primary\" /></div>}\n            {homeAnalysisData && (\n              <div ref={homeAnalysisContentRef} className=\"space-y-3 py-4 text-sm overflow-y-auto pr-2\">\n                {Object.entries(homeAnalysisData).map(([key, value]) => {\n                    const label = { mealtime: \"وقت الطعام:\", bathroom: \"وقت استخدام الحمام:\", playtime: \"وقت اللعب:\", outings: \"عند الخروج من المنزل:\", bedtime: \"وقت النوم:\", toolsIntegration: \"استخدام الأدوات:\", generalTips: \"نصائح عامة للتطبيق:\", }[key] || key;\n                    return (<div key={key}><h4 className=\"font-semibold text-primary mb-1\">{label}</h4><p className=\"whitespace-pre-wrap bg-muted/30 p-2 rounded-md border text-foreground/90\">{value}</p></div>);\n                })}\n              </div>\n            )}\n            <DialogFooter className=\"gap-2 sm:justify-end\">\n              <Button variant=\"outline\" onClick={() => handlePrint(`خطة اسرية للمهارة: ${item.behavior}`, homeAnalysisContentRef)} disabled={!homeAnalysisData || isLoadingHome}>\n                <Printer className=\"ml-2 h-4 w-4\" /> طباعة الخطة\n              </Button>\n              <DialogClose asChild><Button variant=\"outline\">إغلاق</Button></DialogClose>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n\n        <Dialog open={isPreschoolDialogOpen} onOpenChange={(open) => {\n          setIsPreschoolDialogOpen(open);\n          if (!open) setPreschoolAnalysisData(null);\n        }}>\n          <DialogTrigger asChild>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleAnalyzeSkillForPreschool} disabled={isLoadingPreschool}>\n              {isLoadingPreschool && !preschoolAnalysisData ? <Loader2 className=\"ml-2 h-4 w-4 animate-spin\" /> : <BookOpen className=\"ml-2 h-4 w-4 text-green-500\" />}\n              خطة الروضة / الحضانة\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl max-h-[90vh] flex flex-col\">\n            <DialogHeader>\n              <DialogTitle>تحليل دمج المهارة في روتين الروضة/الحضانة</DialogTitle>\n              <DialogDescription>المهارة: {item.behavior} ({item.ageRange}) للطفل {childName}</DialogDescription>\n            </DialogHeader>\n            {isLoadingPreschool && !preschoolAnalysisData && <div className=\"flex-grow flex justify-center items-center p-8\"><Loader2 className=\"h-12 w-12 animate-spin text-primary\" /></div>}\n            {preschoolAnalysisData && (\n              <div ref={preschoolAnalysisContentRef} className=\"space-y-3 py-4 text-sm overflow-y-auto pr-2\">\n                 {Object.entries(preschoolAnalysisData).map(([key, value]) => {\n                    const label = { arrivalTime: \"وقت الوصول:\", morningCircle: \"الدائرة الصباحية:\", activityTransition: \"الانتقال بين الأنشطة:\", learningCenters: \"المراكز/أركان التعلم:\", outdoorPlay: \"الساحة الخارجية:\", preschoolBathroom: \"وقت الحمام (في الروضة):\", preschoolSnackTime: \"الوجبة الخفيفة/الغداء (في الروضة):\", storyTime: \"وقت القصة:\", departureTime: \"المغادرة:\", preschoolToolsIntegration: \"استخدام الأدوات (في الروضة):\", teacherGeneralTips: \"نصائح عامة للتطبيق للمعلمين:\",}[key] || key;\n                    return (<div key={key}><h4 className=\"font-semibold text-primary mb-1\">{label}</h4><p className=\"whitespace-pre-wrap bg-muted/30 p-2 rounded-md border text-foreground/90\">{value}</p></div>);\n                })}\n              </div>\n            )}\n            <DialogFooter className=\"gap-2 sm:justify-end\">\n               <Button variant=\"outline\" onClick={() => handlePrint(`خطة الروضة/الحضانة للمهارة: ${item.behavior}`, preschoolAnalysisContentRef)} disabled={!preschoolAnalysisData || isLoadingPreschool}>\n                <Printer className=\"ml-2 h-4 w-4\" /> طباعة الخطة\n              </Button>\n              <DialogClose asChild><Button variant=\"outline\">إغلاق</Button></DialogClose>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n\n        {isTrackableSkill && (\n          <>\n            <Dialog open={isProgressDialogOpen} onOpenChange={setIsProgressDialogOpen}>\n              <DialogTrigger asChild>\n                <Button variant=\"outline\" size=\"sm\">\n                    <Edit3 className=\"ml-2 h-4 w-4\" /> بدء/تحديث التتبع\n                </Button>\n              </DialogTrigger>\n              <DialogContent className=\"sm:max-w-md\">\n                <DialogHeader>\n                  <DialogTitle>تتبع تقدم المهارة: {item.behavior}</DialogTitle>\n                  <DialogDescription>\n                    حدد حالة التقدم، تواريخ البدء والإنجاز، وأضف ملاحظات.\n                  </DialogDescription>\n                </DialogHeader>\n                <div className=\"grid gap-4 py-4\">\n                  <div>\n                    <Label htmlFor=\"progressStatus\">حالة التقدم</Label>\n                    <Select value={currentProgressStatus} onValueChange={(val) => setCurrentProgressStatus(val as ProgressStatus)}>\n                      <SelectTrigger id=\"progressStatus\"><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        {PROGRESS_STATUS_OPTIONS.map(opt => (\n                          <SelectItem key={opt.value} value={opt.value}>{opt.symbol} {opt.label}</SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"implementationStartDate\">تاريخ بدء التنفيذ</Label>\n                    <DatePicker date={currentImplementationStartDate} setDate={setCurrentImplementationStartDate} buttonClassName=\"w-full\" />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"targetCompletionDate\">تاريخ الإنجاز المستهدف</Label>\n                    <DatePicker date={currentTargetCompletionDate} setDate={setCurrentTargetCompletionDate} buttonClassName=\"w-full\"/>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"progressNotes\">ملاحظات التقدم</Label>\n                    <Textarea \n                      id=\"progressNotes\" \n                      value={currentProgressNotes} \n                      onChange={(e) => setCurrentProgressNotes(e.target.value)}\n                      placeholder=\"أدخل ملاحظات حول التقدم المحرز هنا...\"\n                    />\n                  </div>\n                </div>\n                <DialogFooter>\n                  <DialogClose asChild><Button variant=\"outline\">إلغاء</Button></DialogClose>\n                  <Button onClick={handleSaveProgress}><Save className=\"ml-2 h-4 w-4\" /> حفظ التقدم</Button>\n                </DialogFooter>\n              </DialogContent>\n            </Dialog>\n\n            {item.progressStatus === 'implemented' && item.targetCompletionDate && (\n                 <Dialog open={isExtendDeadlineDialogOpen} onOpenChange={setIsExtendDeadlineDialogOpen}>\n                    <DialogTrigger asChild>\n                        <Button variant=\"outline\" size=\"sm\">\n                            <CalendarClock className=\"ml-2 h-4 w-4\" /> تمديد الموعد\n                        </Button>\n                    </DialogTrigger>\n                    <DialogContent className=\"sm:max-w-xs\">\n                        <DialogHeader>\n                        <DialogTitle>تمديد الموعد المستهدف</DialogTitle>\n                        <DialogDescription>\n                            المهارة: {item.behavior}<br/>\n                            الموعد الحالي: {formatDate(item.targetCompletionDate)}\n                        </DialogDescription>\n                        </DialogHeader>\n                        <div className=\"py-4\">\n                        <Label htmlFor=\"newTargetDate\">تاريخ الإنجاز المستهدف الجديد</Label>\n                        <DatePicker date={newTargetDateForExtension} setDate={setNewTargetDateForExtension} buttonClassName=\"w-full\"/>\n                        </div>\n                        <DialogFooter>\n                        <DialogClose asChild><Button variant=\"outline\">إلغاء</Button></DialogClose>\n                        <Button onClick={handleSaveExtendedDeadline}><Save className=\"ml-2 h-4 w-4\" /> حفظ الموعد الجديد</Button>\n                        </DialogFooter>\n                    </DialogContent>\n                 </Dialog>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAIA,sNAAwG,8BAA8B;AACtI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4OAA6C,kBAAkB;AAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA,qPAAqD,eAAe;AAApE;AAAA;;;AAlBA;;;;;;;;;;;;;;;;;AAmCe,SAAS,oBAAoB,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAA4B;;IACrG,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,qBAAqB;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACrF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEtD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IACxG,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3D,kCAAkC;IAClC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B,KAAK,cAAc,IAAI;IACtH,MAAM,CAAC,gCAAgC,kCAAkC,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjF,KAAK,uBAAuB,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uBAAuB,IAAI;IAE1E,MAAM,CAAC,6BAA6B,+BAA+B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC3E,KAAK,oBAAoB,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oBAAoB,IAAI;IAEpE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,aAAa,IAAI;IAEvF,gCAAgC;IAChC,MAAM,CAAC,4BAA4B,8BAA8B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7E,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvE,KAAK,oBAAoB,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oBAAoB,IAAI;IAGpE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,aAAa,0HAAA,CAAA,uBAAoB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,KAAK,MAAM;IACzE,MAAM,qBAAqB,0HAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,KAAK,KAAK,KAAK,cAAc;IAE9F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,KAAK,oBAAoB,EAAE;gBAC7B,IAAI;oBACF,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oBAAoB;oBACrD,uBAAuB,CAAA,GAAA,yIAAA,CAAA,SAAM,AAAD,EAAE,eAAe,KAAK,cAAc,KAAK;gBACvE,EAAE,OAAO,GAAG;oBACV,uBAAuB;gBACzB;YACF,OAAO;gBACL,uBAAuB;YACzB;YACA,4CAA4C;YAC5C,yBAAyB,KAAK,cAAc,IAAI;YAChD,kCAAkC,KAAK,uBAAuB,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,uBAAuB,IAAI;YAC1G,+BAA+B,KAAK,oBAAoB,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oBAAoB,IAAI;YACjG,wBAAwB,KAAK,aAAa,IAAI;YAC9C,6BAA6B,KAAK,oBAAoB,GAAG,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,oBAAoB,IAAI;QAEjG;wCAAG;QAAC,KAAK,oBAAoB;QAAE,KAAK,cAAc;QAAE,KAAK,uBAAuB;QAAE,KAAK,aAAa;KAAC;IAGrG,MAAM,cAAc,CAAC,OAAe;QAClC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,MAAM;gBAAE,OAAO;gBAAkB,aAAa;gBAAsC,SAAS;YAAc;YAC3G;QACF;QACA,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,aAAa;YACf,YAAY,QAAQ,CAAC,KAAK,CAAC,wBAAwB,QAAQ;YAC3D,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;MAc5B,CAAC;YACD,YAAY,QAAQ,CAAC,KAAK,CAAC;YAC3B,YAAY,QAAQ,CAAC,KAAK,CAAC,SAAS,QAAQ;YAC5C,MAAM,mBAAmB,CAAC,SAAS,EAAE,KAAK,QAAQ,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,QAAQ,EAAE,WAAW;YAC1F,YAAY,QAAQ,CAAC,KAAK,CAAC,mCAAmC,mBAAmB;YAEjF,MAAM,WAAW,WAAW,OAAO,CAAC,gBAAgB,CAAC;YACrD,IAAI,qBAAqB;YACzB,SAAS,OAAO,CAAC,CAAA;gBACb,IAAG,GAAG,OAAO,CAAC,WAAW,OAAO,MAAM;oBAClC,IAAG,oBAAoB;wBACnB,YAAY,QAAQ,CAAC,KAAK,CAAC,mCAAmC,qBAAqB;wBACnF,qBAAqB;oBACzB;gBACJ;gBACA,sBAAsB,GAAG,SAAS;YACtC;YACA,IAAG,oBAAoB;gBACnB,YAAY,QAAQ,CAAC,KAAK,CAAC,mCAAmC,qBAAqB;YACvF;YAEA,YAAY,QAAQ,CAAC,KAAK,CAAC;YAC3B,YAAY,QAAQ,CAAC,KAAK;YAC1B,YAAY,KAAK;YACjB,YAAY,KAAK;QACnB,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAqD,SAAS;YAAa;QAChH;IACF;IAEA,MAAM,4BAA4B;QAChC,iBAAiB;QACjB,oBAAoB;QACpB,IAAI;YACF,MAAM,QAA4B;gBAChC,eAAe,KAAK,QAAQ;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,WAAW;YACb;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,oKAAA,CAAA,8BAA2B,AAAD,EAAE;YACjD,oBAAoB;YACpB,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA,oBAAoB;QACtB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,iCAAiC;QACrC,sBAAsB;QACtB,yBAAyB;QACzB,IAAI;YACF,MAAM,QAAqC;gBACzC,eAAe,KAAK,QAAQ;gBAC5B,UAAU,KAAK,QAAQ;gBACvB,WAAW;YACb;YACA,MAAM,SAAS,MAAM,CAAA,GAAA,wKAAA,CAAA,kCAA+B,AAAD,EAAE;YACrD,yBAAyB;YACzB,yBAAyB;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA,yBAAyB;QAC3B,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,kBAAkB,0HAAA,CAAA,wBAAqB,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtE,IAAI,oBAAoB,CAAC,GAAG;YAC1B,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA8B,SAAS;YAAc;YACxF;QACF;QACA,MAAM,aAAa,0HAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;QAClH,IAAI,eAAe,CAAC,GAAG;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA0C,SAAS;YAAc;YACpG;QACF;QAEA,0HAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,GAAG;YAClE,GAAG,0HAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW;YACpE,gBAAgB;YAChB,yBAAyB,iCAAiC,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,gCAAgC,gBAAgB;YACjH,sBAAsB,8BAA8B,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,6BAA6B,gBAAgB;YACxG,eAAe;QACjB;QAEA,MAAM;YAAE,OAAO;YAAQ,aAAa;QAAgC;QACpE,wBAAwB;QACxB,OAAO,OAAO;IAChB;IAEA,MAAM,6BAA6B;QAChC,IAAI,CAAC,2BAA2B;YAC/B,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAuC,SAAS;YAAc;YACjG;QACF;QACA,MAAM,kBAAkB,0HAAA,CAAA,wBAAqB,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtE,IAAI,oBAAoB,CAAC,GAAG;YAC1B,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA8B,SAAS;YAAc;YACxF;QACF;QACA,MAAM,aAAa,0HAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,KAAK,OAAO;QAClH,IAAI,eAAe,CAAC,GAAG;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA0C,SAAS;YAAc;YACpG;QACF;QAEA,0HAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,oBAAoB,GAAG,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,2BAA2B;QAE3H,MAAM;YAAE,OAAO;YAAQ,aAAa;QAAoC;QACxE,8BAA8B;QAC9B,OAAO,OAAO;IAChB;IAGA,MAAM,mBAAmB,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,KAAK;IAEjE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;;oCAAe,KAAK,UAAU;oCAAC;oCAAG,KAAK,QAAQ;oCAAC;kDAAC,6LAAC;wCAAK,WAAU;;4CAAgC;4CAAE,KAAK,eAAe;4CAAC;4CAAI,KAAK,QAAQ;4CAAC;;;;;;;;;;;;;0CACvJ,6LAAC;gCAAE,WAAU;;oCAAgC;oCAAU,KAAK,iBAAiB;;;;;;;4BAC5E,KAAK,KAAK,kBAAI,6LAAC;gCAAE,WAAU;;oCAAgC;oCAAU,KAAK,KAAK;;;;;;;;;;;;;kCAElF,6LAAC;wBAAI,WAAU;;4BACZ,4BACC,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,YAAY,KAAK,MAAM,KAAK,OAAO,gBAAgB;gCACzF,WAAW,CAAC,kBAAkB,EAAE,KAAK,MAAM,KAAK,QAAQ,4BAA4B,KAAK,MAAM,KAAK,OAAO,0BAA0B,4BAA4B;;oCAChK,WAAW,MAAM;oCAAC;oCAAE,WAAW,KAAK;;;;;;;4BAGxC,oCACE,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,UAAU,EAAE;;oCAClE,mBAAmB,MAAM;oCAAC;oCAAE,mBAAmB,KAAK;;;;;;;;;;;;;;;;;;;YAM9D,KAAK,KAAK,kBAAI,6LAAC;gBAAE,WAAU;;kCAA4D,6LAAC;kCAAO;;;;;;oBAAyB;oBAAE,KAAK,KAAK;;;;;;;YAEnI,KAAK,cAAc,IAAI,KAAK,cAAc,KAAK,2BAC/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;oBACzC,KAAK,uBAAuB,kBAAI,6LAAC;;0CAAE,6LAAC;0CAAO;;;;;;4BAA2B;4BAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,uBAAuB;;;;;;;oBAC/G,KAAK,oBAAoB,kBACxB,6LAAC;wBAAE,WAAW,sBAAsB,2BAA2B;;0CAC7D,6LAAC;0CAAO;;;;;;4BAAgC;4BAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,oBAAoB;4BAC7E,qCAAuB,6LAAC;gCAAK,WAAU;;oCAAO;kDAAc,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAkB;;;;;;;;;;;;;oBAG3G,KAAK,aAAa,kBAAI,6LAAC;wBAAE,WAAU;;0CAA2D,6LAAC;0CAAO;;;;;;4BAAwB;4BAAE,KAAK,aAAa;;;;;;;;;;;;;0BAIvJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAkB,cAAc,CAAC;4BAC7C,oBAAoB;4BACpB,IAAI,CAAC,MAAM,oBAAoB;wBACjC;;0CACE,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;oCAA2B,UAAU;;wCAC/E,iBAAiB,CAAC,iCAAmB,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAiC,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAkC;;;;;;;;;;;;0CAIpJ,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;;oDAAC;oDAAU,KAAK,QAAQ;oDAAC;oDAAG,KAAK,QAAQ;oDAAC;oDAAS;;;;;;;;;;;;;oCAEtE,iBAAiB,CAAC,kCAAoB,6LAAC;wCAAI,WAAU;kDAAiD,cAAA,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;oCACzH,kCACC,6LAAC;wCAAI,KAAK;wCAAwB,WAAU;kDACzC,OAAO,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;4CAC/C,MAAM,QAAQ;gDAAE,UAAU;gDAAe,UAAU;gDAAuB,UAAU;gDAAc,SAAS;gDAAyB,SAAS;gDAAc,kBAAkB;gDAAoB,aAAa;4CAAuB,CAAC,CAAC,IAAI,IAAI;4CAC/O,qBAAQ,6LAAC;;kEAAc,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAAW,6LAAC;wDAAE,WAAU;kEAA4E;;;;;;;+CAA1J;;;;;wCACtB;;;;;;kDAGJ,6LAAC,qIAAA,CAAA,eAAY;wCAAC,WAAU;;0DACtB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,YAAY,CAAC,mBAAmB,EAAE,KAAK,QAAQ,EAAE,EAAE;gDAAyB,UAAU,CAAC,oBAAoB;;kEAClJ,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAEtC,6LAAC,qIAAA,CAAA,cAAW;gDAAC,OAAO;0DAAC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrD,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAuB,cAAc,CAAC;4BAClD,yBAAyB;4BACzB,IAAI,CAAC,MAAM,yBAAyB;wBACtC;;0CACE,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;oCAAgC,UAAU;;wCACpF,sBAAsB,CAAC,sCAAwB,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;iEAAiC,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiC;;;;;;;;;;;;0CAI7J,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;;oDAAC;oDAAU,KAAK,QAAQ;oDAAC;oDAAG,KAAK,QAAQ;oDAAC;oDAAS;;;;;;;;;;;;;oCAEtE,sBAAsB,CAAC,uCAAyB,6LAAC;wCAAI,WAAU;kDAAiD,cAAA,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;oCACnI,uCACC,6LAAC;wCAAI,KAAK;wCAA6B,WAAU;kDAC7C,OAAO,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;4CACrD,MAAM,QAAQ;gDAAE,aAAa;gDAAe,eAAe;gDAAqB,oBAAoB;gDAAyB,iBAAiB;gDAAyB,aAAa;gDAAoB,mBAAmB;gDAA2B,oBAAoB;gDAAsC,WAAW;gDAAc,eAAe;gDAAa,2BAA2B;gDAAgC,oBAAoB;4CAA+B,CAAC,CAAC,IAAI,IAAI;4CAC7d,qBAAQ,6LAAC;;kEAAc,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEAAW,6LAAC;wDAAE,WAAU;kEAA4E;;;;;;;+CAA1J;;;;;wCACtB;;;;;;kDAGJ,6LAAC,qIAAA,CAAA,eAAY;wCAAC,WAAU;;0DACrB,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,YAAY,CAAC,4BAA4B,EAAE,KAAK,QAAQ,EAAE,EAAE;gDAA8B,UAAU,CAAC,yBAAyB;;kEACtK,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAEtC,6LAAC,qIAAA,CAAA,cAAW;gDAAC,OAAO;0DAAC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAKpD,kCACC;;0CACE,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAM;gCAAsB,cAAc;;kDAChD,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,OAAO;kDACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC3B,6LAAC,6MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAGxC,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,6LAAC,qIAAA,CAAA,eAAY;;kEACX,6LAAC,qIAAA,CAAA,cAAW;;4DAAC;4DAAoB,KAAK,QAAQ;;;;;;;kEAC9C,6LAAC,qIAAA,CAAA,oBAAiB;kEAAC;;;;;;;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAiB;;;;;;0EAChC,6LAAC,qIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAuB,eAAe,CAAC,MAAQ,yBAAyB;;kFACrF,6LAAC,qIAAA,CAAA,gBAAa;wEAAC,IAAG;kFAAiB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kFAC/C,6LAAC,qIAAA,CAAA,gBAAa;kFACX,0HAAA,CAAA,0BAAuB,CAAC,GAAG,CAAC,CAAA,oBAC3B,6LAAC,qIAAA,CAAA,aAAU;gFAAiB,OAAO,IAAI,KAAK;;oFAAG,IAAI,MAAM;oFAAC;oFAAE,IAAI,KAAK;;+EAApD,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;kEAKlC,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA0B;;;;;;0EACzC,6LAAC,6IAAA,CAAA,aAAU;gEAAC,MAAM;gEAAgC,SAAS;gEAAmC,iBAAgB;;;;;;;;;;;;kEAEhH,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAuB;;;;;;0EACtC,6LAAC,6IAAA,CAAA,aAAU;gEAAC,MAAM;gEAA6B,SAAS;gEAAgC,iBAAgB;;;;;;;;;;;;kEAE1G,6LAAC;;0EACC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAgB;;;;;;0EAC/B,6LAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;gEACvD,aAAY;;;;;;;;;;;;;;;;;;0DAIlB,6LAAC,qIAAA,CAAA,eAAY;;kEACX,6LAAC,qIAAA,CAAA,cAAW;wDAAC,OAAO;kEAAC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;sEAAU;;;;;;;;;;;kEAC/C,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAS;;0EAAoB,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;4BAK3E,KAAK,cAAc,KAAK,iBAAiB,KAAK,oBAAoB,kBAC9D,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAM;gCAA4B,cAAc;;kDACrD,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,OAAO;kDAClB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;;8DAC3B,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAGlD,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACrB,6LAAC,qIAAA,CAAA,eAAY;;kEACb,6LAAC,qIAAA,CAAA,cAAW;kEAAC;;;;;;kEACb,6LAAC,qIAAA,CAAA,oBAAiB;;4DAAC;4DACL,KAAK,QAAQ;0EAAC,6LAAC;;;;;4DAAI;4DACb,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,oBAAoB;;;;;;;;;;;;;0DAGxD,6LAAC;gDAAI,WAAU;;kEACf,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAgB;;;;;;kEAC/B,6LAAC,6IAAA,CAAA,aAAU;wDAAC,MAAM;wDAA2B,SAAS;wDAA8B,iBAAgB;;;;;;;;;;;;0DAEpG,6LAAC,qIAAA,CAAA,eAAY;;kEACb,6LAAC,qIAAA,CAAA,cAAW;wDAAC,OAAO;kEAAC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;sEAAU;;;;;;;;;;;kEAC/C,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAS;;0EAA4B,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtG;GAhZwB;;QACP,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;;;KAFJ", "debugId": null}}, {"offset": {"line": 2652, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/children/%5BchildId%5D/assessment/%5BassessmentId%5D/page.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { useState, useMemo, useEffect } from 'react';\nimport { PORTAGE_CHECKLIST_DATA, SKILL_STATUS_OPTIONS, PROGRESS_STATUS_OPTIONS } from '@/lib/constants';\nimport { useChildren, useAssessments } from '@/hooks/use-storage';\nimport type { Child, Assessment, PortageSkillItem, PortageDimension, PortageSubCategory, SkillStatus, ProgressStatus } from '@/lib/types';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { ArrowRight, Edit } from 'lucide-react';\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\nimport { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';\nimport { formatDate } from '@/lib/utils';\nimport AgeDisplay from '@/components/assessment/AgeDisplay';\nimport AnalyzableSkillItem, { type AnalyzableItemType as ImportedAnalyzableItemType } from '@/components/assessment/AnalyzableSkillItem';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Label } from \"@/components/ui/label\";\n\n// Enhanced AnalyzableItemType for internal use in this page, including IDs for filtering\ntype PageAnalyzableItemType = ImportedAnalyzableItemType & {\n  dimensionId: string;\n  subCategoryId: string;\n};\n\nconst ALL_DIMENSIONS_VALUE = \"all-dimensions\";\nconst ALL_SUBCATEGORIES_VALUE = \"all-subcategories\";\nconst ALL_SKILL_STATUS_VALUE = \"all-skill-status\"; // Renamed from ALL_STATUS_VALUE for clarity\nconst ALL_PROGRESS_STATUS_VALUE = \"all-progress-status\";\n\n\n\n\n// This function needs to be available client-side or its result passed down.\nconst getSkillDetails = (skillId: string): (PortageSkillItem & { dimensionId: string, dimensionName: string; subCategoryId: string, subCategoryName: string; }) | null => {\n  for (const dimension of PORTAGE_CHECKLIST_DATA) {\n    for (const subCategory of dimension.subCategories) {\n      const skill = subCategory.skills.find(s => s.id === skillId);\n      if (skill) return { ...skill, dimensionId: dimension.id, dimensionName: dimension.name, subCategoryId: subCategory.id, subCategoryName: subCategory.name };\n    }\n  }\n  return null;\n};\n\ninterface AnalysisResult {\n  baselineSkill: PageAnalyzableItemType | null;\n  ceilingSkill: PageAnalyzableItemType | null;\n  teachingRangeSkills: PageAnalyzableItemType[];\n}\n\ninterface SubCategoryAnalysisData extends AnalysisResult {\n  subCategoryName: string;\n  skillsCount: number;\n}\n\ninterface DimensionAnalysisData {\n  dimensionName: string;\n  subCategories: SubCategoryAnalysisData[];\n}\n\nfunction calculateAnalysisForSubCategory(skillsInSubCategory: PageAnalyzableItemType[]): AnalysisResult {\n  const sortedSkills = [...skillsInSubCategory].sort((a, b) => {\n    const numA = parseInt(a.itemNumber, 10);\n    const numB = parseInt(b.itemNumber, 10);\n    return numA - numB;\n  });\n\n  let baselineSkill: PageAnalyzableItemType | null = null;\n  let ceilingSkill: PageAnalyzableItemType | null = null;\n  const teachingRangeSkills: PageAnalyzableItemType[] = [];\n\n  // Baseline: highest numbered skill in the first sequence of 3 consecutive \"yes\"\n  for (let i = 0; i <= sortedSkills.length - 3; i++) {\n    if (\n      sortedSkills[i].status === 'yes' &&\n      sortedSkills[i + 1].status === 'yes' &&\n      sortedSkills[i + 2].status === 'yes'\n    ) {\n      baselineSkill = sortedSkills[i + 2]; // The highest of the three\n      break;\n    }\n  }\n  // If no 3 consecutive \"yes\", baseline is the lowest numbered \"yes\" skill\n  if (!baselineSkill) {\n    baselineSkill = sortedSkills.find(skill => skill.status === 'yes') || null;\n  }\n\n\n  // Ceiling: lowest numbered skill in the first sequence of 3 consecutive \"no\"\n  for (let i = 0; i <= sortedSkills.length - 3; i++) {\n    if (\n      sortedSkills[i].status === 'no' &&\n      sortedSkills[i + 1].status === 'no' &&\n      sortedSkills[i + 2].status === 'no'\n    ) {\n      ceilingSkill = sortedSkills[i]; // The lowest of the three\n      break;\n    }\n  }\n  // If no 3 consecutive \"no\", ceiling is the highest numbered \"no\" skill\n  if (!ceilingSkill) {\n     let highestNoSkill: PageAnalyzableItemType | null = null;\n    for (let i = sortedSkills.length - 1; i >= 0; i--) {\n        if (sortedSkills[i].status === 'no') {\n            highestNoSkill = sortedSkills[i];\n            break;\n        }\n    }\n    if (highestNoSkill) {\n        ceilingSkill = highestNoSkill;\n    }\n  }\n\n  let teachingStartIndex = 0;\n  if (baselineSkill) {\n    const baselineIdx = sortedSkills.findIndex(s => s.skillId === baselineSkill!.skillId);\n    if (baselineIdx !== -1) {\n      teachingStartIndex = baselineIdx + 1; // Start teaching skills *after* the baseline\n    }\n  }\n\n  for (let i = teachingStartIndex; i < sortedSkills.length; i++) {\n    const skill = sortedSkills[i];\n    const skillItemNumber = parseInt(skill.itemNumber, 10);\n\n    // Stop if we've passed the ceiling skill (current skill is numerically greater than ceiling)\n    if (ceilingSkill && skillItemNumber > parseInt(ceilingSkill.itemNumber, 10)) {\n      break;\n    }\n    // Include skills marked 'no' or 'unclear' within the teaching range\n    if (skill.status === 'no' || skill.status === 'unclear') {\n      teachingRangeSkills.push(skill);\n    }\n  }\n\n  return { baselineSkill, ceilingSkill, teachingRangeSkills };\n}\n\n// Props for the client component part\ninterface ViewAssessmentClientProps {\n  child: Child;\n  assessment: Assessment;\n  portageChecklist: PortageDimension[]; // Pass the whole checklist\n}\n\nfunction ViewAssessmentClientContent({ child, assessment, portageChecklist }: ViewAssessmentClientProps) {\n  const [selectedDimensionId, setSelectedDimensionId] = useState<string>(ALL_DIMENSIONS_VALUE);\n  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<string>(ALL_SUBCATEGORIES_VALUE);\n  const [selectedSkillStatus, setSelectedSkillStatus] = useState<string>(ALL_SKILL_STATUS_VALUE);\n  const [selectedProgressStatus, setSelectedProgressStatus] = useState<string>(ALL_PROGRESS_STATUS_VALUE);\n\n\n  const dimensionOptions = useMemo(() => {\n    return [{ value: ALL_DIMENSIONS_VALUE, label: \"الكل - البعد\" }, ...portageChecklist.map(d => ({ value: d.id, label: d.name }))];\n  }, [portageChecklist]);\n\n  const subCategoryOptions = useMemo(() => {\n    const options = [{ value: ALL_SUBCATEGORIES_VALUE, label: \"الكل - المجال\" }];\n    if (selectedDimensionId !== ALL_DIMENSIONS_VALUE) {\n        const dimension = portageChecklist.find(d => d.id === selectedDimensionId);\n        if (dimension) {\n            dimension.subCategories.forEach(sc => {\n                options.push({ value: sc.id, label: sc.name });\n            });\n        }\n    }\n    return options;\n  }, [selectedDimensionId, portageChecklist]);\n\n  const skillStatusOptions = useMemo(() => {\n    return [{ value: ALL_SKILL_STATUS_VALUE, label: \"الكل - حالة المهارة\" }, ...SKILL_STATUS_OPTIONS.map(s => ({ value: s.value, label: s.label }))]\n  }, []);\n\n  const progressStatusOptions = useMemo(() => {\n    return [{ value: ALL_PROGRESS_STATUS_VALUE, label: \"الكل - حالة التقدم\" }, ...PROGRESS_STATUS_OPTIONS.map(s => ({ value: s.value, label: s.label }))]\n  }, []);\n\n  const allAnalyzableSkills: PageAnalyzableItemType[] = useMemo(() => {\n       return assessment.assessedSkills.map(assessedSkill => {\n           const details = getSkillDetails(assessedSkill.skillId);\n           if (!details) {\n             return null;\n           }\n           return {\n               ...assessedSkill,\n               ...details,\n               skillId: assessedSkill.skillId,\n           };\n       }).filter(Boolean) as PageAnalyzableItemType[];\n   }, [assessment.assessedSkills]);\n\n   const filteredSkills = useMemo(() => {\n       return allAnalyzableSkills.filter(skill => {\n           const dimensionMatch = selectedDimensionId === ALL_DIMENSIONS_VALUE || skill.dimensionId === selectedDimensionId;\n           const subCategoryMatch = selectedSubCategoryId === ALL_SUBCATEGORIES_VALUE || skill.subCategoryId === selectedSubCategoryId;\n           const skillStatusMatch = selectedSkillStatus === ALL_SKILL_STATUS_VALUE || skill.status === selectedSkillStatus;\n\n           let progressStatusMatch = selectedProgressStatus === ALL_PROGRESS_STATUS_VALUE;\n           if (!progressStatusMatch) {\n             if (selectedProgressStatus === 'pending') {\n               progressStatusMatch = skill.progressStatus === 'pending' || skill.progressStatus === undefined;\n             } else {\n               progressStatusMatch = skill.progressStatus === selectedProgressStatus;\n             }\n           }\n\n           return dimensionMatch && subCategoryMatch && skillStatusMatch && progressStatusMatch;\n       });\n   }, [allAnalyzableSkills, selectedDimensionId, selectedSubCategoryId, selectedSkillStatus, selectedProgressStatus]);\n\n  const finalGroupedSkillsForDisplay: Record<string, PageAnalyzableItemType[]> = useMemo(() => {\n      const grouped: Record<string, PageAnalyzableItemType[]> = {};\n      filteredSkills.forEach(skill => {\n          if (!grouped[skill.dimensionName]) {\n              grouped[skill.dimensionName] = [];\n          }\n          grouped[skill.dimensionName].push(skill);\n      });\n      for (const dimName in grouped) {\n          grouped[dimName].sort((a, b) => parseInt(a.itemNumber, 10) - parseInt(b.itemNumber, 10));\n      }\n      return grouped;\n  }, [filteredSkills]);\n\n\n  const allAnalysisData: DimensionAnalysisData[] = useMemo(() => {\n    return portageChecklist.map(dimension => {\n      const subCategoriesData: SubCategoryAnalysisData[] = dimension.subCategories.map(subCategory => {\n        const skillsForSubCategory: PageAnalyzableItemType[] = [];\n        assessment.assessedSkills.forEach(assessedSkill => {\n          const details = getSkillDetails(assessedSkill.skillId);\n          if (details && details.dimensionId === dimension.id && details.subCategoryId === subCategory.id) {\n            skillsForSubCategory.push({\n              ...assessedSkill,\n              ...details,\n              skillId: assessedSkill.skillId,\n            });\n          }\n        });\n        skillsForSubCategory.sort((a, b) => parseInt(a.itemNumber, 10) - parseInt(b.itemNumber, 10));\n        const analysis = calculateAnalysisForSubCategory(skillsForSubCategory);\n        return {\n          subCategoryName: subCategory.name,\n          skillsCount: skillsForSubCategory.length,\n          ...analysis,\n        };\n      });\n\n      return {\n        dimensionName: dimension.name,\n        subCategories: subCategoriesData.filter(sc => sc.skillsCount > 0),\n      };\n    }).filter(dim => dim.subCategories.length > 0);\n  }, [assessment.assessedSkills, portageChecklist]);\n\n\n  return (\n    <div className=\"container mx-auto py-8\">\n      <Link href={`/children/${child.id}/assessment`} className=\"inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4\">\n        العودة إلى سجل تقييمات {child.name}\n        <ArrowRight className=\"h-4 w-4\" />\n      </Link>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-primary\">تفاصيل التقييم لـ {child.name}</h1>\n          <p className=\"text-muted-foreground\">تاريخ التقييم: {formatDate(assessment.assessmentDate)}</p>\n          <AgeDisplay birthDate={child.birthDate} assessmentDate={assessment.assessmentDate} label=\"عمر الطفل عند التقييم:\" className=\"text-sm\" />\n        </div>\n        <Link href={`/children/${child.id}/assessment/${assessment.id}/edit`}>\n          <Button variant=\"outline\">\n            <Edit className=\"ml-2 h-4 w-4\" /> تعديل التقييم\n          </Button>\n        </Link>\n      </div>\n\n      <Card className=\"shadow-lg\">\n        <CardHeader>\n          <CardTitle>المهارات المقيمة</CardTitle>\n          <CardDescription>مراجعة أداء الطفل في كل بند مهارة. استخدم الفلاتر أدناه لتسهيل التصفح.</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"mb-6 p-4 border rounded-lg shadow-sm bg-muted/30\">\n            <h3 className=\"text-lg font-semibold mb-3 text-primary\">فلاتر العرض</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              <div>\n                <Label htmlFor=\"dimensionFilterDisplay\" className=\"mb-1 block text-sm font-medium\">البعد:</Label>\n                <Select value={selectedDimensionId} onValueChange={setSelectedDimensionId}>\n                  <SelectTrigger id=\"dimensionFilterDisplay\"><SelectValue placeholder=\"اختر البعد\" /></SelectTrigger>\n                  <SelectContent>\n                    {dimensionOptions.map(option => (\n                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"subCategoryFilterDisplay\" className=\"mb-1 block text-sm font-medium\">المجال:</Label>\n                <Select value={selectedSubCategoryId} onValueChange={setSelectedSubCategoryId} disabled={selectedDimensionId === ALL_DIMENSIONS_VALUE && subCategoryOptions.length <=1 }>\n                  <SelectTrigger id=\"subCategoryFilterDisplay\"><SelectValue placeholder=\"اختر المجال\" /></SelectTrigger>\n                  <SelectContent>\n                    {subCategoryOptions.map(option => (\n                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"skillStatusFilterDisplay\" className=\"mb-1 block text-sm font-medium\">حالة المهارة:</Label>\n                <Select value={selectedSkillStatus} onValueChange={setSelectedSkillStatus}>\n                  <SelectTrigger id=\"skillStatusFilterDisplay\"><SelectValue placeholder=\"اختر حالة المهارة\" /></SelectTrigger>\n                  <SelectContent>\n                    {skillStatusOptions.map(option => (\n                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              <div>\n                <Label htmlFor=\"progressStatusFilterDisplay\" className=\"mb-1 block text-sm font-medium\">حالة التقدم:</Label>\n                <Select value={selectedProgressStatus} onValueChange={setSelectedProgressStatus}>\n                  <SelectTrigger id=\"progressStatusFilterDisplay\"><SelectValue placeholder=\"اختر حالة التقدم\" /></SelectTrigger>\n                  <SelectContent>\n                    {progressStatusOptions.map(option => (\n                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n            </div>\n          </div>\n\n          {Object.keys(finalGroupedSkillsForDisplay).length > 0 ? (\n            <Accordion type=\"multiple\" className=\"w-full\" defaultValue={Object.keys(finalGroupedSkillsForDisplay)}>\n              {Object.entries(finalGroupedSkillsForDisplay).map(([dimensionName, skills]) => (\n                <AccordionItem value={dimensionName} key={dimensionName}>\n                  <AccordionTrigger className=\"text-xl font-semibold text-accent\">{dimensionName}</AccordionTrigger>\n                  <AccordionContent>\n                    <div className=\"space-y-4 pr-4\">\n                      {skills.map((item) => (\n                        <AnalyzableSkillItem\n                          key={item.skillId}\n                          item={item}\n                          childName={child.name}\n                          assessmentId={assessment.id}\n                        />\n                      ))}\n                    </div>\n                  </AccordionContent>\n                </AccordionItem>\n              ))}\n            </Accordion>\n          ) : (\n            <p className=\"text-muted-foreground text-center py-6\">لا توجد مهارات تطابق معايير الفلترة المحددة.</p>\n          )}\n        </CardContent>\n      </Card>\n\n      <Card className=\"mt-6 shadow-lg\">\n        <CardHeader>\n          <CardTitle>التحليل: الخط القاعدي، الخط السقفي، ومنطقة التعلم</CardTitle>\n          <CardDescription>يستند هذا التحليل إلى قواعد بورتيج القياسية المطبقة على **جميع** المهارات المقيمة في هذا التقييم.</CardDescription>\n        </CardHeader>\n        <CardContent>\n          {allAnalysisData.length > 0 ? (\n            <Accordion type=\"multiple\" className=\"w-full\" defaultValue={allAnalysisData.map(d => d.dimensionName)}>\n              {allAnalysisData.map(dimensionData => (\n                <AccordionItem value={dimensionData.dimensionName} key={dimensionData.dimensionName}>\n                  <AccordionTrigger className=\"text-xl font-semibold text-primary\">{dimensionData.dimensionName}</AccordionTrigger>\n                  <AccordionContent>\n                    {dimensionData.subCategories.map(subCategoryData => (\n                      <div key={subCategoryData.subCategoryName} className=\"mb-4 p-3 border rounded-md bg-muted/50\">\n                        <h4 className=\"text-lg font-medium text-accent mb-2\">{subCategoryData.subCategoryName}</h4>\n                        <p><strong>الخط القاعدي:</strong> {subCategoryData.baselineSkill ? `${subCategoryData.baselineSkill.itemNumber}. ${subCategoryData.baselineSkill.behavior}` : \"لم يتم تحديده\"}</p>\n                        <p><strong>الخط السقفي:</strong> {subCategoryData.ceilingSkill ? `${subCategoryData.ceilingSkill.itemNumber}. ${subCategoryData.ceilingSkill.behavior}` : \"لم يتم تحديده\"}</p>\n                        {subCategoryData.teachingRangeSkills.length > 0 ? (\n                          <div>\n                            <h5 className=\"font-semibold mt-2\">منطقة التعلم (المهارات المقترحة للتركيز عليها):</h5>\n                            <ul className=\"list-disc pr-5 mt-1 space-y-1 text-sm\">\n                              {subCategoryData.teachingRangeSkills.map(skill => (\n                                <li key={skill.skillId}>{skill.itemNumber}. {skill.behavior} ({skill.ageRange})</li>\n                              ))}\n                            </ul>\n                          </div>\n                        ) : (\n                          <p className=\"mt-2 text-sm text-muted-foreground\">لا توجد مهارات محددة في منطقة التعلم بناءً على هذا التحليل (قد تكون جميع المهارات حتى الخط السقفي متقنة، أو لم يتم تحديد خط أساس واضح، أو لم يتم تحديد سقف واضح).</p>\n                        )}\n                      </div>\n                    ))}\n                    {dimensionData.subCategories.length === 0 && <p className=\"text-sm text-muted-foreground\">لا توجد بيانات تحليل لهذه الفئة الفرعية.</p>}\n                  </AccordionContent>\n                </AccordionItem>\n              ))}\n            </Accordion>\n          ) : (\n            <p className=\"text-muted-foreground\">لا يمكن إنشاء التحليل. قد لا تكون هناك بيانات تقييم كافية أو مهارات مقيمة.</p>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\n// This is the main page component which will fetch data and pass to client component\nexport default function ViewAssessmentPage({ params }: { params: { childId: string, assessmentId: string } }) {\n  const { getChild, loading: childrenLoading } = useChildren();\n  const { getAssessment, loading: assessmentsLoading } = useAssessments(); // Get all assessments\n\n  const [child, setChild] = useState<Child | null>(null);\n  const [assessment, setAssessment] = useState<Assessment | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Wait for hooks to be ready\n    if (childrenLoading || assessmentsLoading) {\n      return;\n    }\n\n    const childData = getChild(params.childId);\n    const assessmentData = getAssessment(params.assessmentId);\n\n    setChild(childData || null);\n    setAssessment(assessmentData || null);\n    setLoading(false);\n  }, [params.childId, params.assessmentId, getChild, getAssessment, childrenLoading, assessmentsLoading]);\n\n  if (loading || childrenLoading || assessmentsLoading) {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-muted-foreground\">جاري تحميل بيانات التقييم...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!child || !assessment) {\n    return (\n      <div className=\"container mx-auto py-8 text-center\">\n        <h1 className=\"text-2xl font-semibold\">التقييم أو الطفل غير موجود</h1>\n        <Link href={`/children/${params.childId}/assessment`}>\n          <Button variant=\"link\">العودة إلى سجل التقييمات</Button>\n        </Link>\n      </div>\n    );\n  }\n\n  return <ViewAssessmentClientContent child={child} assessment={assessment} portageChecklist={PORTAGE_CHECKLIST_DATA} />;\n}\n\n\n\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAfA;;;;;;;;;;;;;;AAuBA,MAAM,uBAAuB;AAC7B,MAAM,0BAA0B;AAChC,MAAM,yBAAyB,oBAAoB,4CAA4C;AAC/F,MAAM,4BAA4B;AAKlC,6EAA6E;AAC7E,MAAM,kBAAkB,CAAC;IACvB,KAAK,MAAM,aAAa,0HAAA,CAAA,yBAAsB,CAAE;QAC9C,KAAK,MAAM,eAAe,UAAU,aAAa,CAAE;YACjD,MAAM,QAAQ,YAAY,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,IAAI,OAAO,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,UAAU,EAAE;gBAAE,eAAe,UAAU,IAAI;gBAAE,eAAe,YAAY,EAAE;gBAAE,iBAAiB,YAAY,IAAI;YAAC;QAC3J;IACF;IACA,OAAO;AACT;AAkBA,SAAS,gCAAgC,mBAA6C;IACpF,MAAM,eAAe;WAAI;KAAoB,CAAC,IAAI,CAAC,CAAC,GAAG;QACrD,MAAM,OAAO,SAAS,EAAE,UAAU,EAAE;QACpC,MAAM,OAAO,SAAS,EAAE,UAAU,EAAE;QACpC,OAAO,OAAO;IAChB;IAEA,IAAI,gBAA+C;IACnD,IAAI,eAA8C;IAClD,MAAM,sBAAgD,EAAE;IAExD,gFAAgF;IAChF,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG,IAAK;QACjD,IACE,YAAY,CAAC,EAAE,CAAC,MAAM,KAAK,SAC3B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,SAC/B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,OAC/B;YACA,gBAAgB,YAAY,CAAC,IAAI,EAAE,EAAE,2BAA2B;YAChE;QACF;IACF;IACA,yEAAyE;IACzE,IAAI,CAAC,eAAe;QAClB,gBAAgB,aAAa,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,UAAU;IACxE;IAGA,6EAA6E;IAC7E,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG,IAAK;QACjD,IACE,YAAY,CAAC,EAAE,CAAC,MAAM,KAAK,QAC3B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,QAC/B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,MAC/B;YACA,eAAe,YAAY,CAAC,EAAE,EAAE,0BAA0B;YAC1D;QACF;IACF;IACA,uEAAuE;IACvE,IAAI,CAAC,cAAc;QAChB,IAAI,iBAAgD;QACrD,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC/C,IAAI,YAAY,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM;gBACjC,iBAAiB,YAAY,CAAC,EAAE;gBAChC;YACJ;QACJ;QACA,IAAI,gBAAgB;YAChB,eAAe;QACnB;IACF;IAEA,IAAI,qBAAqB;IACzB,IAAI,eAAe;QACjB,MAAM,cAAc,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,cAAe,OAAO;QACpF,IAAI,gBAAgB,CAAC,GAAG;YACtB,qBAAqB,cAAc,GAAG,6CAA6C;QACrF;IACF;IAEA,IAAK,IAAI,IAAI,oBAAoB,IAAI,aAAa,MAAM,EAAE,IAAK;QAC7D,MAAM,QAAQ,YAAY,CAAC,EAAE;QAC7B,MAAM,kBAAkB,SAAS,MAAM,UAAU,EAAE;QAEnD,6FAA6F;QAC7F,IAAI,gBAAgB,kBAAkB,SAAS,aAAa,UAAU,EAAE,KAAK;YAC3E;QACF;QACA,oEAAoE;QACpE,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,MAAM,KAAK,WAAW;YACvD,oBAAoB,IAAI,CAAC;QAC3B;IACF;IAEA,OAAO;QAAE;QAAe;QAAc;IAAoB;AAC5D;AASA,SAAS,4BAA4B,EAAE,KAAK,EAAE,UAAU,EAAE,gBAAgB,EAA6B;;IACrG,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAG7E,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iEAAE;YAC/B,OAAO;gBAAC;oBAAE,OAAO;oBAAsB,OAAO;gBAAe;mBAAM,iBAAiB,GAAG;6EAAC,CAAA,IAAK,CAAC;4BAAE,OAAO,EAAE,EAAE;4BAAE,OAAO,EAAE,IAAI;wBAAC,CAAC;;aAAG;QACjI;gEAAG;QAAC;KAAiB;IAErB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mEAAE;YACjC,MAAM,UAAU;gBAAC;oBAAE,OAAO;oBAAyB,OAAO;gBAAgB;aAAE;YAC5E,IAAI,wBAAwB,sBAAsB;gBAC9C,MAAM,YAAY,iBAAiB,IAAI;yFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBACtD,IAAI,WAAW;oBACX,UAAU,aAAa,CAAC,OAAO;mFAAC,CAAA;4BAC5B,QAAQ,IAAI,CAAC;gCAAE,OAAO,GAAG,EAAE;gCAAE,OAAO,GAAG,IAAI;4BAAC;wBAChD;;gBACJ;YACJ;YACA,OAAO;QACT;kEAAG;QAAC;QAAqB;KAAiB;IAE1C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mEAAE;YACjC,OAAO;gBAAC;oBAAE,OAAO;oBAAwB,OAAO;gBAAsB;mBAAM,0HAAA,CAAA,uBAAoB,CAAC,GAAG;+EAAC,CAAA,IAAK,CAAC;4BAAE,OAAO,EAAE,KAAK;4BAAE,OAAO,EAAE,KAAK;wBAAC,CAAC;;aAAG;QAClJ;kEAAG,EAAE;IAEL,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sEAAE;YACpC,OAAO;gBAAC;oBAAE,OAAO;oBAA2B,OAAO;gBAAqB;mBAAM,0HAAA,CAAA,0BAAuB,CAAC,GAAG;kFAAC,CAAA,IAAK,CAAC;4BAAE,OAAO,EAAE,KAAK;4BAAE,OAAO,EAAE,KAAK;wBAAC,CAAC;;aAAG;QACvJ;qEAAG,EAAE;IAEL,MAAM,sBAAgD,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oEAAE;YACzD,OAAO,WAAW,cAAc,CAAC,GAAG;4EAAC,CAAA;oBACjC,MAAM,UAAU,gBAAgB,cAAc,OAAO;oBACrD,IAAI,CAAC,SAAS;wBACZ,OAAO;oBACT;oBACA,OAAO;wBACH,GAAG,aAAa;wBAChB,GAAG,OAAO;wBACV,SAAS,cAAc,OAAO;oBAClC;gBACJ;2EAAG,MAAM,CAAC;QACd;mEAAG;QAAC,WAAW,cAAc;KAAC;IAE9B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+DAAE;YAC3B,OAAO,oBAAoB,MAAM;uEAAC,CAAA;oBAC9B,MAAM,iBAAiB,wBAAwB,wBAAwB,MAAM,WAAW,KAAK;oBAC7F,MAAM,mBAAmB,0BAA0B,2BAA2B,MAAM,aAAa,KAAK;oBACtG,MAAM,mBAAmB,wBAAwB,0BAA0B,MAAM,MAAM,KAAK;oBAE5F,IAAI,sBAAsB,2BAA2B;oBACrD,IAAI,CAAC,qBAAqB;wBACxB,IAAI,2BAA2B,WAAW;4BACxC,sBAAsB,MAAM,cAAc,KAAK,aAAa,MAAM,cAAc,KAAK;wBACvF,OAAO;4BACL,sBAAsB,MAAM,cAAc,KAAK;wBACjD;oBACF;oBAEA,OAAO,kBAAkB,oBAAoB,oBAAoB;gBACrE;;QACJ;8DAAG;QAAC;QAAqB;QAAqB;QAAuB;QAAqB;KAAuB;IAElH,MAAM,+BAAyE,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6EAAE;YACnF,MAAM,UAAoD,CAAC;YAC3D,eAAe,OAAO;qFAAC,CAAA;oBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,aAAa,CAAC,EAAE;wBAC/B,OAAO,CAAC,MAAM,aAAa,CAAC,GAAG,EAAE;oBACrC;oBACA,OAAO,CAAC,MAAM,aAAa,CAAC,CAAC,IAAI,CAAC;gBACtC;;YACA,IAAK,MAAM,WAAW,QAAS;gBAC3B,OAAO,CAAC,QAAQ,CAAC,IAAI;yFAAC,CAAC,GAAG,IAAM,SAAS,EAAE,UAAU,EAAE,MAAM,SAAS,EAAE,UAAU,EAAE;;YACxF;YACA,OAAO;QACX;4EAAG;QAAC;KAAe;IAGnB,MAAM,kBAA2C,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gEAAE;YACvD,OAAO,iBAAiB,GAAG;wEAAC,CAAA;oBAC1B,MAAM,oBAA+C,UAAU,aAAa,CAAC,GAAG;kGAAC,CAAA;4BAC/E,MAAM,uBAAiD,EAAE;4BACzD,WAAW,cAAc,CAAC,OAAO;0GAAC,CAAA;oCAChC,MAAM,UAAU,gBAAgB,cAAc,OAAO;oCACrD,IAAI,WAAW,QAAQ,WAAW,KAAK,UAAU,EAAE,IAAI,QAAQ,aAAa,KAAK,YAAY,EAAE,EAAE;wCAC/F,qBAAqB,IAAI,CAAC;4CACxB,GAAG,aAAa;4CAChB,GAAG,OAAO;4CACV,SAAS,cAAc,OAAO;wCAChC;oCACF;gCACF;;4BACA,qBAAqB,IAAI;0GAAC,CAAC,GAAG,IAAM,SAAS,EAAE,UAAU,EAAE,MAAM,SAAS,EAAE,UAAU,EAAE;;4BACxF,MAAM,WAAW,gCAAgC;4BACjD,OAAO;gCACL,iBAAiB,YAAY,IAAI;gCACjC,aAAa,qBAAqB,MAAM;gCACxC,GAAG,QAAQ;4BACb;wBACF;;oBAEA,OAAO;wBACL,eAAe,UAAU,IAAI;wBAC7B,eAAe,kBAAkB,MAAM;oFAAC,CAAA,KAAM,GAAG,WAAW,GAAG;;oBACjE;gBACF;uEAAG,MAAM;wEAAC,CAAA,MAAO,IAAI,aAAa,CAAC,MAAM,GAAG;;QAC9C;+DAAG;QAAC,WAAW,cAAc;QAAE;KAAiB;IAGhD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,WAAW,CAAC;gBAAE,WAAU;;oBAA2E;oBAC3G,MAAM,IAAI;kCAClC,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;0BAExB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAAkC;oCAAmB,MAAM,IAAI;;;;;;;0CAC7E,6LAAC;gCAAE,WAAU;;oCAAwB;oCAAgB,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;;;;;;;0CACzF,6LAAC,iJAAA,CAAA,UAAU;gCAAC,WAAW,MAAM,SAAS;gCAAE,gBAAgB,WAAW,cAAc;gCAAE,OAAM;gCAAyB,WAAU;;;;;;;;;;;;kCAE9H,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC;kCAClE,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;;8CACd,6LAAC,8MAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAyB,WAAU;kEAAiC;;;;;;kEACnF,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAqB,eAAe;;0EACjD,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,IAAG;0EAAyB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EACpE,6LAAC,qIAAA,CAAA,gBAAa;0EACX,iBAAiB,GAAG,CAAC,CAAA,uBACpB,6LAAC,qIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAAG,OAAO,KAAK;uEAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0DAKrC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAA2B,WAAU;kEAAiC;;;;;;kEACrF,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAuB,eAAe;wDAA0B,UAAU,wBAAwB,wBAAwB,mBAAmB,MAAM,IAAG;;0EACnK,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,IAAG;0EAA2B,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EACtE,6LAAC,qIAAA,CAAA,gBAAa;0EACX,mBAAmB,GAAG,CAAC,CAAA,uBACtB,6LAAC,qIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAAG,OAAO,KAAK;uEAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0DAKrC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAA2B,WAAU;kEAAiC;;;;;;kEACrF,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAqB,eAAe;;0EACjD,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,IAAG;0EAA2B,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EACtE,6LAAC,qIAAA,CAAA,gBAAa;0EACX,mBAAmB,GAAG,CAAC,CAAA,uBACtB,6LAAC,qIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAAG,OAAO,KAAK;uEAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;0DAKrC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAA8B,WAAU;kEAAiC;;;;;;kEACxF,6LAAC,qIAAA,CAAA,SAAM;wDAAC,OAAO;wDAAwB,eAAe;;0EACpD,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,IAAG;0EAA8B,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EACzE,6LAAC,qIAAA,CAAA,gBAAa;0EACX,sBAAsB,GAAG,CAAC,CAAA,uBACzB,6LAAC,qIAAA,CAAA,aAAU;wEAAoB,OAAO,OAAO,KAAK;kFAAG,OAAO,KAAK;uEAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQxC,OAAO,IAAI,CAAC,8BAA8B,MAAM,GAAG,kBAClD,6LAAC,wIAAA,CAAA,YAAS;gCAAC,MAAK;gCAAW,WAAU;gCAAS,cAAc,OAAO,IAAI,CAAC;0CACrE,OAAO,OAAO,CAAC,8BAA8B,GAAG,CAAC,CAAC,CAAC,eAAe,OAAO,iBACxE,6LAAC,wIAAA,CAAA,gBAAa;wCAAC,OAAO;;0DACpB,6LAAC,wIAAA,CAAA,mBAAgB;gDAAC,WAAU;0DAAqC;;;;;;0DACjE,6LAAC,wIAAA,CAAA,mBAAgB;0DACf,cAAA,6LAAC;oDAAI,WAAU;8DACZ,OAAO,GAAG,CAAC,CAAC,qBACX,6LAAC,0JAAA,CAAA,UAAmB;4DAElB,MAAM;4DACN,WAAW,MAAM,IAAI;4DACrB,cAAc,WAAW,EAAE;2DAHtB,KAAK,OAAO;;;;;;;;;;;;;;;;uCANe;;;;;;;;;qDAkB9C,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;0BAK5D,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAEnB,6LAAC,mIAAA,CAAA,cAAW;kCACT,gBAAgB,MAAM,GAAG,kBACxB,6LAAC,wIAAA,CAAA,YAAS;4BAAC,MAAK;4BAAW,WAAU;4BAAS,cAAc,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;sCACjG,gBAAgB,GAAG,CAAC,CAAA,8BACnB,6LAAC,wIAAA,CAAA,gBAAa;oCAAC,OAAO,cAAc,aAAa;;sDAC/C,6LAAC,wIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAAsC,cAAc,aAAa;;;;;;sDAC7F,6LAAC,wIAAA,CAAA,mBAAgB;;gDACd,cAAc,aAAa,CAAC,GAAG,CAAC,CAAA,gCAC/B,6LAAC;wDAA0C,WAAU;;0EACnD,6LAAC;gEAAG,WAAU;0EAAwC,gBAAgB,eAAe;;;;;;0EACrF,6LAAC;;kFAAE,6LAAC;kFAAO;;;;;;oEAAsB;oEAAE,gBAAgB,aAAa,GAAG,GAAG,gBAAgB,aAAa,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB,aAAa,CAAC,QAAQ,EAAE,GAAG;;;;;;;0EAC9J,6LAAC;;kFAAE,6LAAC;kFAAO;;;;;;oEAAqB;oEAAE,gBAAgB,YAAY,GAAG,GAAG,gBAAgB,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,gBAAgB,YAAY,CAAC,QAAQ,EAAE,GAAG;;;;;;;4DACzJ,gBAAgB,mBAAmB,CAAC,MAAM,GAAG,kBAC5C,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAqB;;;;;;kFACnC,6LAAC;wEAAG,WAAU;kFACX,gBAAgB,mBAAmB,CAAC,GAAG,CAAC,CAAA,sBACvC,6LAAC;;oFAAwB,MAAM,UAAU;oFAAC;oFAAG,MAAM,QAAQ;oFAAC;oFAAG,MAAM,QAAQ;oFAAC;;+EAArE,MAAM,OAAO;;;;;;;;;;;;;;;qFAK5B,6LAAC;gEAAE,WAAU;0EAAqC;;;;;;;uDAd5C,gBAAgB,eAAe;;;;;gDAkB1C,cAAc,aAAa,CAAC,MAAM,KAAK,mBAAK,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;mCAtBtC,cAAc,aAAa;;;;;;;;;iDA4BvF,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GA/PS;KAAA;AAkQM,SAAS,mBAAmB,EAAE,MAAM,EAAyD;;IAC1G,MAAM,EAAE,QAAQ,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IACzD,MAAM,EAAE,aAAa,EAAE,SAAS,kBAAkB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,KAAK,sBAAsB;IAE/F,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,6BAA6B;YAC7B,IAAI,mBAAmB,oBAAoB;gBACzC;YACF;YAEA,MAAM,YAAY,SAAS,OAAO,OAAO;YACzC,MAAM,iBAAiB,cAAc,OAAO,YAAY;YAExD,SAAS,aAAa;YACtB,cAAc,kBAAkB;YAChC,WAAW;QACb;uCAAG;QAAC,OAAO,OAAO;QAAE,OAAO,YAAY;QAAE;QAAU;QAAe;QAAiB;KAAmB;IAEtG,IAAI,WAAW,mBAAmB,oBAAoB;QACpD,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,IAAI,CAAC,SAAS,CAAC,YAAY;QACzB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAyB;;;;;;8BACvC,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,CAAC,UAAU,EAAE,OAAO,OAAO,CAAC,WAAW,CAAC;8BAClD,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;kCAAO;;;;;;;;;;;;;;;;;IAI/B;IAEA,qBAAO,6LAAC;QAA4B,OAAO;QAAO,YAAY;QAAY,kBAAkB,0HAAA,CAAA,yBAAsB;;;;;;AACpH;IA/CwB;;QACyB,iIAAA,CAAA,cAAW;QACH,iIAAA,CAAA,iBAAc;;;MAF/C", "debugId": null}}]}