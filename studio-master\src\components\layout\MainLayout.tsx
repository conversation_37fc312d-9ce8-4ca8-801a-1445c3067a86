
"use client";

import React from 'react';
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barInset,
  SidebarHeader,
  SidebarFooter,
} from '@/components/ui/sidebar';
import AppHeader from '@/components/layout/AppHeader';
import AppSidebar from '@/components/layout/AppSidebar';

type MainLayoutProps = {
  children: React.ReactNode;
};

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <>
      <Sidebar>
        <SidebarHeader>
          {/* Placeholder for potential sidebar header content if needed */}
        </SidebarHeader>
        <SidebarContent>
          <AppSidebar />
        </SidebarContent>
        <SidebarFooter>
          {/* Placeholder for potential sidebar footer content if needed */}
        </SidebarFooter>
      </Sidebar>
      <SidebarInset>
        <AppHeader />
        <main className="flex-1 p-4 sm:p-6 lg:p-8 bg-background">
          {children}
        </main>
        <footer className="p-4 text-center text-xs text-muted-foreground border-t">
          تصميم احمد الخمايسه
        </footer>
      </SidebarInset>
    </>
  );
}
