
"use client";

import React, { useState, useEffect, useRef } from 'react'; // Added useRef
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { CalendarCheck2, FileText, Edit, PlusCircle, Users, AlertTriangle, Info, Briefcase, UserCircle2, ClipboardList, FilePenLine, StickyNote, Check, X, Printer, Hash, Upload } from 'lucide-react'; // Added Upload
import type { Child, Assessment, CalculatedAge, SessionNote, CaseStudyData, CaseStudyBasicInfo, CaseStudyPregnancyAndBirthInfo, CaseStudyReinforcerResponseInfo } from '@/lib/types';
import AgeDisplay from '@/components/assessment/AgeDisplay';
import { calculateAge, formatDate, generateUniqueId } from '@/lib/utils';
import { useChildren, useAssessments } from '@/hooks/use-storage';
import Image from 'next/image';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { defaultCaseStudyData } from '@/lib/constants';
import { Alert, AlertTitle, AlertDescription as ShadCnAlertDescription } from "@/components/ui/alert";
import { addMonths, isPast, parseISO, differenceInDays, isValid, format } from 'date-fns';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { DatePicker } from '@/components/ui/date-picker';
import { ScrollArea } from '@/components/ui/scroll-area';

interface ChildProfileClientProps {
  childId: string;
}

export default function ChildProfileClient({ childId }: ChildProfileClientProps) {
  const { getChild, updateChild, loading: childrenLoading } = useChildren();
  const { assessments, loading: assessmentsLoading } = useAssessments(childId);

  const [childData, setChildData] = useState<Child | null>(null);
  const [loading, setLoading] = useState(true);
  const [latestAssessment, setLatestAssessment] = useState<Assessment | undefined>(undefined);
  const [isProfileFormDialogOpen, setIsProfileFormDialogOpen] = useState(false);
  const [profileFormData, setProfileFormData] = useState<Child | null>(null);
  const [profileAvatarPreview, setProfileAvatarPreview] = useState<string | null>(null);

  const [isSessionNoteDialogOpen, setIsSessionNoteDialogOpen] = useState(false);
  const [currentSessionNote, setCurrentSessionNote] = useState<Partial<SessionNote>>({});
  const [sessionNoteDate, setSessionNoteDate] = useState<Date | undefined>(new Date());

  const [isCaseStudyDialogOpen, setIsCaseStudyDialogOpen] = useState(false);
  const [currentCaseStudyData, setCurrentCaseStudyData] = useState<CaseStudyData>(defaultCaseStudyData);
  const caseStudyPrintRef = useRef<HTMLDivElement>(null); // Ref for printing case study


  const { toast } = useToast();
  const router = useRouter();

  const [alerts, setAlerts] = useState<{
    needsReassessment: boolean;
    reassessmentSoon: boolean;
    noAssessment: boolean;
    serviceCompleted: boolean;
    reassessmentDueDateString: string | null;
  }>({
    needsReassessment: false,
    reassessmentSoon: false,
    noAssessment: false,
    serviceCompleted: false,
    reassessmentDueDateString: null,
  });

  // Initialize child data from storage
  useEffect(() => {
    // Wait for children hook to be ready
    if (childrenLoading) {
      return;
    }

    const child = getChild(childId);

    if (child) {
      setChildData(child);
      setProfileFormData(child);
      setProfileAvatarPreview(child.avatarUrl || null);
      setCurrentCaseStudyData(child.caseStudy ? JSON.parse(JSON.stringify(child.caseStudy)) : JSON.parse(JSON.stringify(defaultCaseStudyData)));
    } else {
      setChildData(null);
    }
    setLoading(false);
  }, [childId, getChild, childrenLoading]);

  // Update latest assessment when assessments change
  useEffect(() => {
    if (assessments.length > 0) {
      const latest = assessments.sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];
      setLatestAssessment(latest);
    } else {
      setLatestAssessment(undefined);
    }
  }, [assessments]);

  useEffect(() => {
    if (!childData) return;

    const today = new Date();
    let needsReassessmentFlag = false;
    let reassessmentSoonFlag = false;
    let noAssessmentFlag = false;
    let serviceCompletedFlag = false;
    let dueDateString: string | null = null;

    const ageNow = calculateAge(childData.birthDate, today.toISOString());
    if (ageNow.years >= 6) {
      serviceCompletedFlag = true;
    }

    if (latestAssessment) {
      try {
        const lastAssessmentDate = parseISO(latestAssessment.assessmentDate);
        if (isValid(lastAssessmentDate)) {
          const reassessmentDueDate = addMonths(lastAssessmentDate, 4);
          dueDateString = formatDate(reassessmentDueDate.toISOString());

          if (isPast(reassessmentDueDate)) {
            needsReassessmentFlag = true;
          } else {
            const daysUntilReassessment = differenceInDays(reassessmentDueDate, today);
            if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {
              reassessmentSoonFlag = true;
            }
          }
        } else {
          noAssessmentFlag = true;
        }
      } catch (error) {
        noAssessmentFlag = true;
      }
    } else {
      noAssessmentFlag = true;
    }

    setAlerts({
      needsReassessment: needsReassessmentFlag && !serviceCompletedFlag,
      reassessmentSoon: reassessmentSoonFlag && !serviceCompletedFlag,
      noAssessment: noAssessmentFlag && !serviceCompletedFlag,
      serviceCompleted: serviceCompletedFlag,
      reassessmentDueDateString: dueDateString,
    });

  }, [childData, latestAssessment]);


  const handleOpenProfileFormDialog = () => {
    if (childData) {
      setProfileFormData(childData);
      setProfileAvatarPreview(childData.avatarUrl || null);
      setIsProfileFormDialogOpen(true);
    }
  };

  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileFormData(prev => ({ ...prev, [name]: value } as Child));
  };

  const handleProfileGenderChange = (value: Child['gender']) => {
    setProfileFormData(prev => ({ ...prev, gender: value }));
  };

  const handleProfileAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        setProfileFormData(prev => ({ ...prev, avatarUrl: reader.result as string } as Child));
        setProfileAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveProfileAvatar = () => {
    setProfileFormData(prev => ({ ...prev, avatarUrl: undefined } as Child));
    setProfileAvatarPreview(null);
    const fileInput = document.getElementById('avatarUrl-edit-profile') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const handleProfileFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!profileFormData || !profileFormData.name || !profileFormData.birthDate || !profileFormData.enrollmentDate || !profileFormData.specialistName) {
      toast({ title: "خطأ", description: "يرجى ملء جميع الحقول الإلزامية.", variant: "destructive" });
      return;
    }

    let finalAvatarUrl = profileFormData.avatarUrl;
    if (!finalAvatarUrl && !profileAvatarPreview) {
      const initials = profileFormData.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase() || '؟؟';
      finalAvatarUrl = `https://placehold.co/100x100.png?text=${encodeURIComponent(initials)}`;
    } else if (!finalAvatarUrl && profileAvatarPreview) {
      finalAvatarUrl = profileAvatarPreview;
    }

    const updatedChildDetails: Child = {
      ...childData!,
      name: profileFormData.name,
      birthDate: profileFormData.birthDate,
      enrollmentDate: profileFormData.enrollmentDate,
      specialistName: profileFormData.specialistName,
      avatarUrl: finalAvatarUrl,
      gender: profileFormData.gender,
    };

    const success = updateChild(updatedChildDetails);
    if (success) {
      setChildData(updatedChildDetails);
      toast({ title: "نجاح", description: `تم تحديث بيانات ${updatedChildDetails.name} بنجاح.` });
      setIsProfileFormDialogOpen(false);
    } else {
      toast({ title: "خطأ", description: "فشل في تحديث بيانات الطفل.", variant: "destructive" });
    }
  };

  // Loading state
  if (loading || childrenLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات الطفل...</p>
          </div>
        </div>
      </div>
    );
  }

  // Child not found
  if (!childData) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  const fallbackName = childData.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'C';

  function isValidISO(dateString: string | undefined | null): boolean {
    if (!dateString) return false;
    try {
      return isValid(parseISO(dateString));
    } catch {
      return false;
    }
  }

  const handleOpenSessionNoteDialog = () => {
    setCurrentSessionNote({});
    setSessionNoteDate(new Date());
    setIsSessionNoteDialogOpen(true);
  };

  const handleSessionNoteInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCurrentSessionNote(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveSessionNote = () => {
    if (!sessionNoteDate || !currentSessionNote.goalDiscussed || !currentSessionNote.attendees || !currentSessionNote.notes) {
      toast({ title: "خطأ", description: "يرجى ملء جميع الحقول المطلوبة للمحضر.", variant: "destructive" });
      return;
    }
    const newNote: SessionNote = {
      id: generateUniqueId('note'),
      date: format(sessionNoteDate, "yyyy-MM-dd"),
      goalDiscussed: currentSessionNote.goalDiscussed || "",
      attendees: currentSessionNote.attendees || "",
      notes: currentSessionNote.notes || "",
      nextSteps: currentSessionNote.nextSteps,
    };

    const updatedChild = {
      ...childData!,
      sessionNotes: [...(childData!.sessionNotes || []), newNote]
    };

    const success = updateChild(updatedChild);
    if (success) {
      setChildData(updatedChild);
      toast({ title: "نجاح", description: "تم حفظ محضر الجلسة بنجاح." });
      setIsSessionNoteDialogOpen(false);
    } else {
      toast({ title: "خطأ", description: "فشل في حفظ محضر الجلسة.", variant: "destructive" });
    }
  };

  const handleOpenCaseStudyDialog = () => {
    if (!childData) return;

    const ageNow = calculateAge(childData.birthDate);
    const currentAgeString = `${ageNow.years} سنوات و ${ageNow.months} أشهر`;

    let newCaseStudyDataState: CaseStudyData = JSON.parse(JSON.stringify(defaultCaseStudyData));

    if (childData.caseStudy) {
      newCaseStudyDataState = {
        basicInfo: {
          ...newCaseStudyDataState.basicInfo,
          ...(childData.caseStudy.basicInfo || {}),
        },
        pregnancyAndBirthInfo: {
          ...newCaseStudyDataState.pregnancyAndBirthInfo,
          ...(childData.caseStudy.pregnancyAndBirthInfo || {}),
        },
        reinforcerResponseInfo: {
          ...newCaseStudyDataState.reinforcerResponseInfo,
          ...(childData.caseStudy.reinforcerResponseInfo || {}),
        },
      };
    }

    newCaseStudyDataState.basicInfo.childName = childData.name || newCaseStudyDataState.basicInfo.childName || "";
    newCaseStudyDataState.basicInfo.birthDate = childData.birthDate || newCaseStudyDataState.basicInfo.birthDate || "";
    newCaseStudyDataState.basicInfo.currentAge = currentAgeString;
    newCaseStudyDataState.basicInfo.gender = childData.gender || newCaseStudyDataState.basicInfo.gender || "unknown";

    setCurrentCaseStudyData(newCaseStudyDataState);
    setIsCaseStudyDialogOpen(true);
  };


  const handleCaseStudyInputChange = (section: keyof CaseStudyData, field: string, value: string | Date | undefined) => {
    setCurrentCaseStudyData(prev => {
        const newState = JSON.parse(JSON.stringify(prev));
        if (!newState[section]) {
            newState[section] = {} as any;
        }
        (newState[section] as any)[field] = value instanceof Date ? format(value, "yyyy-MM-dd") : value;
        return newState;
    });
  };

  const handleSaveCaseStudy = () => {
    const updatedChild = {
      ...childData!,
      caseStudy: currentCaseStudyData
    };

    const success = updateChild(updatedChild);
    if (success) {
      setChildData(updatedChild);
      toast({ title: "نجاح", description: "تم حفظ دراسة الحالة بنجاح." });
      setIsCaseStudyDialogOpen(false);
    } else {
      toast({ title: "خطأ", description: "فشل في حفظ دراسة الحالة.", variant: "destructive" });
    }
  };

  const renderCaseStudySection = (title: string, data?: Record<string, string | undefined>) => {
    if (!data || Object.values(data).every(val => !val)) {
      return (
        <Card>
          <CardHeader><CardTitle>{title}</CardTitle></CardHeader>
          <CardContent><p className="text-muted-foreground">لا توجد بيانات متاحة لهذا القسم.</p></CardContent>
        </Card>
      );
    }

    const keyMap: Record<string, string> = {
        childName: "اسم الطفل:",
        birthDate: "تاريخ الميلاد:",
        currentAge: "العمر الحالي (بالسنوات والأشهر):",
        gender: "الجنس:",
        guardianName: "اسم ولي الأمر (الأم/الأب/الوصي):",
        guardianPhoneNumber: "رقم هاتف ولي الأمر:",
        homeAddress: "عنوان المنزل:",
        guardianRelationship: "علاقة ولي الأمر بالطفل:",
        hasSiblings: "هل هناك إخوة أو أخوات للطفل؟",
        siblingsInfo: "إذا كان نعم، اذكر أسماءهم وأعمارهم:",
        motherAgeAtPregnancy: "كم كان عمر الأم عندما حملت بهذا الطفل؟",
        fullTermPregnancy: "هل كان الحمل كاملاً (9 أشهر) أم حدثت ولادة مبكرة؟",
        prematureBirthMonth: "إذا كانت ولادة مبكرة، في أي شهر؟",
        motherHealthIssuesDuringPregnancy: "هل واجهت الأم أي مشاكل صحية خلال فترة الحمل؟ (مثل: سكري الحمل، ارتفاع ضغط الدم، أخذ أدوية معينة، إلخ.)",
        motherHealthIssuesDetails: "إذا كان نعم، يرجى التوضيح (مشاكل صحية للأم):",
        deliveryType: "هل حدثت الولادة بشكل طبيعي أم قيصري؟",
        childHealthIssuesAtBirth: "هل واجه الطفل أي مشاكل صحية عند الولادة أو بعدها مباشرة؟ (مثل: نقص الأكسجين، مشاكل في التنفس، الحاجة للبقاء في الحضانة، إلخ.)",
        childHealthIssuesDetails: "إذا كان نعم، يرجى التوضيح (مشاكل صحية للطفل):",
        favoriteToys: "ما هي الأشياء التي يحب الطفل اللعب بها أو يحبها كثيراً؟ (مثل: ألعاب معينة، كتب، فقاعات، كرات، سيارات، حيوانات محشوة، إلخ.)",
        enjoyableActivities: "ما هي الأنشطة التي يستمتع بها الطفل؟ (مثل: الاستماع للموسيقى، الرقص، المشي في الخارج، مشاهدة التلفزيون، الرسم، اللعب بالماء، إلخ.)",
        favoriteFoods: "هل هناك أطعمة معينة يحبها الطفل جداً؟ (مثل: بسكويت، فواكه، حلويات، عصير، إلخ.)",
        happinessExpression: "ماذا يفعل الطفل عندما يكون سعيداً أو متحفزاً؟ (مثل: يبتسم، يضحك، يصفق، يقفز، يصدر أصواتاً، يحاول الاقتراب من الشيء الذي يحبه، إلخ.)",
        motivationMethods: "عندما تريد من الطفل أن يفعل شيئاً ما (مثل: يجلس، ينظر إليك)، ما الذي تستخدمه لتحفيزه أو مكافأته عادةً؟ (مثال: إعطاء لعبة يحبها، التصفير، الحضن، قول \"أحسنت\"، تشغيل أغنية، إلخ.)",
        smilesAtGuardian: "هل يبتسم الطفل عندما يراك أو يسمع صوتك؟",
        communicatesNeeds: "هل يحاول الطفل التواصل معك عندما يحتاج شيئًا؟ (مثل: الإشارة، النظر إليك، إصدار أصوات، البكاء بطريقة معينة، إلخ.)",
    };


    return (
      <Card>
        <CardHeader><CardTitle>{title}</CardTitle></CardHeader>
        <CardContent className="space-y-2 text-sm">
          {Object.entries(data).map(([key, value]) => {
            const displayValue = value || "غير محدد";
            const label = keyMap[key] || key;
            if (key === 'gender') {
                const genderLabel = displayValue === 'male' ? 'ذكر' : displayValue === 'female' ? 'أنثى' : displayValue === 'other' ? 'آخر' : 'غير معروف';
                return <p key={key}><strong>{label}</strong> {genderLabel}</p>;
            }
             if (key === 'hasSiblings' || key === 'fullTermPregnancy' || key === 'motherHealthIssuesDuringPregnancy' || key === 'childHealthIssuesAtBirth' || key === 'smilesAtGuardian' || key === 'communicatesNeeds') {
                const boolLabel = displayValue === 'yes' ? 'نعم' : displayValue === 'no' ? 'لا' : displayValue === 'sometimes' ? 'أحياناً' : 'غير محدد';
                return <p key={key}><strong>{label}</strong> {boolLabel}</p>;
            }
            if (key === 'deliveryType') {
                const deliveryLabel = displayValue === 'natural' ? 'طبيعي' : displayValue === 'cesarean' ? 'قيصري' : 'غير محدد';
                return <p key={key}><strong>{label}</strong> {deliveryLabel}</p>;
            }
            if (value && (key.toLowerCase().includes('date') || key === 'birthDate')) {
                return <p key={key}><strong>{label}</strong> {formatDate(value)}</p>;
            }
            if (key.toLowerCase().includes('details') || key === 'siblingsInfo' || key === 'favoriteToys' || key === 'enjoyableActivities' || key === 'favoriteFoods' || key === 'happinessExpression' || key === 'motivationMethods') {
              return (
                <div key={key} className="space-y-0.5">
                  <strong>{label}</strong>
                  <pre className="whitespace-pre-wrap font-sans text-sm p-2 bg-muted/30 rounded-md border">{displayValue}</pre>
                </div>
              );
            }
            return <p key={key}><strong>{label}</strong> {displayValue}</p>;
          })}
        </CardContent>
      </Card>
    );
  };

  const handlePrintCaseStudy = () => {
    if (!caseStudyPrintRef.current) {
        toast({ title: "خطأ في الطباعة", description: "لم يتم العثور على محتوى دراسة الحالة للطباعة.", variant: "destructive" });
        return;
    }

    const printWindow = window.open('', '_blank');
    if (printWindow) {
        printWindow.document.write('<html><head><title>' + `دراسة حالة لـ ${childData.name}` + '</title>');
        printWindow.document.write(`
            <style>
              body { font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif; direction: rtl; padding: 20px; margin: 0; }
              h1, h2, h3 { color: #333; margin-bottom: 0.8em; margin-top: 1.2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }
              h1 { font-size: 1.8em; }
              h2 { font-size: 1.5em; }
              h3 { font-size: 1.2em; color: hsl(var(--primary)); }
              p { margin-bottom: 0.8em; line-height: 1.6; }
              pre { white-space: pre-wrap; font-family: inherit; background-color: #f9f9f9; border: 1px solid #eee; padding: 10px; border-radius: 4px; margin-top: 0.3em; }
              strong { font-weight: bold; }
              .print-section { margin-bottom: 20px; page-break-inside: avoid; }
              .print-section-title { font-size: 1.3em; font-weight: bold; color: hsl(var(--accent)); margin-bottom: 10px; }
              .print-item { margin-bottom: 5px; }
              .print-item strong { display: block; margin-bottom: 2px; color: #555; }
              @media print {
                body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                @page { margin: 20mm; }
                button, .no-print { display: none !important; }
              }
            </style>
        `);
        printWindow.document.write('</head><body dir="rtl">');
        printWindow.document.write('<h1>' + `دراسة حالة لـ ${childData.name}` + '</h1>');

        const sections = caseStudyPrintRef.current.querySelectorAll('div.print-section-card > div > h3, div.print-section-card > div > div > p, div.print-section-card > div > div > div');

        let currentSectionHtml = "";
        let currentSectionTitle = "";

        sections.forEach(el => {
            if (el.tagName.toLowerCase() === 'h3') { // This is CardTitle inside CardHeader
                if (currentSectionHtml) { // Print previous section
                    printWindow.document.write('<h2>' + currentSectionTitle + '</h2>');
                    printWindow.document.write('<div class="print-section">' + currentSectionHtml + '</div>');
                }
                currentSectionTitle = el.textContent || "قسم";
                currentSectionHtml = ""; // Reset for new section
            } else {
                 // For p and div elements containing label and value
                 const labelElement = el.querySelector('strong');
                 const valueElement = labelElement ? labelElement.nextSibling : el.querySelector('pre') || el; // Get text node or pre

                 if (labelElement && valueElement) {
                     currentSectionHtml += '<div class="print-item"><strong>' + labelElement.textContent + '</strong>';
                     if(valueElement.nodeName === "PRE") {
                         currentSectionHtml += '<pre>' + valueElement.textContent + '</pre></div>';
                     } else {
                          currentSectionHtml += (valueElement.textContent || "") + '</div>';
                     }
                 } else if (el.textContent?.trim()) { // Fallback for simple p tags
                     currentSectionHtml += `<p>${el.textContent}</p>`;
                 }
            }
        });

        if (currentSectionHtml) { // Print the last section
            printWindow.document.write('<h2>' + currentSectionTitle + '</h2>');
            printWindow.document.write('<div class="print-section">' + currentSectionHtml + '</div>');
        }

        printWindow.document.write('</body></html>');
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
    } else {
        toast({ title: "خطأ", description: "يرجى السماح بالنوافذ المنبثقة لطباعة هذا المحتوى.", variant: "destructive"});
    }
  };


  return (
    <>
      <div className="container mx-auto py-8 space-y-8">
        <Card className="overflow-hidden shadow-lg">
          <CardHeader className="bg-muted/30 p-6 flex flex-col sm:flex-row items-start sm:items-center gap-6">
            <Avatar className="h-28 w-28 border-4 border-primary shadow-lg">
              <AvatarImage src={childData.avatarUrl} alt={childData.name} data-ai-hint="child portrait" className="object-cover" />
              <AvatarFallback className="text-3xl font-semibold bg-gradient-to-br from-primary/20 to-primary/10">{fallbackName}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <Badge variant="outline" className="text-sm font-mono">
                  <Hash className="h-4 w-4 mr-1" />
                  {childData.childIdNumber}
                </Badge>
              </div>
              <CardTitle className="text-3xl font-bold text-primary">{childData.name}</CardTitle>
              <div className="text-muted-foreground mt-1">
                <p className="text-sm flex items-center gap-1"><UserCircle2 className="h-4 w-4" />الجنس: {childData.gender === 'male' ? 'ذكر' : childData.gender === 'female' ? 'أنثى' : childData.gender === 'other' ? 'آخر' : 'غير معروف'}</p>
                <AgeDisplay birthDate={childData.birthDate} label="العمر الحالي:" className="text-lg" />
                <p className="text-sm">تاريخ الميلاد: {formatDate(childData.birthDate)}</p>
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={handleOpenProfileFormDialog}>
              <Edit className="ml-2 h-4 w-4" /> تعديل الملف الشخصي
            </Button>
          </CardHeader>
          <CardContent className="p-6 grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-2 text-foreground/90">تفاصيل التسجيل</h3>
              <div className="space-y-1 text-sm">
                <p className="flex items-center gap-1"><Briefcase className="h-4 w-4 text-accent" /> <strong className="text-muted-foreground">تاريخ الالتحاق:</strong> {isValidISO(childData.enrollmentDate) ? formatDate(childData.enrollmentDate) : 'غير محدد'}</p>
                <p className="flex items-center gap-1"><Users className="h-4 w-4 text-accent" /> <strong className="text-muted-foreground">الأخصائي:</strong> {childData.specialistName}</p>
              </div>
            </div>
            {latestAssessment && isValidISO(latestAssessment.assessmentDate) ? (
              <div>
                <h3 className="text-lg font-semibold mb-2 text-foreground/90">آخر تقييم</h3>
                <p className="text-sm"><strong className="text-muted-foreground">التاريخ:</strong> {formatDate(latestAssessment.assessmentDate)}</p>
                <AgeDisplay birthDate={childData.birthDate} assessmentDate={latestAssessment.assessmentDate} label="العمر عند التقييم:" className="text-sm" />
              </div>
            ) : (
              <div>
                <h3 className="text-lg font-semibold mb-2 text-foreground/90">آخر تقييم</h3>
                <p className="text-sm text-muted-foreground">لا يوجد تقييم سابق لهذا الطفل أو تاريخ التقييم غير صالح.</p>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="space-y-4">
          {alerts.serviceCompleted && (
            <Alert variant="default" className="bg-yellow-100 border-yellow-500 text-yellow-700 dark:bg-yellow-900/30 dark:border-yellow-700 dark:text-yellow-300">
              <Info className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              <AlertTitle className="font-semibold">اكتمال الخدمة (محتمل)</AlertTitle>
              <ShadCnAlertDescription>
                بلغ الطفل {childData.name} عمر 6 سنوات أو أكثر. يرجى مراجعة استمرارية الخدمة المقدمة له.
              </ShadCnAlertDescription>
            </Alert>
          )}

          {!alerts.serviceCompleted && alerts.noAssessment && (
            <Alert variant="default" className="bg-blue-100 border-blue-500 text-blue-700 dark:bg-blue-900/30 dark:border-blue-700 dark:text-blue-300">
              <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              <AlertTitle className="font-semibold">تقييم أولي مطلوب</AlertTitle>
              <ShadCnAlertDescription>
                لا يوجد تقييم حالي للطفل {childData.name}. يرجى إجراء تقييم أولي لتتبع تطوره.
              </ShadCnAlertDescription>
            </Alert>
          )}

          {!alerts.serviceCompleted && alerts.needsReassessment && (
            <Alert variant="destructive">
              <AlertTriangle className="h-5 w-5" />
              <AlertTitle className="font-semibold">إعادة التقييم مطلوبة</AlertTitle>
              <ShadCnAlertDescription>
                لقد حان موعد إعادة تقييم الطفل {childData.name}. مر على آخر تقييم أكثر من 4 أشهر (آخر تقييم في: {latestAssessment ? formatDate(latestAssessment.assessmentDate) : 'غير معروف'}).
              </ShadCnAlertDescription>
            </Alert>
          )}

          {!alerts.serviceCompleted && alerts.reassessmentSoon && !alerts.needsReassessment && (
            <Alert variant="default" className="bg-orange-100 border-orange-500 text-orange-700 dark:bg-orange-900/30 dark:border-orange-700 dark:text-orange-300">
              <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              <AlertTitle className="font-semibold">تذكير بإعادة التقييم</AlertTitle>
              <ShadCnAlertDescription>
                يقترب موعد إعادة تقييم الطفل {childData.name}. الموعد المتوقع: {alerts.reassessmentDueDateString}.
              </ShadCnAlertDescription>
            </Alert>
          )}
        </div>

        <Separator />

        <Card className="shadow-lg">
          <CardHeader className="flex flex-row justify-between items-center">
            <div className="flex items-center gap-2">
              <ClipboardList className="h-6 w-6 text-accent" />
              <CardTitle>توثيق الجلسات / محاضر الاجتماعات</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={handleOpenSessionNoteDialog}>
              <PlusCircle className="ml-2 h-4 w-4" /> إضافة محضر جديد
            </Button>
          </CardHeader>
          <CardContent>
            {childData.sessionNotes && childData.sessionNotes.length > 0 ? (
              <ScrollArea className="h-[300px] pr-3">
                <div className="space-y-4">
                  {childData.sessionNotes.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).map(note => (
                    <Card key={note.id} className="bg-muted/50">
                      <CardHeader className="pb-2 pt-3 px-4">
                        <CardTitle className="text-md">
                          تاريخ الجلسة: {formatDate(note.date)}
                        </CardTitle>
                        <CardDescription className="text-xs">الحضور: {note.attendees}</CardDescription>
                      </CardHeader>
                      <CardContent className="px-4 pb-3 space-y-1 text-sm">
                        <div><strong>الهدف المناقش:</strong> {note.goalDiscussed}</div>
                        <div><strong>الملاحظات:</strong> <pre className="whitespace-pre-wrap font-sans text-sm p-2 bg-background border rounded-md">{note.notes}</pre></div>
                        {note.nextSteps && <div><strong>الخطوات التالية:</strong> <pre className="whitespace-pre-wrap font-sans text-sm p-2 bg-background border rounded-md">{note.nextSteps}</pre></div>}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <p className="text-muted-foreground text-center py-4">لا توجد محاضر جلسات مسجلة لهذا الطفل.</p>
            )}
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader className="flex flex-row justify-between items-center">
            <div className="flex items-center gap-2">
              <FilePenLine className="h-6 w-6 text-accent" />
              <CardTitle>دراسة حالة</CardTitle>
            </div>
            <Button variant="outline" size="sm" onClick={handleOpenCaseStudyDialog}>
              {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? <Edit className="ml-2 h-4 w-4" /> : <PlusCircle className="ml-2 h-4 w-4" />}
              {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? "تعديل دراسة الحالة" : "إضافة دراسة حالة"}
            </Button>
          </CardHeader>
          <CardContent className="space-y-4" ref={caseStudyPrintRef}> {/* Added ref here */}
            {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? (
              <>
                <div className="print-section-card">{renderCaseStudySection("معلومات أساسية عن الطفل والأسرة", childData.caseStudy.basicInfo)}</div>
                <div className="print-section-card">{renderCaseStudySection("معلومات عن فترة الحمل والولادة", childData.caseStudy.pregnancyAndBirthInfo)}</div>
                <div className="print-section-card">{renderCaseStudySection("استجابة الطفل للمعززات والمكافآت", childData.caseStudy.reinforcerResponseInfo)}</div>
              </>
            ) : (
              <p className="text-muted-foreground text-center py-4">لم يتم إدخال بيانات دراسة الحالة لهذا الطفل بعد.</p>
            )}
          </CardContent>
        </Card>

        <Separator />

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
          <Card className="shadow-md hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarCheck2 className="h-6 w-6 text-accent" />
                التقييمات
              </CardTitle>
              <CardDescription>إدارة ومراجعة تقييمات بورتيج لـ {childData.name}.</CardDescription>
            </CardHeader>
            <CardContent>
              <Image src="https://placehold.co/600x300.png" alt="توضيح التقييم" width={600} height={300} className="rounded-md mb-4" data-ai-hint="checklist form" />
              <p className="text-sm text-muted-foreground mb-4">
                إجمالي التقييمات: {assessments.length}
              </p>
              <div className="flex flex-col sm:flex-row gap-2">
                <Link href={`/children/${childData.id}/assessment/new`} passHref>
                  <Button className="w-full sm:w-auto">
                    <PlusCircle className="ml-2 h-4 w-4" /> بدء تقييم جديد
                  </Button>
                </Link>
                <Link href={`/children/${childData.id}/assessment`} passHref>
                  <Button variant="outline" className="w-full sm:w-auto">عرض سجل التقييمات</Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-md hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-6 w-6 text-accent" />
                التقرير الشامل
              </CardTitle>
              <CardDescription>إنشاء وعرض تقارير شاملة مع تحليل للبيانات.</CardDescription>
            </CardHeader>
            <CardContent>
              <Image src="https://placehold.co/600x300.png" alt="توضيح التقرير الشامل" width={600} height={300} className="rounded-md mb-4" data-ai-hint="report document" />
              <p className="text-sm text-muted-foreground mb-4">
                استخدم الذكاء الاصطناعي لإنشاء تقارير مفصلة وتحليل لنتائج التقييم.
              </p>
              <Link href={`/children/${childData.id}/plan`} passHref>
                <Button className="w-full">
                  الانتقال إلى التقارير
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Profile Edit Dialog */}
      <Dialog open={isProfileFormDialogOpen} onOpenChange={setIsProfileFormDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>تعديل بيانات الطفل</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleProfileFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="childIdNumber-edit-profile" className="text-right">رقم الطفل</Label>
                <Input
                  id="childIdNumber-edit-profile"
                  name="childIdNumber"
                  value={profileFormData.childIdNumber || ''}
                  onChange={handleProfileInputChange}
                  className="col-span-3 font-mono"
                  placeholder="CH-2024-001"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name-edit-profile" className="text-right">الاسم</Label>
                <Input id="name-edit-profile" name="name" value={profileFormData.name || ''} onChange={handleProfileInputChange} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="birthDate-edit-profile" className="text-right">تاريخ الميلاد</Label>
                <Input id="birthDate-edit-profile" name="birthDate" type="date" value={profileFormData.birthDate || ''} onChange={handleProfileInputChange} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="enrollmentDate-edit-profile" className="text-right">تاريخ الالتحاق</Label>
                <Input id="enrollmentDate-edit-profile" name="enrollmentDate" type="date" value={profileFormData.enrollmentDate || ''} onChange={handleProfileInputChange} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="specialistName-edit-profile" className="text-right">الأخصائي</Label>
                <Input id="specialistName-edit-profile" name="specialistName" value={profileFormData.specialistName || ''} onChange={handleProfileInputChange} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="gender-edit-profile" className="text-right">الجنس</Label>
                <Select value={profileFormData.gender} onValueChange={(value) => handleProfileGenderChange(value as Child['gender'])}>
                  <SelectTrigger id="gender-edit-profile" className="col-span-3">
                    <SelectValue placeholder="اختر الجنس" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">ذكر</SelectItem>
                    <SelectItem value="female">أنثى</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="avatarUrl-edit-profile" className="text-right pt-2">صورة شخصية</Label>
                <div className="col-span-3 space-y-3">
                  <div className="flex items-center gap-3">
                    {profileAvatarPreview ? (
                      <div className="relative h-16 w-16 rounded-full overflow-hidden border-2 border-primary shadow-md">
                        <Image src={profileAvatarPreview} alt="معاينة الصورة" fill className="object-cover" />
                      </div>
                    ) : (
                      <div className="h-16 w-16 rounded-full bg-muted border-2 border-dashed border-muted-foreground/30 flex items-center justify-center">
                        <Upload className="h-6 w-6 text-muted-foreground" />
                      </div>
                    )}
                    <div className="flex-1">
                      <Input
                        id="avatarUrl-edit-profile"
                        name="avatarUrl"
                        type="file"
                        accept="image/*"
                        onChange={handleProfileAvatarChange}
                        className="text-sm"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        اختر صورة بصيغة JPG أو PNG
                      </p>
                    </div>
                  </div>
                  {profileAvatarPreview && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleRemoveProfileAvatar}
                      className="text-destructive hover:text-destructive"
                    >
                      <X className="h-4 w-4 mr-1" />
                      إزالة الصورة
                    </Button>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">إلغاء</Button>
              </DialogClose>
              <Button type="submit">حفظ التعديلات</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Session Note Dialog */}
      <Dialog open={isSessionNoteDialogOpen} onOpenChange={setIsSessionNoteDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>إضافة محضر جلسة جديد</DialogTitle>
            <DialogDescription>
              قم بتوثيق تفاصيل الجلسة مع الأسرة أو الفريق.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="session-date" className="text-right">
                التاريخ
              </Label>
              <DatePicker
                date={sessionNoteDate}
                setDate={setSessionNoteDate}
                buttonClassName="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="session-goal" className="text-right">
                الهدف المناقش
              </Label>
              <Textarea
                id="session-goal"
                name="goalDiscussed"
                value={currentSessionNote.goalDiscussed || ""}
                onChange={handleSessionNoteInputChange}
                className="col-span-3 min-h-[80px]"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="session-attendees" className="text-right">
                الحضور
              </Label>
              <Input
                id="session-attendees"
                name="attendees"
                value={currentSessionNote.attendees || ""}
                onChange={handleSessionNoteInputChange}
                className="col-span-3"
                placeholder="مثال: الأم, الأب, الأخصائي"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="session-notes" className="text-right">
                الملاحظات
              </Label>
              <Textarea
                id="session-notes"
                name="notes"
                value={currentSessionNote.notes || ""}
                onChange={handleSessionNoteInputChange}
                className="col-span-3 min-h-[100px]"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="session-nextSteps" className="text-right">
                الخطوات التالية (اختياري)
              </Label>
              <Textarea
                id="session-nextSteps"
                name="nextSteps"
                value={currentSessionNote.nextSteps || ""}
                onChange={handleSessionNoteInputChange}
                className="col-span-3 min-h-[80px]"
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">إلغاء</Button>
            </DialogClose>
            <Button type="button" onClick={handleSaveSessionNote}>حفظ المحضر</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Case Study Dialog */}
      <Dialog open={isCaseStudyDialogOpen} onOpenChange={setIsCaseStudyDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>
              {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? "تعديل دراسة الحالة" : "إضافة دراسة حالة"} لـ {childData.name}
            </DialogTitle>
            <DialogDescription>
              املأ المعلومات التالية لتوثيق دراسة حالة الطفل.
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="h-[70vh] p-1">
            <div className="space-y-6 py-4 pr-3"> {/* Removed ref from here */}
              {/* Basic Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات أساسية عن الطفل والأسرة</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="cs-childName">اسم الطفل</Label>
                    <Input id="cs-childName" value={currentCaseStudyData.basicInfo?.childName || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "childName", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-birthDate">تاريخ الميلاد</Label>
                    <DatePicker date={currentCaseStudyData.basicInfo?.birthDate ? parseISO(currentCaseStudyData.basicInfo.birthDate) : undefined} setDate={(date) => handleCaseStudyInputChange("basicInfo", "birthDate", date)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-currentAge">العمر الحالي (بالسنوات والأشهر)</Label>
                    <Input id="cs-currentAge" value={currentCaseStudyData.basicInfo?.currentAge || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "currentAge", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-gender">الجنس</Label>
                    <Select value={currentCaseStudyData.basicInfo?.gender || "unknown"} onValueChange={(value) => handleCaseStudyInputChange("basicInfo", "gender", value as Child['gender'])}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">ذكر</SelectItem>
                        <SelectItem value="female">أنثى</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="cs-guardianName">اسم ولي الأمر (الأم/الأب/الوصي)</Label>
                    <Input id="cs-guardianName" value={currentCaseStudyData.basicInfo?.guardianName || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "guardianName", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-guardianPhoneNumber">رقم هاتف ولي الأمر</Label>
                    <Input id="cs-guardianPhoneNumber" value={currentCaseStudyData.basicInfo?.guardianPhoneNumber || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "guardianPhoneNumber", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-homeAddress">عنوان المنزل</Label>
                    <Input id="cs-homeAddress" value={currentCaseStudyData.basicInfo?.homeAddress || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "homeAddress", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-guardianRelationship">علاقة ولي الأمر بالطفل</Label>
                    <Input id="cs-guardianRelationship" value={currentCaseStudyData.basicInfo?.guardianRelationship || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "guardianRelationship", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-hasSiblings">هل هناك إخوة أو أخوات للطفل؟</Label>
                    <Select value={currentCaseStudyData.basicInfo?.hasSiblings || "no"} onValueChange={(value) => handleCaseStudyInputChange("basicInfo", "hasSiblings", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">نعم</SelectItem>
                        <SelectItem value="no">لا</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {currentCaseStudyData.basicInfo?.hasSiblings === "yes" && (
                    <div>
                      <Label htmlFor="cs-siblingsInfo">إذا كان نعم، اذكر أسماءهم وأعمارهم</Label>
                      <Textarea id="cs-siblingsInfo" value={currentCaseStudyData.basicInfo?.siblingsInfo || ""} onChange={(e) => handleCaseStudyInputChange("basicInfo", "siblingsInfo", e.target.value)} />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Pregnancy and Birth Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle>معلومات عن فترة الحمل والولادة</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="cs-motherAgeAtPregnancy">كم كان عمر الأم عندما حملت بهذا الطفل؟</Label>
                    <Input id="cs-motherAgeAtPregnancy" value={currentCaseStudyData.pregnancyAndBirthInfo?.motherAgeAtPregnancy || ""} onChange={(e) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "motherAgeAtPregnancy", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-fullTermPregnancy">هل كان الحمل كاملاً (9 أشهر) أم حدثت ولادة مبكرة؟</Label>
                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.fullTermPregnancy || "yes"} onValueChange={(value) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "fullTermPregnancy", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">حمل كامل</SelectItem>
                        <SelectItem value="no">ولادة مبكرة</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {currentCaseStudyData.pregnancyAndBirthInfo?.fullTermPregnancy === "no" && (
                    <div>
                      <Label htmlFor="cs-prematureBirthMonth">إذا كانت ولادة مبكرة، في أي شهر؟</Label>
                      <Input id="cs-prematureBirthMonth" value={currentCaseStudyData.pregnancyAndBirthInfo?.prematureBirthMonth || ""} onChange={(e) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "prematureBirthMonth", e.target.value)} />
                    </div>
                  )}
                  <div>
                    <Label htmlFor="cs-motherHealthIssuesDuringPregnancy">هل واجهت الأم أي مشاكل صحية خلال فترة الحمل؟</Label>
                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.motherHealthIssuesDuringPregnancy || "no"} onValueChange={(value) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "motherHealthIssuesDuringPregnancy", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">نعم</SelectItem>
                        <SelectItem value="no">لا</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {currentCaseStudyData.pregnancyAndBirthInfo?.motherHealthIssuesDuringPregnancy === "yes" && (
                    <div>
                      <Label htmlFor="cs-motherHealthIssuesDetails">إذا كان نعم، يرجى التوضيح (مشاكل صحية للأم)</Label>
                      <Textarea id="cs-motherHealthIssuesDetails" value={currentCaseStudyData.pregnancyAndBirthInfo?.motherHealthIssuesDetails || ""} onChange={(e) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "motherHealthIssuesDetails", e.target.value)} />
                    </div>
                  )}
                  <div>
                    <Label htmlFor="cs-deliveryType">هل حدثت الولادة بشكل طبيعي أم قيصري؟</Label>
                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.deliveryType || "natural"} onValueChange={(value) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "deliveryType", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="natural">طبيعي</SelectItem>
                        <SelectItem value="cesarean">قيصري</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="cs-childHealthIssuesAtBirth">هل واجه الطفل أي مشاكل صحية عند الولادة أو بعدها مباشرة؟</Label>
                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.childHealthIssuesAtBirth || "no"} onValueChange={(value) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "childHealthIssuesAtBirth", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">نعم</SelectItem>
                        <SelectItem value="no">لا</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {currentCaseStudyData.pregnancyAndBirthInfo?.childHealthIssuesAtBirth === "yes" && (
                    <div>
                      <Label htmlFor="cs-childHealthIssuesDetails">إذا كان نعم، يرجى التوضيح (مشاكل صحية للطفل)</Label>
                      <Textarea id="cs-childHealthIssuesDetails" value={currentCaseStudyData.pregnancyAndBirthInfo?.childHealthIssuesDetails || ""} onChange={(e) => handleCaseStudyInputChange("pregnancyAndBirthInfo", "childHealthIssuesDetails", e.target.value)} />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Reinforcer Response Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle>استجابة الطفل للمعززات والمكافآت</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="cs-favoriteToys">ما هي الأشياء التي يحب الطفل اللعب بها أو يحبها كثيراً؟</Label>
                    <Textarea id="cs-favoriteToys" value={currentCaseStudyData.reinforcerResponseInfo?.favoriteToys || ""} onChange={(e) => handleCaseStudyInputChange("reinforcerResponseInfo", "favoriteToys", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-enjoyableActivities">ما هي الأنشطة التي يستمتع بها الطفل؟</Label>
                    <Textarea id="cs-enjoyableActivities" value={currentCaseStudyData.reinforcerResponseInfo?.enjoyableActivities || ""} onChange={(e) => handleCaseStudyInputChange("reinforcerResponseInfo", "enjoyableActivities", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-favoriteFoods">هل هناك أطعمة معينة يحبها الطفل جداً؟</Label>
                    <Textarea id="cs-favoriteFoods" value={currentCaseStudyData.reinforcerResponseInfo?.favoriteFoods || ""} onChange={(e) => handleCaseStudyInputChange("reinforcerResponseInfo", "favoriteFoods", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-happinessExpression">ماذا يفعل الطفل عندما يكون سعيداً أو متحفزاً؟</Label>
                    <Textarea id="cs-happinessExpression" value={currentCaseStudyData.reinforcerResponseInfo?.happinessExpression || ""} onChange={(e) => handleCaseStudyInputChange("reinforcerResponseInfo", "happinessExpression", e.target.value)} />
                  </div>
                  <div>
                    <Label htmlFor="cs-motivationMethods">عندما تريد من الطفل أن يفعل شيئاً ما، ما الذي تستخدمه لتحفيزه أو مكافأته عادةً؟</Label>
                    <Textarea id="cs-motivationMethods" value={currentCaseStudyData.reinforcerResponseInfo?.motivationMethods || ""} onChange={(e) => handleCaseStudyInputChange("reinforcerResponseInfo", "motivationMethods", e.target.value)} />
                  </div>
                   <div>
                    <Label htmlFor="cs-smilesAtGuardian">هل يبتسم الطفل عندما يراك أو يسمع صوتك؟</Label>
                    <Select value={currentCaseStudyData.reinforcerResponseInfo?.smilesAtGuardian || "unknown"} onValueChange={(value) => handleCaseStudyInputChange("reinforcerResponseInfo", "smilesAtGuardian", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">نعم</SelectItem>
                        <SelectItem value="no">لا</SelectItem>
                        <SelectItem value="sometimes">أحياناً</SelectItem>
                        <SelectItem value="unknown">غير معروف/غير ملاحظ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="cs-communicatesNeeds">هل يحاول الطفل التواصل معك عندما يحتاج شيئًا؟</Label>
                    <Select value={currentCaseStudyData.reinforcerResponseInfo?.communicatesNeeds || "unknown"} onValueChange={(value) => handleCaseStudyInputChange("reinforcerResponseInfo", "communicatesNeeds", value)}>
                      <SelectTrigger><SelectValue /></SelectTrigger>
                      <SelectContent>
                        <SelectItem value="yes">نعم</SelectItem>
                        <SelectItem value="no">لا</SelectItem>
                        <SelectItem value="sometimes">أحياناً</SelectItem>
                        <SelectItem value="unknown">غير معروف/غير ملاحظ</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
          <DialogFooter className="gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => handlePrintCaseStudy()} disabled={!childData.caseStudy || !Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val))}>
                <Printer className="ml-2 h-4 w-4" /> طباعة دراسة الحالة
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline">إلغاء</Button>
            </DialogClose>
            <Button type="button" onClick={handleSaveCaseStudy}>حفظ دراسة الحالة</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </>
  );
}

