{"node": {"4098cb20d0bb4270082ef9403a594235c45035e36a": {"workers": {"app/children/[childId]/plan/page": {"moduleId": "[project]/.next-internal/server/app/children/[childId]/plan/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/generate-learning-plan.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/children/[childId]/plan/page": "action-browser"}}, "402305cc25f6a6a60f35c997089c1e13d5d6f96b3c": {"workers": {"app/children/[childId]/assessment/[assessmentId]/page": {"moduleId": "[project]/.next-internal/server/app/children/[childId]/assessment/[assessmentId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/analyze-skill-for-daily-routine.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/analyze-skill-for-preschool-routine.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/children/[childId]/assessment/[assessmentId]/page": "action-browser"}}, "40c436ebdbe9f241e9e6f8226d53aa1cf18ccecb42": {"workers": {"app/children/[childId]/assessment/[assessmentId]/page": {"moduleId": "[project]/.next-internal/server/app/children/[childId]/assessment/[assessmentId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/analyze-skill-for-daily-routine.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/analyze-skill-for-preschool-routine.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/children/[childId]/assessment/[assessmentId]/page": "action-browser"}}}, "edge": {}, "encryptionKey": "2j7wetXNVcEuas0AAq4fMATANCB2VwOdg4wsHWhIyK8="}