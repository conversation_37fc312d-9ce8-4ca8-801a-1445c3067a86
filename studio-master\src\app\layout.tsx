
import type { Metadata } from 'next';
import './globals.css';
import { SidebarProvider } from '@/components/ui/sidebar';
import { Toaster } from '@/components/ui/toaster';
import MainLayout from '@/components/layout/MainLayout';
import { APP_NAME } from '@/lib/constants';

export const metadata: Metadata = {
  title: `${APP_NAME} - تنمية الطفولة المبكرة`,
  description: `${APP_NAME}: التقييم والتنمية للطفولة المبكرة بناءً على برنامج بورتيج.`,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning>
      <body className="antialiased">
        <SidebarProvider>
          <MainLayout>{children}</MainLayout>
          <Toaster />
        </SidebarProvider>
      </body>
    </html>
  );
}
