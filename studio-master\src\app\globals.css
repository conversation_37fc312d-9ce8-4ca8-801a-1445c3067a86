@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 40% 98%; /* Soft, clean white-blue: #F6F9FC */
    --foreground: 220 15% 20%; /* Elegant charcoal blue: #2C3440 */
    
    --card: 0 0% 100%; /* Pure white: #FFFFFF */
    --card-foreground: 220 15% 20%; /* Same as foreground */
    
    --popover: 0 0% 100%;
    --popover-foreground: 220 15% 20%;
    
    --primary: 220 60% 52%; /* Elegant blue: #4D9AD8 */
    --primary-foreground: 220 25% 12%; /* Deep navy: #151D28 */
    
    --secondary: 220 30% 92%; /* Very soft blue-gray: #E6EDF5 */
    --secondary-foreground: 220 15% 35%; /* Muted slate: #4B5A6B */
    
    --muted: 220 30% 95%; /* Off-white with hint of blue: #F2F6FA */
    --muted-foreground: 220 10% 45%; /* Subtle slate: #5A6574 */
    
    --accent: 265 50% 58%; /* Refined violet: #8F6DD8 */
    --accent-foreground: 0 0% 100%; /* White */
    
    --destructive: 0 65% 50%; /* Mature red: #D64545 */
    --destructive-foreground: 0 0% 100%; /* White */
    
    --border: 220 25% 85%; /* Light cool gray-blue: #D0D8E2 */
    --input: 220 40% 97%; /* Almost white with cool hint: #F5F8FB */
    --ring: 220 70% 65%; /* Bright but soft blue: #78B7E8 */
    
    --chart-1: 220 60% 52%; 
    --chart-2: 265 50% 58%;
    --chart-3: 180 40% 50%; /* Teal */
    --chart-4: 30 80% 55%; /* Elegant orange: #F08A4B */
    --chart-5: 0 65% 55%; /* Mature red: #E35A5A */
    
    --radius: 0.5rem;
    
    --sidebar-background: 220 30% 96%; /* Very soft slate-blue: #F0F3F8 */
    --sidebar-foreground: 220 15% 20%;
    --sidebar-primary: 220 60% 52%;
    --sidebar-primary-foreground: 220 25% 12%;
    --sidebar-accent: 220 30% 92%;
    --sidebar-accent-foreground: 220 15% 35%;
    --sidebar-border: 220 25% 85%;
    --sidebar-ring: 220 70% 65%;
    
  }

  .dark {
    --background: 220 15% 12%; /* Deep slate-blue: #1A1E25 */
    --foreground: 220 20% 88%; /* Soft cool gray: #D9DFE6 */
    
    --card: 220 15% 18%; /* Slightly lighter: #23282F */
    --card-foreground: 220 20% 88%;
    
    --popover: 220 15% 18%;
    --popover-foreground: 220 20% 88%;
    
    --primary: 220 60% 60%; /* Soft electric blue: #68B2E0 */
    --primary-foreground: 220 25% 10%; /* Almost black-blue: #101820 */
    
    --secondary: 220 10% 25%; /* Deep slate: #3A3F46 */
    --secondary-foreground: 220 20% 85%; /* Soft light slate: #CBD2DA */
    
    --muted: 220 10% 22%; /* Dim cool gray: #2E343B */
    --muted-foreground: 220 10% 60%; /* Dusty light gray: #96A1AC */
    
    --accent: 265 50% 68%; /* Elegant lavender: #A98DE5 */
    --accent-foreground: 265 30% 15%; /* Dark plum: #1E1433 */
    
    --destructive: 0 65% 58%; /* Soft but serious red: #E35C5C */
    --destructive-foreground: 0 75% 15%; /* Dark red: #4A1212 */
    
    --border: 220 10% 30%; /* Dim slate: #40464F */
    --input: 220 15% 22%; /* Darker form field: #2A2F37 */
    --ring: 220 60% 68%; /* Brighter focus blue: #84C1ED */
    
    --chart-1: 220 60% 60%;
    --chart-2: 265 50% 68%;
    --chart-3: 180 35% 58%;
    --chart-4: 30 70% 60%;
    --chart-5: 0 60% 60%;
    
    --sidebar-background: 220 15% 14%; /* Very dark slate: #1D2128 */
    --sidebar-foreground: 220 20% 88%;
    --sidebar-primary: 220 60% 60%;
    --sidebar-primary-foreground: 220 25% 10%;
    --sidebar-accent: 220 10% 25%;
    --sidebar-accent-foreground: 220 20% 85%;
    --sidebar-border: 220 10% 30%;
    --sidebar-ring: 220 60% 68%;
    
  }
}

@layer base {
  html {
    direction: rtl; /* Add this for Right-to-Left text direction */
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; /* Add Arabic friendly fonts */
  }
}
