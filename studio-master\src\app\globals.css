@tailwind base;
@tailwind components;
@tailwind utilities;

@media (prefers-color-scheme: light) {
  :root {
    --text-50: #eff6f4;
    --text-100: #deedea;
    --text-200: #bddbd4;
    --text-300: #9cc9bf;
    --text-400: #7bb7aa;
    --text-500: #5ba494;
    --text-600: #488477;
    --text-700: #366359;
    --text-800: #24423b;
    --text-900: #12211e;
    --text-950: #09100f;

    --background-50: #ecf8f5;
    --background-100: #daf1eb;
    --background-200: #b4e4d6;
    --background-300: #8fd6c2;
    --background-400: #69c9ae;
    --background-500: #44bb99;
    --background-600: #36967b;
    --background-700: #29705c;
    --background-800: #1b4b3d;
    --background-900: #0e251f;
    --background-950: #07130f;

    --primary-50: #ebf9f6;
    --primary-100: #d8f3ec;
    --primary-200: #b1e7da;
    --primary-300: #8adbc7;
    --primary-400: #63cfb4;
    --primary-500: #3cc3a1;
    --primary-600: #309c81;
    --primary-700: #247561;
    --primary-800: #184e41;
    --primary-900: #0c2720;
    --primary-950: #061410;

    --secondary-50: #eafaf6;
    --secondary-100: #d5f6ed;
    --secondary-200: #abeddb;
    --secondary-300: #82e3c9;
    --secondary-400: #58dab7;
    --secondary-500: #2ed1a6;
    --secondary-600: #25a784;
    --secondary-700: #1c7d63;
    --secondary-800: #125442;
    --secondary-900: #092a21;
    --secondary-950: #051511;

    --accent-50: #e9fcf7;
    --accent-100: #d3f8ef;
    --accent-200: #a7f1df;
    --accent-300: #7beace;
    --accent-400: #4fe3be;
    --accent-500: #22ddae;
    --accent-600: #1cb08b;
    --accent-700: #158468;
    --accent-800: #0e5846;
    --accent-900: #072c23;
    --accent-950: #031611;

    /* Tailwind/shadcn compatible variables */
    --background: 160 50% 97%; /* background-50 equivalent */
    --foreground: 160 30% 15%; /* text-900 equivalent */

    --card: 0 0% 100%; /* Pure white */
    --card-foreground: 160 30% 15%; /* text-900 */

    --popover: 0 0% 100%;
    --popover-foreground: 160 30% 15%;

    --primary: 160 50% 50%; /* primary-500 equivalent */
    --primary-foreground: 160 80% 98%; /* primary-50 equivalent */

    --secondary: 160 60% 95%; /* secondary-100 equivalent */
    --secondary-foreground: 160 40% 25%; /* secondary-800 equivalent */

    --muted: 160 50% 96%; /* background-100 equivalent */
    --muted-foreground: 160 20% 40%; /* text-600 equivalent */

    --accent: 160 70% 55%; /* accent-500 equivalent */
    --accent-foreground: 160 80% 98%; /* accent-50 equivalent */

    --destructive: 0 65% 50%; /* Keep existing red */
    --destructive-foreground: 0 0% 100%; /* White */

    --border: 160 30% 85%; /* text-200 equivalent */
    --input: 160 50% 97%; /* background-50 equivalent */
    --ring: 160 50% 50%; /* primary-500 equivalent */

    --chart-1: 160 50% 50%; /* primary-500 */
    --chart-2: 160 70% 55%; /* accent-500 */
    --chart-3: 160 60% 45%; /* secondary-600 */
    --chart-4: 160 40% 35%; /* primary-700 */
    --chart-5: 160 80% 65%; /* accent-400 */

    --radius: 0.5rem;

    --sidebar-background: 160 50% 98%; /* background-50 */
    --sidebar-foreground: 160 30% 15%; /* text-900 */
    --sidebar-primary: 160 50% 50%; /* primary-500 */
    --sidebar-primary-foreground: 160 80% 98%; /* primary-50 */
    --sidebar-accent: 160 60% 95%; /* secondary-100 */
    --sidebar-accent-foreground: 160 40% 25%; /* secondary-800 */
    --sidebar-border: 160 30% 85%; /* text-200 */
    --sidebar-ring: 160 50% 50%; /* primary-500 */
  }
}

@media (prefers-color-scheme: dark) {
  :root {
    --text-50: #09100f;
    --text-100: #12211e;
    --text-200: #24423b;
    --text-300: #366359;
    --text-400: #488477;
    --text-500: #5ba494;
    --text-600: #7bb7aa;
    --text-700: #9cc9bf;
    --text-800: #bddbd4;
    --text-900: #deedea;
    --text-950: #eff6f4;

    --background-50: #07130f;
    --background-100: #0e251f;
    --background-200: #1b4b3d;
    --background-300: #29705c;
    --background-400: #36967b;
    --background-500: #44bb99;
    --background-600: #69c9ae;
    --background-700: #8fd6c2;
    --background-800: #b4e4d6;
    --background-900: #daf1eb;
    --background-950: #ecf8f5;

    --primary-50: #061410;
    --primary-100: #0c2720;
    --primary-200: #184e41;
    --primary-300: #247561;
    --primary-400: #309c81;
    --primary-500: #3cc3a1;
    --primary-600: #63cfb4;
    --primary-700: #8adbc7;
    --primary-800: #b1e7da;
    --primary-900: #d8f3ec;
    --primary-950: #ebf9f6;

    --secondary-50: #051511;
    --secondary-100: #092a21;
    --secondary-200: #125442;
    --secondary-300: #1c7d63;
    --secondary-400: #25a784;
    --secondary-500: #2ed1a6;
    --secondary-600: #58dab7;
    --secondary-700: #82e3c9;
    --secondary-800: #abeddb;
    --secondary-900: #d5f6ed;
    --secondary-950: #eafaf6;

    --accent-50: #031611;
    --accent-100: #072c23;
    --accent-200: #0e5846;
    --accent-300: #158468;
    --accent-400: #1cb08b;
    --accent-500: #22ddae;
    --accent-600: #4fe3be;
    --accent-700: #7beace;
    --accent-800: #a7f1df;
    --accent-900: #d3f8ef;
    --accent-950: #e9fcf7;

    /* Tailwind/shadcn compatible variables for dark mode */
    --background: 160 60% 8%; /* background-50 equivalent */
    --foreground: 160 30% 90%; /* text-900 equivalent */

    --card: 160 50% 12%; /* background-100 equivalent */
    --card-foreground: 160 30% 90%; /* text-900 */

    --popover: 160 50% 12%;
    --popover-foreground: 160 30% 90%;

    --primary: 160 50% 50%; /* primary-500 equivalent */
    --primary-foreground: 160 80% 8%; /* primary-50 equivalent */

    --secondary: 160 60% 15%; /* secondary-200 equivalent */
    --secondary-foreground: 160 40% 85%; /* secondary-800 equivalent */

    --muted: 160 50% 10%; /* background-100 equivalent */
    --muted-foreground: 160 20% 60%; /* text-600 equivalent */

    --accent: 160 70% 55%; /* accent-500 equivalent */
    --accent-foreground: 160 80% 8%; /* accent-50 equivalent */

    --destructive: 0 65% 58%; /* Keep existing red */
    --destructive-foreground: 0 75% 15%; /* Dark red */

    --border: 160 30% 25%; /* text-200 equivalent */
    --input: 160 50% 12%; /* background-100 equivalent */
    --ring: 160 50% 50%; /* primary-500 equivalent */

    --chart-1: 160 50% 50%; /* primary-500 */
    --chart-2: 160 70% 55%; /* accent-500 */
    --chart-3: 160 60% 45%; /* secondary-600 */
    --chart-4: 160 40% 35%; /* primary-700 */
    --chart-5: 160 80% 65%; /* accent-400 */

    --radius: 0.5rem;

    --sidebar-background: 160 50% 10%; /* background-100 */
    --sidebar-foreground: 160 30% 90%; /* text-900 */
    --sidebar-primary: 160 50% 50%; /* primary-500 */
    --sidebar-primary-foreground: 160 80% 8%; /* primary-50 */
    --sidebar-accent: 160 60% 15%; /* secondary-200 */
    --sidebar-accent-foreground: 160 40% 85%; /* secondary-800 */
    --sidebar-border: 160 30% 25%; /* text-200 */
    --sidebar-ring: 160 50% 50%; /* primary-500 */
  }
}

@layer base {
  html {
    direction: rtl; /* Add this for Right-to-Left text direction */
  }
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; /* Add Arabic friendly fonts */
  }
}
