
"use client"

import * as React from "react"
import { format } from "date-fns"
import { arSA } from "date-fns/locale" // For Arabic locale
import { Calendar as CalendarIcon } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  buttonClassName?: string;
  placeholder?: string;
}

export function DatePicker({ date, setDate, buttonClassName, placeholder = "اختر تاريخًا" }: DatePickerProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-right font-normal", // Changed text-left to text-right for RTL
            !date && "text-muted-foreground",
            buttonClassName
          )}
        >
          <CalendarIcon className="ml-2 h-4 w-4" /> {/* Changed mr-2 to ml-2 for RTL */}
          {date ? format(date, "PPP", { locale: arSA }) : <span>{placeholder}</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
          locale={arSA} // Set locale for Calendar
          dir="rtl" // Ensure calendar itself is RTL
        />
      </PopoverContent>
    </Popover>
  )
}
