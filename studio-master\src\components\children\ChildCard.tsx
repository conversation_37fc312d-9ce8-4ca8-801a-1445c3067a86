
import Link from 'next/link';
import type { Child } from '@/lib/types';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { CalendarDays, UserSquare, Edit, Trash2, Briefcase } from 'lucide-react'; // Added Briefcase for enrollment
import AgeDisplay from '@/components/assessment/AgeDisplay'; 
import { formatDate } from '@/lib/utils';

interface ChildCardProps {
  child: Child;
  onEdit: (child: Child) => void;
  onDelete: (childId: string) => void;
}

export default function ChildCard({ child, onEdit, onDelete }: ChildCardProps) {
  const fallbackName = child.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'C';

  return (
    <Card className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
      <CardHeader className="flex flex-row items-center gap-4 p-4 bg-muted/30">
        <Avatar className="h-16 w-16 border-2 border-primary">
          <AvatarImage src={child.avatarUrl} alt={child.name} data-ai-hint="child portrait"/>
          <AvatarFallback className="text-xl">{fallbackName}</AvatarFallback>
        </Avatar>
        <div>
          <CardTitle className="text-xl text-primary group-hover:text-primary-dark transition-colors">
            <Link href={`/children/${child.id}`}>
              {child.name}
            </Link>
          </CardTitle>
          <AgeDisplay birthDate={child.birthDate} className="text-sm text-muted-foreground" label="العمر:" />
        </div>
      </CardHeader>
      <CardContent className="p-4 flex-grow">
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <CalendarDays className="h-4 w-4 text-accent" />
            <span>تاريخ الميلاد: {formatDate(child.birthDate, 'PPP')}</span>
          </div>
          <div className="flex items-center gap-2">
            <Briefcase className="h-4 w-4 text-accent" /> {/* Changed icon for enrollment date */}
            <span>تاريخ الالتحاق: {formatDate(child.enrollmentDate, 'PPP')}</span>
          </div>
          <div className="flex items-center gap-2">
            <UserSquare className="h-4 w-4 text-accent" />
            <span>الأخصائي: {child.specialistName}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 border-t flex flex-col sm:flex-row gap-2">
        <Link href={`/children/${child.id}`} className="w-full sm:w-auto flex-grow">
          <Button variant="outline" className="w-full">
            عرض الملف الشخصي
          </Button>
        </Link>
        <div className="flex gap-2 w-full sm:w-auto">
          <Button variant="ghost" size="icon" onClick={() => onEdit(child)} className="text-blue-500 hover:text-blue-700 flex-1 sm:flex-none">
            <Edit className="h-4 w-4" />
            <span className="sr-only">تعديل</span>
          </Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(child.id)} className="text-red-500 hover:text-red-700 flex-1 sm:flex-none">
            <Trash2 className="h-4 w-4" />
            <span className="sr-only">حذف</span>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
