
import type { PortageDimension, Assessment, Child, User, UserRole, ProgressStatus, SessionNote, CaseStudyData } from './types';
import { format, parseISO } from 'date-fns'; // For session note dates

export const APP_NAME = "بورتيج بلس";

export const AGE_RANGES = [
  "0-9 أشهر",
  "9-18 شهرًا",
  "18-36 شهرًا",
  "3-4 سنوات",
  "4-5 سنوات",
  "5-6 سنوات",
  "غير محدد",
];

export const SKILL_STATUS_OPTIONS: { value: 'yes' | 'no' | 'unclear'; label: string; symbol: string }[] = [
  { value: 'yes', label: 'نعم', symbol: '✔️' },
  { value: 'no', label: 'لا', symbol: '❌' },
  { value: 'unclear', label: 'غير واضح', symbol: '❓' },
];

export const PROGRESS_STATUS_OPTIONS: { value: ProgressStatus; label: string; symbol: string; colorClass: string }[] = [
  { value: 'pending', label: 'قيد الانتظار', symbol: '⏳', colorClass: 'bg-gray-500 text-white' },
  { value: 'implemented', label: 'تم البدء بالتنفيذ', symbol: '🚀', colorClass: 'bg-blue-500 text-white' },
  { value: 'mastered', label: 'أُتقن الهدف', symbol: '🏆', colorClass: 'bg-green-600 text-white' },
  { value: 'postponed', label: 'تم التأجيل', symbol: '🗓️', colorClass: 'bg-orange-500 text-white' },
];


export const USER_ROLE_OPTIONS: { value: UserRole; label: string }[] = [
  { value: 'super_admin', label: 'إداري الشركة (المتحكم بالموقع)' },
  { value: 'eiu_manager', label: 'مدير وحدة التدخل المبكر' },
  { value: 'case_manager', label: 'مدير الحالة' },
  { value: 'specialist', label: 'الأخصائي' },
  { value: 'educator', label: 'المعلم' },
  { value: 'viewer', label: 'مشاهد' },
];

export const AGE_DISTRIBUTION_GROUPS: { label: string; minMonths: number; maxMonths: number; count?: number }[] = [
  { label: '0-1 سنة', minMonths: 0, maxMonths: 12 },
  { label: '1-2 سنة', minMonths: 12, maxMonths: 24 },
  { label: '2-3 سنوات', minMonths: 24, maxMonths: 36 },
  { label: '3-4 سنوات', minMonths: 36, maxMonths: 48 },
  { label: '4-5 سنوات', minMonths: 48, maxMonths: 60 },
  { label: '5-6 سنوات', minMonths: 60, maxMonths: 72 },
];


export const defaultCaseStudyData: CaseStudyData = {
  basicInfo: {
    childName: "",
    birthDate: "",
    currentAge: "",
    gender: "unknown",
    guardianName: "",
    guardianPhoneNumber: "",
    homeAddress: "",
    guardianRelationship: "",
    hasSiblings: "no",
    siblingsInfo: "",
  },
  pregnancyAndBirthInfo: {
    motherAgeAtPregnancy: "",
    fullTermPregnancy: "yes",
    prematureBirthMonth: "",
    motherHealthIssuesDuringPregnancy: "no",
    motherHealthIssuesDetails: "",
    deliveryType: "natural",
    childHealthIssuesAtBirth: "no",
    childHealthIssuesDetails: "",
  },
  reinforcerResponseInfo: {
    favoriteToys: "",
    enjoyableActivities: "",
    favoriteFoods: "",
    happinessExpression: "",
    motivationMethods: "",
    smilesAtGuardian: "unknown",
    communicatesNeeds: "unknown",
  },
};


export const PORTAGE_CHECKLIST_DATA: PortageDimension[] = [
  {
    id: "social",
    name: "البعد الاجتماعي",
    subCategories: [
      {
        id: "social-relationships",
        name: "العلاقات",
        skills: [
          { id: "soc-rel-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "ينظر لفترة مناسبة إلى مقدم الرعاية.", applicationMethod: "سؤال الأسرة/ مقدم الرعاية هل يحدق النظر الى وجهك لفترة مناسبة أثناء حمله بينما يواجه وجهه، وجهك؟." },
          { id: "soc-rel-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يظهر تمييزا لمقدمي الرعاية الرئيسيين.", applicationMethod: "سؤال الأسرة/ مقدم الرعاية هل يبتسم أو يحرك يديه حين يأتي أحدكم باتجاهه أو يسمع صوته؟." },
          { id: "soc-rel-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يعبر عن رغبته بوقف النشاط من خلال إشارة معينة (يتجهم، يبتعد، أو يبكي).", applicationMethod: "الملاحظة المباشرة أو سؤال الأسرة/ مقدم الرعاية يلاحظ الميسر ردود أفعال الرضيع لطول فترة التقييم معه وما اذا كان يبكي أو يظهر عدم ارتياحه كرغبة في وقف النشاط، وان لم يلاحظ ذلك بشكل مباشر بإمكانه سؤال الأسرة/ مقدم الرعاية." },
          { id: "soc-rel-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "ينظر إلى مقدم الرعاية منتظرا موافقته أو تأكيده على أمر ما.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ الميسر ما اذا كان الرضيع ينظر اليه قبل أن يمد يده لأخذ لعبة خاصة بالمقيم، وان لم يلاحظ ذلك بشكل مباشر بإمكانه سؤال الأسرة/ مقدم الرعاية." },
          { id: "soc-rel-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يقرأ انفعالات مقدم الرعاية وتعبيراته الوجهية.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يطلب من أحد أفراد الأسرة/ أو مقدم الرعاية أن يضحك بوجه الطفل، ومن ثم يظهر غضبه بتقطيب حاجبية، فإن لم يلاحظ تغير تعبيرات الطفل الوجهية استجابة لتغير ايماءات الكبير بشكل مباشر بإمكانه سؤال الأسرة/ مقدم الرعاية." },
          { id: "soc-rel-6", itemNumber: "6", ageRange: "9-18 شهرًا", behavior: "يشارك الكبار بطريقة عفوية.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يطلب المقدم من أحد أفراد الأسرة/ أو مقدم الرعاية أن يطلب من الطفل اعطاءه لعبة بيده، فإن لم يستجب بإمكانه سؤال الأسرة/ مقدم الرعاية عما اذا كان \"يطعمه اذا قال له الكبير: \"\"طعميني\"\"\"." },
          { id: "soc-rel-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يستكشف الفكاهة من خلال أدائه للأشياء المضحكة كي يحصل على الانتباه.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يطلب من أحد أفراد الأسرة/ أو مقدم الرعاية أن يقول له صحة، فإن لم يسعل بإمكانه سؤال الأسرة/ مقدم الرعاية عما اذا كان يسعل ان قال له: \"\"صحة\"\"، أو يكرر أي فعل يحصل من خلاله على انتباه الكبار." },
          { id: "soc-rel-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يجرب سلوكيات مختلفة لاختبار استجابات ومحددات مقدم الرعاية.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل يحاول لفت نظر والدته أو مقدم الرعاية أثناء انشغاله بالحديث للمقيم، قد يحاول فتح التلفاز أو يطلب الذهاب إلى الحمام أو يعبر عن جوعه، فإن لم يظهر ذلك يتم سؤال الأسرة/ مقدم الرعاية." },
          { id: "soc-rel-9", itemNumber: "9", ageRange: "18-36 شهرًا", behavior: "ينفذ بعض الأفعال أو يري الأشياء لمقدم الرعاية كي يحصل على موافقته.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل يأخذ اذنه قبل أخذ شيء من الألعاب الخاصة بالتقييم، فإن لم يكترث بها يتم سؤال الأسرة/ مقدم الرعاية عما اذا كان يستأذن أو يظهر رغبته بالحصول على شيء ما بطرق إيجابية." },
          { id: "soc-rel-10", itemNumber: "10", ageRange: "18-36 شهرًا", behavior: "ينفصل بسهولة عن والديه في البيئات المألوفة.", applicationMethod: "سؤال الأسرة/ مقدم الرعاية يسأل المقيم مقدم الرعاية عما اذا كان الطفل يقبل البقاء بعيدا عن والديه في البيئات المألوفة كبيت جده أو دار الحضانة النهارية مثلا." },
          { id: "soc-rel-11", itemNumber: "11", ageRange: "3-4 سنوات", behavior: "يتعاون مع طلبات الكبار غالبية الوقت.", applicationMethod: "الملاحظة المباشرة نطلب من الأسرة أن تطلب منه احضار شيء أو اغلاق الباب ونلاحظ استجابته." },
          { id: "soc-rel-12", itemNumber: "12", ageRange: "3-4 سنوات", behavior: "يظهر فخرا بما أنجزه دون مساعدة الكبار.", applicationMethod: "الملاحظة المباشرة وسؤال الأسرة/ مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل يظهر فخره بما أنجز بعد أن يعززه المقيم على استجابته بوضع نجمة على جبينه، ويحاول لفت نظر مقدم الرعاية لها، وان لم يفعل يمكن سؤال مقدم الرعاية عن ذلك." },
          { id: "soc-rel-13", itemNumber: "13", ageRange: "4-5 سنوات", behavior: "يحاول حل مشاكله مع أقرانه دون عراك جسدي بمساعدة من الكبار.", applicationMethod: "سؤال الأسرة/ مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل أثناء تفاعله مع أصدقاءه واذا ما واجهته مشاكل في اللعب معهم، يحاول حل مشكلاته معهم دون أن يلجأ مباشرة إلى العنف أو الضرب (قد يطلب المساعدة في ذلك من مقدم الرعاية أو الكبار من حوله)." },
          { id: "soc-rel-14", itemNumber: "14", ageRange: "5-6 سنوات", behavior: "يحل خلافاته مع الأطفال الآخرين دون مساعدة من الكبار.", applicationMethod: "سؤال الأسرة/ مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل أثناء تفاعله مع أصدقاءه واذا ما واجهته مشاكل في اللعب معهم، يلجأ إلى الحوار والتفاوض معهم (قد يطلب المساعدة في ذلك من مقدم الرعاية أو الكبار من حوله)." }
        ]
      },
      {
        id: "social-interaction",
        name: "التفاعل مع الاخرين",
        skills: [
          { id: "soc-int-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "ينام أو يحرك وجهه جانبا بعد أن يكون قد تعرض لمثيرات عديدة.", applicationMethod: "سؤال الأسرة عما اذا كان ينام أو يحرك وجهه جانبا بعد أن يكون قد تعرض لمثيرات عديدة." },
          { id: "soc-int-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يعيد الأصوات والأفعال التي تعزز الاستجابات التفاعلية.", applicationMethod: "الملاحظة المباشرة أو سؤال الأسرة نطلب من الأم أن تقلد الأصوات التي يصدرها ونلاحظ ما اذا كان يكررها، وان لم يكررها نسأل مقدم الرعاية." },
          { id: "soc-int-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يستجيب لواحد أو اثنين من التعبيرات اللفظية الروتينية.", applicationMethod: "الملاحظة المباشرة أو سؤال الأسرة نطلب من الأسرة أن تقول له نام ونلاحظ ما اذا كان يميل رأسه متظاهرا بالنوم، فان لم يفعل نسأل مقدم الرعاية." },
          { id: "soc-int-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يبادر ببعض الألعاب البسيطة ليلفت انتباه الكبار: تصفيق اليدين، لعبة الإخفاء باليد (أو بيييي).", applicationMethod: "الملاحظة المباشرة أو سُؤال مقدم الرعاية نلاحظ سلوك الطفل أثناء التقييم وما اذا كان يبادر ببعض الألعاب البسيطة ليلفت انتباه الكبار: تصفيق اليدين، لعبة الإخفاء باليد (أو بيييي) وان لم يفعل نسأل مقدم الرعاية." },
          { id: "soc-int-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يبتهج لألعاب الفعل ورد الفعل (مثال: الاختفاء والبحث والوصول إلى شخص ما).", applicationMethod: "الملاحظة المباشرة: نطلب من مقدم الرعاية أن يلعب مع الطفل ألعاب الفعل ورد الفعل (مثال: الاختفاء والبحث والوصول إلى شخص ما) ونلاحظ استجابته." },
          { id: "soc-int-6", itemNumber: "6", ageRange: "9-18 شهرًا", behavior: "يتحكم بنفسه لمدة تتراوح ما بين 10-5 دقائق حين يتواجد بالقرب من الكبار.", applicationMethod: "الملاحظة المباشرة: نلاحظ سلوك الطفل العام بوجود المقيم وما اذا كان يستطيع البقاء منضبطا لفترة تتراوح ما بين 10-5 دقائق." },
          { id: "soc-int-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يأخذ دورا في الأنشطة والمحادثات مع بعض المساعدة.", applicationMethod: "الملاحظة المباشرة: يطرح المقيم على الطفل بعض الأسئلة البسيطة مثل: \"\"ما أسمك؟\"\" \"\"ما هي لعبتك المفضلة؟\"\" ونلاحظ استجابته، وان لم يستجب معنا نطلب من الأم سؤاله، ويؤجل السؤال لآخر الجلسة تحسبا ما اذا كانت ضعف استجابته خجلا." },
          { id: "soc-int-8", itemNumber: "8", ageRange: "3-4 سنوات", behavior: "يشارك الأطفال الآخرين مع بعض المساعدة من الكبار.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك مجموعة أطفال في موقف التقييم نلاحظ مستوى تفاعله الأطفال الآخرين مع بعض المساعدة من الكبار، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية." },
          { id: "soc-int-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يُشارك ضمن مجموعات صغيرة في الأنشطة التي تتضمن قوانين لمدة تتراوح ما بين 15-10 دقيقة.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك مجموعة أطفال في موقف التقييم نلاحظ مشاركته ضمن مجموعات صغيرة في الأنشطة التي تتضمن قوانين لمدة تتراوح من 15-10 دقيقة، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية." },
          { id: "soc-int-10", itemNumber: "10", ageRange: "4-5 سنوات", behavior: "يستمع إلى محادثات الكبار ويشارك فيها.", applicationMethod: "الملاحظة المباشرة: نلاحظ ما اذا كان يشارك في الحديث مع الكبار في الأمور المتعلقة به، شريطة أن تتم إدارة الحوار من المقيم بحيث يتجنب أي حديث سلبي عن الطفل أمامه." },
          { id: "soc-int-11", itemNumber: "11", ageRange: "4-5 سنوات", behavior: "يتحدث عن أصدقائه الجدد.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: يستفسر المقيم من الطفل عن أصدقاءه الجدد في الروضة أو منطقة اللعب، وان لم يستجب الطفل للمقيم بإمكانه الاستفسار من مقدم الرعاية عما اذا كان الطفل يحضر من الروضة ويخبره عن الأصدقاء الجدد الذين صادفهم أو ان التقاهم في حديقة عامة أو مكان عام." },
          { id: "soc-int-12", itemNumber: "12", ageRange: "4-5 سنوات", behavior: "يتحدث عن مشاعره ومشاعر الآخرين.", applicationMethod: "الملاحظة المباشرة أو سُؤال مقدم الرعاية يطلب المقيم من مقدم الرعاية أن يتظاهر بالعبوس، ويسأله (مابها والدتك/ والدك؟)، ثم يطلب من مقدم الرعاية الضحك، ويستفسر منه عما تغير في تعابير وجهه. إضافة إلى ذلك يستفسر المقيم أيضا من مقدم الرعاية عما اذا كان الطفل في العادة يأتي ويخبره عن غضبه أو فرحه معبرا عن مشاعره." },
          { id: "soc-int-13", itemNumber: "13", ageRange: "5-6 سنوات", behavior: "يختار أصدقاءه بنفسه.", applicationMethod: "سُؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يبادر في مكان عام أو في الروضة بالتعرف على الأطفال، أم أنه ينتظر القيام بذلك من الكبار أو الأقران الآخرين؟." },
          { id: "soc-int-14", itemNumber: "14", ageRange: "5-6 سنوات", behavior: "يتبع القواعد الموجهة للألعاب الذهنية والجسدية حين يلعب مع أقرانه.", applicationMethod: "سُؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يلعب الألعاب الذهنية أو الجسدية الجماعية (مثال: فتحت الوردة، طاق طاق طاقية)، سواء عند تجمع العائلة أو في الروضة." }
        ]
      },
      {
        id: "social-emotional-response",
        name: "الاستجابة الانفعالية",
        skills: [
          { id: "soc-er-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يستخدم التعبيرات الوجهية المختلفة لعكس حالته الانفعالية.", applicationMethod: "الملاحظة المباشرة أو سؤال الأسرة نلاحظ ما اذا كانت تعبيرات وجهه تعكس راحته أو عدم راحته، وان لم يظهرها نسأل مقدم الرعاية." },
          { id: "soc-er-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يعبر عن عدم راحته عند مغادرة مقدم الرعاية.", applicationMethod: "الملاحظة المباشرة نطلب من الأسرة مغادرة المكان وترك الطفل في الغرفة مع المقيّم مع بقاء عين مقدم الرعاية بالقرب من الباب لسرعة الاستجابة في حال بكاءه أو لطمأنته الفورية ان اظهر عدم راحة." },
          { id: "soc-er-3", itemNumber: "3", ageRange: "9-18 شهرًا", behavior: "يكرر محاولاته للوصول إلى النتائج المرجوة (مثال: يرمي لعبة فيلتقطها مقدم الرعاية بثبات).", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ خلال الجلسة ما اذا كان الطفل يحاول أن يلفت نظر الكبير من خلال سلوكيات كرمي الخشخيشة، وان لم يفعل أثناء وجود المقيم يمكنه سؤال مقدم الرعاية عن ذلك." },
          { id: "soc-er-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يحافظ على هدوئه لفترة قصيرة حين يكون محبطا.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ ردود فعل الطفل عند غياب مقدم الرعاية مثلا أو أخذ لعبة مفضلة منه، وان لم تظهر أي ردة فعل تعكس احباطه نسأل مقدم الرعاية." },
          { id: "soc-er-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يستكشف بثقة منزله أو البيئات المألوفة له.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ ذلك بشكل مباشر سواء كان يتنقل زحفا أو دحرجة أو بالكراجة أو بأي طريقة لاستكشاف بيئته، وان لم يُظهر ذلك نسأل مقدم الرعاية." },
          { id: "soc-er-6", itemNumber: "6", ageRange: "18-36 شهرًا", behavior: "يظهر تحكما بالنفس من خلال اتباع القوانين البسيطة (مثال: تناول الطعام على الطاولة).", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية، فاذا كان جاء وقت تناول الطعام للطفل أثناء التقييم، نلاحظ مستوى استجابته للتعليمات المتعلقة بتناول الطعام، وان لم يحدث ذلك فإن سؤال مقدم الرعاية يصبح ضروريا." },
          { id: "soc-er-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يظهر الحذر في الأوضاع الخطرة، ويتجنب الأخطار الشائعة.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية، إذا لاحظ المقيم ذلك الأمر فإن ذلك يكون مناسبا، ولكن لا ينبغي أن يضع المقيم الطفل في أي موقف خطر، وان لم يحدث الموقف أمامه، بإمكانه سؤال مقدم الرعاية عما إذا كان يتجنب المخاطر مثل الصوبة أو الدرج المرتفع أو غيرها من المخاطر الشائعة." },
          { id: "soc-er-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يحاول تأدية غالبية المهام باستقلالية (تنظيف الأنف، ارتداء الملابس، تناول الطعام، الذهاب إلى الحمام)", applicationMethod: "سؤال مقدم الرعاية عما اذا كان الطفل يحاول تأدية غالبية المهام باستقلالية (تنظيف الأنف، ارتداء الملابس، تناول الطعام، الذهاب إلى الحمام) حتى وان كان لا يتقن أداءها بشكل كامل، ولكن الهدف معرفة مستوى رغبته في الاستقلالية." },
          { id: "soc-er-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يستكشف المواقع والأشخاص غير المألوفين أثناء وجود مقدم الرعاية.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية هذا السؤال مرحلة متقدمة من سؤال (5)، ولكن يتعلق بالأشخاص والأماكن غير المألوفة، وهنا على المقيم ملاحظة سلوكه نحوه ورغبته في التعرف على المقيم أو التعامل معه، وان لم يظهر ذلك من الممكن سؤال مقدم الرعاية." },
          { id: "soc-er-10", itemNumber: "10", ageRange: "3-4 سنوات", behavior: "ينتظر من 5-3 دقائق حتى تلبى احتياجاته.", applicationMethod: "سؤال مقدم الرعاية عما إذا كان ينتظر من 5-3 دقائق حتى تلبى احتياجاته." },
          { id: "soc-er-11", itemNumber: "11", ageRange: "3-4 سنوات", behavior: "يعبر عن محبته نحو أصدقاء اللعب وأفراد الأسرة.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: يلاحظ المقيم اذا كان الطفل يعبر عن محبته لنحو أصدقاء اللعب ان وُجدوا، وأفراد الآسرة، وإن لم يُظهر ذلك بإمكانه سؤال مقدم الرعاية عن ذلك." },
          { id: "soc-er-12", itemNumber: "12", ageRange: "4-5 سنوات", behavior: "يتحكم بردود فعله الانفعالية بمساعدة الكبار.", applicationMethod: "سؤال مقدم الرعاية: يستفسر المقيم من مقدم الرعاية مما اذا كان الطفل سريع الغضب حال حدوث أمر يستدعي ذلك أما أنه يضبط نفسه ويتحكم بردود أفعاله." },
          { id: "soc-er-13", itemNumber: "13", ageRange: "4-5 سنوات", behavior: "يجرب بنفسه خبرات جديدة.", applicationMethod: "الملاحظة المباشرة يلاحظ المقيم ومن خلال تفاعله مع الطفل واستخدام الأدوات الموجودة في حقيبة التقييم ما اذا كان الطفل يحب المرور بخبرات التقييم ويتفاعل معها بشكل جيد أم أنه يرفض تجربة الأمور الجديدة." },
          { id: "soc-er-14", itemNumber: "14", ageRange: "4-5 سنوات", behavior: "يعمل لوحده في مهمة لمدة تتراوح ما بين 20-15 دقيقة.", applicationMethod: "الملاحظة المباشرة يلاحظ المقيم ومن خلال تفاعله مع الطفل، ما اذا كان يتنقل بسرعة بين الأنشطة ولا يركز انتباهه في النشاط الواحد لفترة مناسبة، أم أنه يلتزم بالعمل في المهمة (مثال: العمل بالمكعبات) لمدة تتراوح ما بين 20-15 دقيقة." },
          { id: "soc-er-15", itemNumber: "15", ageRange: "5-6 سنوات", behavior: "يتبع القوانين الموجهة لسلوكهم العام.", applicationMethod: "الملاحظة المباشرة وسؤال مقدم الرعاية يلاحظ المقيم ما اذا كان الطفل ينضبط لتنبيهات مقدم الرعاية بالجلوس مع المقيم ومتابعة تعليماته، كما ويستفسر من مقدم الرعاية عما اذا كان في العادة يلتزم بآداب الطعام أثناء تناول الوجبات." },
          { id: "soc-er-16", itemNumber: "16", ageRange: "5-6 سنوات", behavior: "يعبر عن حقوقه وواجباته.", applicationMethod: "الملاحظة المباشرة يستفسر المقيم من الطفل عن مسؤولياته تجاه والديه، وما يفترض أن يقوما به تجاههم." },
          { id: "soc-er-17", itemNumber: "17", ageRange: "5-6 سنوات", behavior: "يتحدث عن نفسه بإيجابية.", applicationMethod: "الملاحظة المباشرة يطلب المقيم من الطفل أن يحدثه عن نفسه ويلاحظ ما اذا كان تعبيره عن نفسه إيجابيا أم سلبيا." },
          { id: "soc-er-18", itemNumber: "18", ageRange: "5-6 سنوات", behavior: "يضع لنفسه أهدافا وينفذها.", applicationMethod: "الملاحظة المباشرة وسؤال مقدم الرعاية يستفسر المقيم من الطفل عن أهدافه للعطلة الصيفية، أو لعطلة منتصف الفصل الدراسي، ويسأل مقدم الرعاية عما اذا كان الطفل ينفذ ما يخطط له بالفعل." }
        ]
      },
      {
        id: "social-play-development",
        name: "تطور اللعب الاجتماعي",
        skills: [
          { id: "soc-spd-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يفضل الأشخاص عن الألعاب والأشياء.", applicationMethod: "التجريب المباشر بعد أن تطلب من والدته أن تمد يديها نحوه إيذانا بحمله، فيما تقوم بهز الخشخيشة، هل يميل نحو أمه أم الخشخيشة؟." },
          { id: "soc-spd-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يظهر سعادة ومتعة لمشاهدة الأطفال الآخرين وهم يلعبون.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم نلاحظ ما اذا كان ينظر اليهم وهم يلعبون، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية." },
          { id: "soc-spd-3", itemNumber: "3", ageRange: "18-36 شهرًا", behavior: "يلعب قرب الأطفال الآخرين، بينما ينفذ كل منهم نشاطا منفصلا.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم يحرص المقيم على توفير لعبة لكل طفل ونلاحظ ما اذا بقي يلعب بلعبته (بجانبهم ودون تفاعل معهم)، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية." },
          { id: "soc-spd-4", itemNumber: "4", ageRange: "18-36 شهرًا", behavior: "يقلد سلوكيات الآخرين أثناء اللعب (مثال: يتظاهر بالطبخ أو تناول وجبة خفيفة).", applicationMethod: "الملاحظة المباشرة: يعطي المقيم الطفل لعبة المطبخ ونبدأ بالتظاهر بالأكل بها أمامه، ونلاحظ ما اذا كان يبدأ بتقليدنا أثناء اللعب بالتظاهر بالطبخ أو تناول الوجبة الخفيفة." },
          { id: "soc-spd-5", itemNumber: "5", ageRange: "3-4 سنوات", behavior: "يلعب مع 3-1 أطفال ويتشاركون الألعاب.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم يلاحظ المقيم ما اذا كان الطفل يلعب بتعاون ويتشارك أدوات لعبة تشاركية كالمكعبات مع الأطفال الآخرين أثناء اللعب مع أقرانه، وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية. (هذا السؤال مرتبط مع سؤال 10 تنظيم ذاتي)." },
          { id: "soc-spd-6", itemNumber: "6", ageRange: "3-4 سنوات", behavior: "يتبع القوانين بتقليد حركات الأطفال الآخرين.", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية: في حال كان هناك أطفال في موقف التقييم يطلب المقيم منهم اللعب في لعبة جماعية لها قوانين محددة وتتضمن أداء بعض الحركات (مثال: كيف بنغسل أيدينا؟ هيك بيطيروا العصافير؟) وان لم يكن هناك أطفال نسأل السؤال لمقدم الرعاية." },
          { id: "soc-spd-7", itemNumber: "7", ageRange: "4-5 سنوات", behavior: "يشارك في اللعب الدرامي مع 4-3 أطفال لوقت مناسب.", applicationMethod: "سؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل قد أدى دورا في مسرحية أو مشهد تمثيلي، وما اذا كان يشارك في العادة في أنشطة الروضة الدرامية مع 4-3 أطفال." },
          { id: "soc-spd-8", itemNumber: "8", ageRange: "4-5 سنوات", behavior: "يأخذ دورا.", applicationMethod: "سؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل ينتظر دوره أم يحاول تجاوز الآخرين ولا يحترم الدور." },
          { id: "soc-spd-9", itemNumber: "9", ageRange: "5-6 سنوات", behavior: "يعمل بتعاون مع 5-3 أطفال آخرين.", applicationMethod: "الملاحظة المباشرة أو سُؤال مقدم الرعاية اذا كان التقييم يتم في بيئة التعلم المبكر، يلاحظ المقيم ما اذا كان الطفل يتعاون مع 5-3 أطفال آخرين في تنفيذ نشاط مشترك، وان لم يحدث التقييم في بيئة التعلم المبكر من الممكن أن يستفسر من الوالدين عما اذا كان يحدث ذلك في التجمعات الأسرية." }
        ]
      },
      {
        id: "social-creative-expression",
        name: "التعبير الإبداعي عن الذات",
        skills: [
          { id: "soc-sce-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يبتسم ويصدر بعض الأصوات والحركات للحصول على الانتباه.", applicationMethod: "الملاحظة المباشرة أو سؤال الأسرة نلاحظ ما اذا كان يبتسم أو يصدر بعض الأصوات والحركات للحصول على الانتباه، وان لم يفعل نسأل مقدم الرعاية." },
          { id: "soc-sce-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يختبر بنفسه طرق إصدار الأصوات المختلفة ويتحرك استجابة للموسيقى.", applicationMethod: "الملاحظة المباشرة: يضع الخشخيشة بيد الطفل ويلاحظ ما اذا كان يهزها لإصدار أصوات مختلفة، ويحرك جسمه استجابة لإيقاع مألوف ومفضل سمعه." },
          { id: "soc-sce-3", itemNumber: "3", ageRange: "18-36 شهرًا", behavior: "يستكشف الخيارات المتنوعة لاستخدام الأدوات الفنية وأدوات البناء (مثال: الألوان، معجونة اللعب، المكعبات).", applicationMethod: "الملاحظة المباشرة: يضع المكعبات بيد الطفل ويلاحظ ما إذا كان يتفاعل معها بشكل جيد ويستخدمها بشكل مناسب. يمكن الاستعانة بالألوان ومعجونة اللعب الغذائية الآمنة ان توفرت في بيئة التقييم." },
          { id: "soc-sce-4", itemNumber: "4", ageRange: "18-36 شهرًا", behavior: "يستجيب للموسيقى بالتصفيق أو الرقص أو بضرب أداتين معا.", applicationMethod: "الملاحظة المباشرة: هذه المرحلة متقدمة على سؤال 2، ففي تلك المرحلة تكون الحركة غير متناسقة، ولكن هنا يطلب المقيم من مقدم الرعاية يغني للطفل الأغنية المفضلة أو يديرها على الهاتف المحمول ويلاحظ ردة فعل الطفل عليها بالتصفيق أو الرقص أو ضرب يده بالطاولة أو غيرها من الحركات المُستجيبة للإيقاع." },
          { id: "soc-sce-5", itemNumber: "5", ageRange: "3-4 سنوات", behavior: "يستخدم قدراته الفنية والأدوات المتوفرة بيديه لتحقيق أهدافه الخاصة.", applicationMethod: "الملاحظة المباشرة: يضع المقيم المكعبات بيد الطفل ويلاحظ ما إذا كان يؤدي منها نماذج تعكس مهارته في استخدامها. يمكن الاستعانة بالألوان ومعجونة اللعب الغذائية الآمنة ان توفرت في بيئة التقييم." },
          { id: "soc-sce-6", itemNumber: "6", ageRange: "3-4 سنوات", behavior: "يشارك في الألعاب الموسيقية أو يستجيب للإيقاع الموسيقي.", applicationMethod: "الملاحظة المباشرة: يطلب المقيم من مقدم الرعاية أن يؤدي مع الطفل لعبة موسيقية (مثال: حركة وصنم، أو بعد الأناشيد التي تتضمن أداء مثل القفز والدوران)، ويلاحظ استجابته بتأدية حركات مناسبة للإيقاع." },
          { id: "soc-sce-7", itemNumber: "7", ageRange: "4-5 سنوات", behavior: "يستجيب للإيقاع بأداء حركات منظمة.", applicationMethod: "الملاحظة المباشرة وسؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما إذا كان للطفل مهارات أدائية مرتبطة بالرقص أو الدبكة، وان كان كذلك يطلب منه مشاهدة تسجيلات له، أو ان كان لدى الطفل الرغبة في أدائها أمام المقيم يمكنه مشاهدتها مباشرة." },
          { id: "soc-sce-8", itemNumber: "8", ageRange: "4-5 سنوات", behavior: "يرسم رسوما بسيطة ومفهومة أو يستخدم بعض الأدوات في تنفيذ تصميم معين.", applicationMethod: "الملاحظة المباشرة يطلب المقيم من الطفل أن يرسم باستخدام الورق وألوان الفلومستر، ويلاحظ ما اذا كانت رسومه مفهومة (مثال: الرأس واليدين والقدمين لرسم الرجل)." },
          { id: "soc-sce-9", itemNumber: "9", ageRange: "5-6 سنوات", behavior: "يرسم ويبني بتفصيل وإبداع أكثر.", applicationMethod: "الملاحظة المباشرة يطلب المقيم من الطفل أن يرسم باستخدام الورق وألوان الفلومستر، ويلاحظ ما اذا كانت رسومه تتضمن تفاصيل أكثر (مثال: الرأس بتفاصيل العينين والأنف والفم والأذنين والشعر واليدين والقدمين لرسم الرجل)." },
          { id: "soc-sce-10", itemNumber: "10", ageRange: "5-6 سنوات", behavior: "يشارك في الأنشطة الأدائية أو الإيقاعية مع الأطفال الآخرين (الغناء، الرقص، الخ).", applicationMethod: "الملاحظة المباشرة وسؤال مقدم الرعاية يستفسر المقيم من مقدم الرعاية عما اذا كان للطفل قد أدى عروضا أدائية مرتبطة بالرقص أو الدبكة في أي من احتفالات الروضة، وان كان كذلك يطلب منه مشاهدة تسجيلات له." }
        ]
      },
    ],
  },
  {
    id: "exploration-learning",
    name: "بعد الاستكشاف وطرق التعلم",
    subCategories: [
      {
        id: "exp-cognitive-development",
        name: "التطور الإدراكي",
        skills: [
            { id: "exp-cog-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يظهر وعيا بالأوضاع والجديدة والغريبة.", applicationMethod: "المُلاحظة المباشرة لردة فعل الطفل عند حضور المقيم أول مرة، وسؤال والدته عما اذا كانت ردة فعله هذه يومية أم هي بسبب وجود المقيم." },
            { id: "exp-cog-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يلعب بنفس اللعبة لمدة تتراوح من 5-2 دقائق.", applicationMethod: "المُلاحظة المباشرة نضع وعاء المكعبات وهو مفتوح أمامه ونلاحظ ما إذا كان يلعب بها لفترة مناسبة لا تقل عند دقيقتين." },
            { id: "exp-cog-3", itemNumber: "3", ageRange: "9-18 شهرًا", behavior: "يجمع أداتان معا أثناء اللعب.", applicationMethod: "المُلاحظة المباشرة نضع أمامه مكعبين، ونجلس مقابله ونحمل مكعبين، ثم نقوم بطرق المكعبين الذين بيدينا أمامه ونلاحظ ما اذا كان يقلد ما قمنا به." },
            { id: "exp-cog-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يستكشف طرقا للتحكم بالبيئة.", applicationMethod: "المُلاحظة المباشرة أثناء انشغاله بالمكعب، نسحبه من يده ونخفيه تحت غطاء الوعاء، أو تحت الفراش، ونلاحظ ما اذا كان يُحاول الوصول إليه بأي طريقة كانت." },
            { id: "exp-cog-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يمر بخبرات حول الأحجام والعلاقات الفراغية أثناء اللعب.", applicationMethod: "المُلاحظة المباشرة نضع وعاء المكعبات وهو مفتوح أمامه ونلاحظ ما اذا كان قد طور مهارات فراغية متقدمة من حيث وضع الأشياء تحت الغطاء مثلا، أو تكديسها فوق بعضها البعض، أو صفها على شكل قطار." },
            { id: "exp-cog-6", itemNumber: "6", ageRange: "18-36 شهرًا", behavior: "يستخدم أداة واحدة لتقوم مقام أخرى أثناء اللعب بتوجيه من الكبار.", applicationMethod: "الملاحظة المباشرة نمسك أمام الطفل قطعة من المكعبات مستطيلة الشكل ونتظاهر بأننا نرغب بالحديث معه عبر الهاتف، ونعطيه قطعة مشابهة لها، ونبدأ بالحديث كما لو كانت القطعة المستطيلة هاتفا، ونلاحظ ما اذا قربها في حينه أو لاحقا من أذنه للتظاهر بالحديث عبر الهاتف." },
            { id: "exp-cog-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يطابق ويقارن الأشكال المتشابهة.", applicationMethod: "الملاحظة المباشرة نضع أمام الطفل قطعة مربعة من المكعبات، ونطلب منه أن يختار ما يشبهها من مجموعة المكعبات، وكذلك قطعة مستطيلة، وأخيرا قطعة دائرية." },
            { id: "exp-cog-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يستخدم أصابعه لترتيب الأشياء أو وضع الأشياء الصغيرة في وعاء مفتوح أو لتقليب الصفحات.", applicationMethod: "الملاحظة المباشرة نطلب من الطفل إعادة وضع المكعبات في الوعاء واحدة تلو الأخرى." },
            { id: "exp-cog-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يصنف الأدوات في مجموعات.", applicationMethod: "الملاحظة المباشرة نطلب من الطفل جمع الألوان المتشابهة مع بعضها البعض– يمكن قبول الأداء إن جمع الأدوات حسب الشكل أو الحجم." },
            { id: "exp-cog-10", itemNumber: "10", ageRange: "3-4 سنوات", behavior: "يكمل أحجية مكونة من 10-5 قطع.", applicationMethod: "المُلاحظة المباشرة يضع الميسر أحجية مجزأة إلى 5 قطع، ويطلب من الطفل تركيب قطعها." },
            { id: "exp-cog-11", itemNumber: "11", ageRange: "4-5 سنوات", behavior: "يفهم بعض الأنماط ويقلدها.", applicationMethod: "الملاحظة المباشرة يختار المقيم من المكعبات 4 مكعبات لونها أصفر و4 مكعبات لونها أخضر من نفس الحجم، ويبدأ بوضع مكعب أصفر ثم أخضر ويطلب من الطفل أن يكمل ترتيبها بنفس التسلسل." },
            { id: "exp-cog-12", itemNumber: "12", ageRange: "5-6 سنوات", behavior: "يرتب الأشياء تبعا للطول أو الحجم.", applicationMethod: "الملاحظة المباشرة يحضر المقيم مكعبا ومستطيلا متوسط الطول، وآخر طويل ويطلب من الطفل ترتيبها من الأقصر إلى الأطول (اذا لم تتوفر مكعبات متفاوتة في الطول ممكن أن يكون الترتيب بحسب الحجم من الأصغر إلى الأكبر)." },
            { id: "exp-cog-13", itemNumber: "13", ageRange: "5-6 سنوات", behavior: "يركب أحجية مكونة من 10 قطع أو أكثر.", applicationMethod: "المُلاحظة المباشرة يضع الميسر أحجية مجزأة إلى 10 قطع، ويطلب من الطفل تركيب قطعها." },
            { id: "exp-cog-14", itemNumber: "14", ageRange: "5-6 سنوات", behavior: "يخبر عن أوجه شبه الأشياء وأوجه اختلافها.", applicationMethod: "الملاحظة المباشرة يحضر المقيم مكعبا كبيرا أحمر، ومكعبا صغيرا أصفر، ويطلب من الطفل أن يخبره عن الفرق بين المكعبين (الإجابة لابد أن تراعي الفروق في الحجم واللون)." },
        ]
      },
      {
        id: "exp-critical-thinking",
        name: "التفكير الناقد والاستكشاف",
        skills: [
            { id: "exp-crit-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يهز أداة محددة بصورة متكررة.", applicationMethod: "المُلاحظة المباشرة نطلب من الأم/ مقدم الرعاية أن تضع بيده خشخيشته الخاصة، ونلاحظ إذا هزها بصورة متكررة (ان لم تتواجد لدى الطفل خشخيشة يستعين المقيم بخشخيشة من حقيبة ألعابه)." },
            { id: "exp-crit-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يبحث ببصره عن مصدر صوت ما.", applicationMethod: "المُلاحظة المباشرة نطلب من الأم/ مقدم الرعاية أن تحرك الخشخيشة خلف رأسه، ونلاحظ ما اذا كان يبحث ببصره عن مصدر الصوت (ان لم تتواجد لدى الطفل خشخيشة يستعين المقيم بخشخيشة من حقيبة ألعابه)." },
            { id: "exp-crit-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يفضل الألعاب غير المألوفة على الألعاب المألوفة.", applicationMethod: "المُلاحظة المباشرة نطلب من الأم/ مقدم الرعاية أن تُحضر احدى أدوات الطفل المألوفة له، وتضع أمامه لعبته المألوفة بالإضافة إلى الخشخيشة الخاصة بالمقيم والموجودة في حقيبة التقييم، ونلاحظ ما اذا كان مد يده أو وجه بصره نحو خشخيشة المقيم كمؤشر لتفضيله الألعاب غير المألوفة على الألعاب المألوفة." },
            { id: "exp-crit-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يبدأ بتسمية الأشياء/ الأشخاص عند غيابهم.", applicationMethod: "سؤال مقدم الرعاية نستفسر من مقدم الرعاية عما اذا كان الطفل يسمي أشخاصا مألوفين له عند غيابهم (مثال: تسمية ماما، أو بابا، أو أحد أشقائه) في مؤشر عن استفقاد لهم." },
            { id: "exp-crit-5", itemNumber: "5", ageRange: "18-36 شهرًا", behavior: "يظهر فهمه لاثنين أو أكثر من الكلمات الفئوية.", applicationMethod: "الملاحظة المباشرة نطلب منه بتزويدنا بمكعب، وان زودنا بمكعب واحد نقول له اعطني مكعبات، ونلاحظ ما اذا أعطانا أكثر من مكعب في مؤشر لفهمه لصيغة الجمع (الكلمات الفئوية)." },
            { id: "exp-crit-6", itemNumber: "6", ageRange: "18-36 شهرًا", behavior: "يخبر عن سلسلة من نشاطين مألوفين.", applicationMethod: "الملاحظة المباشرة نسأله عما يفعله عند الاستيقاظ من النوم، ونلاحظ ما اذا كان يسرد حدثين على الأقل بالتسلسل (تقبل الإشارة إذا كان الطفل غير ناطق)." },
            { id: "exp-crit-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يستخدم الخصائص الفردية لكل لعبة وأداة أثناء اللعب.", applicationMethod: "الملاحظة المباشرة نزوده بأدوات المطبخ وبوعاء فيه مكعبات، ونطلب منه بناء بيت، ونلاحظ ما اذا كان يستخدم المكعبات في ذلك، ثم نطلب منه التظاهر بطهي الطعام، ونلاحظ ما اذا كان يستخدم لعبة أدوات المطبخ في ذلك." },
            { id: "exp-crit-8", itemNumber: "8", ageRange: "3-4 سنوات", behavior: "يسمي الأشياء التي تتناسب معا أو يزاوج بينها عندما يطلب منه ذلك.", applicationMethod: "الملاحظة المباشرة نضع القلم والملعقة أمامه على الطاولة (أو الأرض)، ثم نطلب منه أن يضع المبراة مع ما يناسبها من الأدوات الموجودة على الأرض، ثم أن يضع الشوكة مع ما يناسبها من الأدوات الموضوعة على الأرض." },
            { id: "exp-crit-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يجيب على أسئلة (ماذا لو...؟) بذكر الأفعال المناسبة.", applicationMethod: "الملاحظة المباشرة نسأله عما يحدث إذا اقترب أحد من النار؟، أو إذا كسر أحد كوب زجاج؟ (تقبل الإشارة إذا كان الطفل غير ناطق)." },
            { id: "exp-crit-10", itemNumber: "10", ageRange: "4-5 سنوات", behavior: "يخبر عن الأنشطة اللاحقة.", applicationMethod: "الملاحظة المباشرة نسأله عما سيفعله عند انتهاء التقييم (تقبل الإشارة إذا كان الطفل غير ناطق)." },
            { id: "exp-crit-11", itemNumber: "11", ageRange: "4-5 سنوات", behavior: "يُخبر عن توقعاته لما سيحدث.", applicationMethod: "الملاحظة المباشرة يستفسر مقدم الرعاية من الطفل عن توقعاته لما ستفعله الأسرة في العطلة الصيفية مثلا ، وقد يجيب: نذهب إلى رحلة، نذهب إلى البحر،....الخ." },
            { id: "exp-crit-12", itemNumber: "12", ageRange: "5-6 سنوات", behavior: "يتوصل إلى حلول متنوعة لكل سؤال أو مهمة أو مشكلة بالتعاون مع أقرانه أو الكبار من حوله.", applicationMethod: "الملاحظة المباشرة يستفسر مقدم الرعاية من الطفل عما يتوقع حدوثه في حال تعطلت سيارة والده، أو في حال انقطاع الكهرباء، ويلاحظ ما اذا كان يصل الى طرح حلول متنوعة ومقنعة حتى لو تشاور بدرجة قليلة مع من حوله من الكبار." },
        ]
      },
      {
        id: "exp-early-math",
        name: "الرياضيات المبكرة",
        skills: [
            { id: "exp-math-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يظهر تفضيله لألعاب أو أشياء محددة.", applicationMethod: "المُلاحظة المباشرة نضع أمامه الوعاء الذي يضم مجموعة من المكعبات، ونلاحظ ما اذا كان يفضل أحدها وينجذب ربما للونه أو لملمسه." },
            { id: "exp-math-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يضع أدوات في وعاء ويفرغها منه.", applicationMethod: "المُلاحظة المباشرة نضع وعاء المكعبات وهو مفتوح أمامه ونلاحظ ما إذا كان يضع المكعبات بداخل الوعاء ويخرجها منها." },
            { id: "exp-math-3", itemNumber: "3", ageRange: "18-36 شهرًا", behavior: "يعطي المزيد من الشيء حين يطلب منه ذلك.", applicationMethod: "الملاحظة المباشرة: يحضر مجموعة من المكعبات من حقيبة التقييم، ويضعها أمام الطفل، ويطلب أن يناوله أحدها، وبعد أن يناوله أحده يطلب منه اعطاءه المزيد (كمان)." },
            { id: "exp-math-4", itemNumber: "4", ageRange: "18-36 شهرًا", behavior: "يلمس ويعد من 3-1 أشياء.", applicationMethod: "الملاحظة المباشرة يحضر المقيم 3 مكعبات من حقيبة التقييم ويرتبها بشكل متتال، ثم يطلب من الطفل أن يضع أصبعه عليها واحدة تلو الأخرى ليعدها." },
            { id: "exp-math-5", itemNumber: "5", ageRange: "3-4 سنوات", behavior: "يميز الأرقام الملصقة (مثال: رقم الغرفة، رقم الصفحة).", applicationMethod: "الملاحظة المباشرة: يحضر المقيم من حقيبة التقييم قصة مصورة ويطلب من الطفل قراءة الأرقام البسيطة الموجودة فيها." },
            { id: "exp-math-6", itemNumber: "6", ageRange: "3-4 سنوات", behavior: "يلمس ويعد الأرقام والأشياء المتزايدة.", applicationMethod: "الملاحظة المباشرة: يحضر المقيم عدادا من حقيبة التقييم، ويطلب منه عد خط منها." },
            { id: "exp-math-7", itemNumber: "7", ageRange: "3-4 سنوات", behavior: "يزاوج بين الأشياء (واحد إلى واحد).", applicationMethod: "الملاحظة المباشرة: يضع المقيم 5 مكعبات منفردة كل على حدى، ثم يطلب من الطفل أن يضع مكعبا بجانب كل مكعب منفرد." },
            { id: "exp-math-8", itemNumber: "8", ageRange: "4-5 سنوات", behavior: "يعد إلى 7 مع مفهوم العدد.", applicationMethod: "الملاحظة المباشرة يحضر المقيم 7 مكعبات من حقيبة التقييم ويطلب من المقيم اخباره عن عددها." },
            { id: "exp-math-9", itemNumber: "9", ageRange: "4-5 سنوات", behavior: "يظهر فهما للمقاييس المختلفة.", applicationMethod: "الملاحظة المباشرة يحضر المقيم من حقيبة التقييم لعبة الأوزان، والتي تتضمن قطعتي قماش متماثلتين تماما في الشكل ومختلفتين في الوزن، ويطلب من الطفل وضع احداها في كفه اليمين، والثانية في كفه اليسار، ثم يطلب منه وصفها بالكلمات (خفيف، وثقيل)." },
            { id: "exp-math-10", itemNumber: "10", ageRange: "5-6 سنوات", behavior: "يقرأ الأرقام المكتوبة من 5-1.", applicationMethod: "الملاحظة المباشرة يكتب المقيم الأرقام من 1 إلى 5 ويطلب من الطفل أن يقرأها بشكل غير متسلسل." },
            { id: "exp-math-11", itemNumber: "11", ageRange: "5-6 سنوات", behavior: "يقارن كميا بين المجموعات مستخدما كلمات بسيطة.", applicationMethod: "الملاحظة المباشرة يضع 5 مكعبات في المجموعة الأولى، و3 مكعبات في المجموعة الثانية، وأخيرا مكعبا واحدا لوحده ويطلب من الطفل وصفها مستخدما عبارات (قليل جدا، ومتوسط وكثير)." },
            { id: "exp-math-12", itemNumber: "12", ageRange: "5-6 سنوات", behavior: "يجمع ويطرح الأدوات والأشياء الملموسة.", applicationMethod: "الملاحظة المباشرة يحضر المقيم من حقيبة التقييم ثلاث مكعبات، ويسأل الطفل إذا ضفنا اليها مكعبين كم يصبح المجموع؟ ثم يسأله بعد أن أصبح عددها 5 مكعبات لو أخذت منها مكعبا كم يصبح العدد." },
        ]
      },
      {
        id: "exp-science",
        name: "العلوم",
        skills: [
          { id: "exp-sci-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يستكشف الألعاب بنفس الطريقة (يهزها، يرميها، يضربها، يضعها في فمه).", applicationMethod: "نلاحظ عند وضع مكعب بيد الطفل ما اذا كان يهزه، أو يرميه، أو يضربه، أو يضعه في فمه مُحاولة منه لاستكشافه." },
          { id: "exp-sci-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يبحث عن مقدم الرعاية أو عن لعبة أزيحت عن مستوى نظره.", applicationMethod: "نُقرب المكعب مستوى نظر الطفل (يفضل اختيار مكعب بلون غامق) ونزيحه فجأة من مستوى نظره ونلاحظ ما اذا كان الطفل يحرك رأسه بحثا عنه." },
          { id: "exp-sci-3", itemNumber: "3", ageRange: "18-36 شهرًا", behavior: "يظهر وعيا بنتائج أفعاله في الأوضاع المألوفة.", applicationMethod: "نسأل مقدم الرعاية عما اذا كان يعتذر ان فعل شيئا خاطئا، وما اذا كان يحاول تنظيف ما سكبه." },
          { id: "exp-sci-4", itemNumber: "4", ageRange: "3-4 سنوات", behavior: "يصف وظائف الأشياء.", applicationMethod: "يحضر المقيم قلم، ويسأل الطفل عما نعمله به، ثم يحضر مقص ويسأل الطفل عما نفعله به، ثم يحضر ملعقة ويسأل الطفل عما نفعله بها." },
          { id: "exp-sci-5", itemNumber: "5", ageRange: "4-5 سنوات", behavior: "يمر بخبرات جديدة ويجرب بنفسه أشياء ترتبط بها.", applicationMethod: "يستفسر المقيم من مقدم الرعاية بطرح بعض الأسئلة مثل: هل يحب الطفل المغامرات الجديدة؟ أو الذهاب إلى أماكن جديدة؟ أو تناول مأكولات غريبة وجديدة عليه؟." },
          { id: "exp-sci-6", itemNumber: "6", ageRange: "5-6 سنوات", behavior: "يحل المشكلات من خلال الاستكشاف بفاعلية والمرور بخبرات المحاولة والخطأ أو الدخول في حوارات ونقاشات مع الآخرين.", applicationMethod: "يستفسر من مقدم الرعاية عما إذا كان الطفل قد مر بتحد ما ولجأ إلى حله بمفرده أو ناقش الحلول المقترحة مع والده (مثال: إذا رغب الطفل في تناول شيء من الطعام أثناء انشغال الكبير، هل حاول تنفيذها لنفسه وبمفرده؟ أو ناقش طريقة عملها مع الطفل)." },
          { id: "exp-sci-7", itemNumber: "7", ageRange: "5-6 سنوات", behavior: "يلاحظ ويناقش ويصف عالم الطبيعة والكائنات الحية والعمليات الطبيعية.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل قد تعلم شيئا له علاقة بدورة حياة الكائنات الحية في الطبيعة، أو الكائنات التي تلد والتي تبيض، وان أجاب بالإيجاب يطلب من مقدم الرعاية أن يسأل الطفل أسئلة حولها." },
        ]
      },
    ],
  },
  {
    id: "communication",
    name: "البعد الاتصالي",
    subCategories: [
      {
        id: "comm-communication",
        name: "التواصل",
        skills: [
          { id: "comm-comm-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يظهر ردود فعل مناسبة حين يحرك أحد جسمه أو حين يكون محمولا.", applicationMethod: "نلاحظ ما اذا كان يُظهر ارتياحا حين يحرك مقدم الرعاية جسمه أو حين يحمله." },
          { id: "comm-comm-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يتوقف عن البكاء عندما يحمله شخص كبير.", applicationMethod: "نلاحظ ما اذا كان يتوقف عن البكاء عندما يحمله شخص كبير، وان لم يبك أثناء التقييم يمكننا سؤال مقدم الرعاية عن ذلك." },
          { id: "comm-comm-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يختلف بكاءه عند الجوع عن الألم عن عدم الراحة.", applicationMethod: "نسأل مقدم الرعاية عما اذا كان بكاءه يختلف باختلاف حاجته (مثال: بكاء الجوع يختلف عن بكاء الألم أو عن البكاء تعبيرا عن عدم الراحة)." },
          { id: "comm-comm-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يستخدم الأصوات والإشارات والإيماءات للفت الانتباه والتعليق والاستجابة.", applicationMethod: "عند انشغال مقدم الرعاية بالحديث مع المقيم، نلاحظ ما اذا بدأ الطفل باستخدام الأصوات والإشارات والإيماءات للفت الانتباه، فإن لم يحدث ذلك أثناء التقييم نستفسر من مقدم الرعاية." },
          { id: "comm-comm-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يفهم التعليمات البسيطة ويستجيب إليها.", applicationMethod: "نطلب من مقدم الرعاية أن يقول للطفل أي تعبير يفهمه عليه ونلاحظ استجابته له (مثال: هات، باي.....الخ)." },
          { id: "comm-comm-6", itemNumber: "6", ageRange: "9-18 شهرًا", behavior: "يحرك رأسه بالنفي أو الإيجاب بطريقة ذات معنى.", applicationMethod: "نطلب من مقدم الرعاية أن يسأل الطفل أسئلة بسيطة مثل (تأكل؟، تنام؟،...الخ) ونلاحظ ما اذا حرك رأسه بالنفي أو الإيجاب." },
          { id: "comm-comm-7", itemNumber: "7", ageRange: "9-18 شهرًا", behavior: "يشارك في الأناشيد والألعاب الغنائية.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن ينشد أنشودة مفضلة للطفل ونلاحظ ما اذا كان الطفل يتمتم معها بأصوات أو كلمات بسيطة، أو حتى بالإيقاع." },
          { id: "comm-comm-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يُعبر عن احتياجاته بالكلمات والأصوات والإيماءات (مثال: الحمام، والجوع، والألم).", applicationMethod: "يسأل المقيم مقدم الرعاية عما اذا كان الطفل يُعبر عن احتياجاته الأساسية (مثال: الحمام، والجوع، والألم).(بالكلمات والأصوات والإيماءات)." },
          { id: "comm-comm-9", itemNumber: "9", ageRange: "18-36 شهرًا", behavior: "يستخدم بعض كلمات القياس (كبير، صغير، قليل) في وصف الأشياء.", applicationMethod: "نخبر الطفل بأن من بين المكعبات الموضوعة أمامه مكعبات كبيرة وأخرى صغيرة، ثم نشير لأحد المكعبات ونقول له هذا..... وننتظر منه الإجابة، ثم نسأل عن الحجم الثاني." },
          { id: "comm-comm-10", itemNumber: "10", ageRange: "18-36 شهرًا", behavior: "يضحك على المثيرات والأفعال المُضحكة.", applicationMethod: "نطلب من مقدم الرعاية مداعبته بالطريقة المفضلة لديه، ونلاحظ استجابته بالابتسام أو الضحك." },
          { id: "comm-comm-11", itemNumber: "11", ageRange: "3-4 سنوات", behavior: "يتحدث عن الأشياء المألوفة، والأفعال والخبرات المباشرة أو القصص المألوفة.", applicationMethod: "يطلب المقيم من الطفل أن يتحدث عن الأشياء المألوفة (مثال: الروضة)، والأفعال والخبرات المباشرة (مثال: ماذا تفعل في البقالة) أو القصص المألوفة (مثال: القصة التي اعتادت جدته أن تقصها عليه)." },
          { id: "comm-comm-12", itemNumber: "12", ageRange: "3-4 سنوات", behavior: "يسأل أسئلة ليشبع فضوله ويجيب على أسئلة من وكيف وماذا.", applicationMethod: "نلاحظ خلال جلسة التقييم ما اذا كان الطفل يسأل أسئلة ليشبع فضوله (مثال: قد يسأل المقيم عما يوجد في حقيبته) ويجيب على أسئلة من وكيف وماذا (مثال: أن يجيب على أسئلة مقدم الرعاية التي تبدأ بمن- من أمك- أو كيف-,كيف حالك- أو ماذا- مثل ماذا أفطرت؟-( تُقبل الإشارة في الحالات التي تتطلب قبولها." },
          { id: "comm-comm-13", itemNumber: "13", ageRange: "3-4 سنوات", behavior: "يستخدم اللغة في التحكم والتفاوض أثناء اللعب مع الآخرين.", applicationMethod: "خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يستخدم كلمات بسيطة محكية أو بالإشارة في النقاش مع المقيم أو حتى مع مقدم الرعاية، وإن لم يظهر ذلك نستفسر من مقدم الرعاية." },
          { id: "comm-comm-14", itemNumber: "14", ageRange: "4-5 سنوات", behavior: "يتبع التعليمات المعقدة التي تتضمن أكثر من طلب.", applicationMethod: "يطلب منه مقدم الرعاية أن يأخذ مكعبا ويعطيه لوالدته ثم يغلق الباب (أو يفتح الباب) ان استجاب الطفل بسهولة ولم يعد بعد الطلب الأول دون تنفيذ بقية الطلبات لا تعتبر استجابته مناسبة." },
          { id: "comm-comm-15", itemNumber: "15", ageRange: "5-6 سنوات", behavior: "يشارك تجاربه أو ممتلكاته لفظيا مع أقرانه.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل في العادة يقبل بأن يلعب أقرانه عند زيارته في المنزل بألعابه أم أنه يرفض مشاركتها؟." },
          { id: "comm-comm-16", itemNumber: "16", ageRange: "5-6 سنوات", behavior: "يقول نكات بسيطة.", applicationMethod: "يطلب المقيم من الطفل أن يسرد عليه نكتة بسيطة." },
          { id: "comm-comm-17", itemNumber: "17", ageRange: "5-6 سنوات", behavior: "يتحدث بعبارات بسيطة من لغة أخرى (غير اللغة الأم).", applicationMethod: "يسأل المقيم مقدم الرعاية عما اذا كان الطفل قد تعلم بعض الكلمات بلغة غير الأم (اذا كانت اللغة العربية الأم قد تكون اللغة المطلوبة هي الإنجليزية)، ومن ثم يطلب منه أن يخبر الطفل بالحديث مستخدمها أو اخبار المقيم عنها." }
        ]
      },
      {
        id: "comm-speech-language",
        name: "الكلام واللغة",
        skills: [
          { id: "comm-sl-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يلعب بالأصوات، ويعيد أصواته وسجعاته وضحكاته الشخصية.", applicationMethod: "خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يناغي ويصدر أصواتا تعكس عن ارتياحه، وان لم يفعلها أمامنا نستفسر من مقدم الرعاية عن ذلك." },
          { id: "comm-sl-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يُثرثر أو يُحاول تقليد الأصوات الكلامية.", applicationMethod: "خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يجيب على كلام الكبير (المقيم أو مقدم الرعاية) بمناغاة أو أصوات يصدرها، وإن لم يفعل نستفسر من مقدم الرعاية." },
          { id: "comm-sl-3", itemNumber: "3", ageRange: "9-18 شهرًا", behavior: "يتبع التعليمات البسيطة كالإشارة إلى أجزاء الجسم، والأشياء أو يتبع تعليمات الخطوة الواحدة.", applicationMethod: "نطلب من مقدم الرعاية أن يسأله (أين عينك؟) ونلاحظ ما اذا كان يضع يده على عينه." },
          { id: "comm-sl-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يبدأ باستخدام أجزاء الكلمات للإشارة إلى الأشياء المألوفة (مثال: نم للإشارة إلى الطعام).", applicationMethod: "يلاحظ المقيم ما اذا كان يستخدم أجزاء الكلمات للإشارة إلى الأشياء المألوفة (مثال: نم للإشارة إلى الطعام) أثناء تواجده وان لم يفعل نستفسر من مقدم الرعاية." },
          { id: "comm-sl-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يستخدم عددا من الكلمات والمصطلحات والإشارات الرسمية.", applicationMethod: "يلاحظ المقيم ما اذا كان يستخدم عددا من الكلمات والمصطلحات (مثال: هات، راح، نام، باي) أثناء فترة التقييم." },
          { id: "comm-sl-6", itemNumber: "6", ageRange: "18-36 شهرًا", behavior: "يستخدم جملة مكونة من كلمتين أو ثلاث كلمات.", applicationMethod: "يلاحظ المقيم ما اذا كان الطفل أظهر استخدامه لجملة مكونة من كلمتين أو ثلاث كلمات أثناء فترة التقييم." },
          { id: "comm-sl-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يستخدم 50 كلمة أو إشارة.", applicationMethod: "يلاحظ المقيم مخزون الطفل اللغوي وما اذ كان يتحدث بكلمات واضحة (أو إشارات واضحة) للتعبير عن حاجاته وبما لا يقل عن 50 كلمة أو إشارة خلال فترة التقييم." },
          { id: "comm-sl-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يستخدم صيغ الجمع الشائعة.", applicationMethod: "نضع مكعب منفرد، ومجموعة مكعبات مع بعضها البعض ضمن مجموعة، ونشير الى المكعب المنفرد ونقول له هذا مكعب، ثم نشير إلى المجموعة ونقول له هذه..... وننتظر منه إتمام الكلمة (مكعبات.)." },
          { id: "comm-sl-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يستخدم جملا أطول في التواصل.", applicationMethod: "يلاحظ المقيم ما اذا كان الطفل يستخدم جملا أطول في التواصل طوال فترة التقييم بحيث تتناسب مع الأطفال في سن الرابعة." },
          { id: "comm-sl-10", itemNumber: "10", ageRange: "4-5 سنوات", behavior: "يتحدث مستخدما جملا معقدة لوصف الأحداث والأشياء.", applicationMethod: "خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يتحدث مستخدما جملا معقدة لوصف الأحداث والأشياء مع المقيم أو حتى مع مقدم الرعاية." },
          { id: "comm-sl-11", itemNumber: "11", ageRange: "4-5 سنوات", behavior: "يستخدم مفردات متنوعة أثناء الحديث.", applicationMethod: "خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يتحدث مستخدما مفردات متنوعة أثناء الحديث مع المقيم أو حتى مع مقدم الرعاية." },
          { id: "comm-sl-12", itemNumber: "12", ageRange: "4-5 سنوات", behavior: "يُخبر عن أضداد الكلمات.", applicationMethod: "يسأل المقيم الطفل عن المعنى المعاكس لبعض الكلمات (مثال: ما عكس نهار: ليل، وما عكس أبيض؟ أسود، وما عكس مفتوح؟ مغلق)." },
          { id: "comm-sl-13", itemNumber: "13", ageRange: "5-6 سنوات", behavior: "يستخدم جملا مكونة من 8-6 كلمات.", applicationMethod: "خلال تفاعلنا مع الطفل أثناء التقييم، نلاحظ ما اذا كان يتحدث مستخدما جملا طويلة مكونة من 8-6 كلمات أثناء الحديث مع المقيم أو حتى مع مقدم الرعاية." },
          { id: "comm-sl-14", itemNumber: "14", ageRange: "5-6 سنوات", behavior: "يربط بين الأصوات والكلمات المكتوبة.", applicationMethod: "يحضر المقيم قلما ورقة من حقيبة التقييم، ويقوم بكتابة كلمات بسيطة مثل (باب) ثم يقرأها أمام الطفل: بـــــــاااااب ثم يطلب منه أن يشير الى حرف الـ (ا) المكتوب، ونكرر ذلك مع أحرف العلة الأخرى (مثال: بووووووت، أين الـ (و)، بيييييييت، أين الـ (ي))." }
        ]
      },
      {
        id: "comm-early-reading",
        name: "القراءة المبكرة",
        skills: [
          { id: "comm-er-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "ينظر إلى جسم أو شخص عند تسميته.", applicationMethod: "ينادي المقيم باسم مقدم الرعاية ويلاحظ ما اذا كان الطفل ينظر اليه عند تسميته." },
          { id: "comm-er-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "ينظر إلى صور في كتاب الصور.", applicationMethod: "يضع المقيم أمام الطفل كتاب مصور شريطة أن تكون ألوانه متغايرة (واضحة وليست فاتحة)، ويلاحظ ما اذا كان الطفل ينظر إلى الصور الموجودة في الكتاب." },
          { id: "comm-er-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يستمع لمدة دقيقة إلى دقيقتين لقصة تسرد عليه.", applicationMethod: "يعطي المقيم الكتاب لمقدم الرعاية ويطلب منه سرد القصة الموجودة فيه على الطفل، ويلاحظ ما اذا كان الطفل يتابع القصة لمدة لا تقل عن دقيقة." },
          { id: "comm-er-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "يختار كتابا مفضلا لينظر إليه أو ليقرأه شخص كبير عليه.", applicationMethod: "يسأل المقيم مقدم الرعاية عما اذا كان لدى الطفل كُتب مناسبة لعمره، واذا كانت الأسرة معتادة على القراءة له وان كان الأمر كذلك يطلب منه احضار كتابين من كتب الطفل، ويطلب منه أن يضعهما أمامه ويلاحظ ما اذا كان الطفل سيختار أحدهما سواء بالنظر أو مد يده إليه (ملاحظة: اذا لم تعتد الطفلة على القراءة للطفل ولا تتواجد لديه كتب مناسبة نضع إشارة x." },
          { id: "comm-er-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يُشير إلى صورة مسماة.", applicationMethod: "يعرض المقيم صورتين على الطفل لأشياء مألوفة (مثال: تفاحة وموزة/ ملعقة وصحن)، ويطلب منه الإشارة إلى صورة أحدها." },
          { id: "comm-er-6", itemNumber: "6", ageRange: "18-36 شهرًا", behavior: "يجد الأشياء المتطابقة ويوصل بينها.", applicationMethod: "يضع المقيم أمام الطفل مكعبا أحمر ومكعبا أخضر ومكعبا أصفر، ثم يعطيه مكعبا أحمر آخر ويطلب منه أن يضعه فوق الذي يشبهه أمامه، وكذلك الأمر بالنسبة لبقية الألوان." },
          { id: "comm-er-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يجد كتابا محددا عند الطلب ويجلس ليستمع إلى قصة لمدة تتراوح من 8-5 دقيقة.", applicationMethod: "يسأل المقيم مقدم الرعاية عما اذا كان لدى الطفل كُتب مناسبة لعمره، واذا كانت الأسرة معتادة على القراءة له وان كان الأمر كذلك يطلب من الطفل احضار كتابه المفضل، ليستمع معه لقصة يسردها مقدم الرعاية عليه (ملاحظة: اذا لم يعتد مقدم الرعاية على القراءة للطفل ولا تتواجد لديه كتب مناسبة نضع إشارة x." },
          { id: "comm-er-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يُخبر عن الأحداث المتمثلة في الكتب المصورة.", applicationMethod: "يستخدم المقيم احدى القصص الموجودة لديه، شريطة أن تكون صورها واضحة ومألوفة للأطفال، ويطلب من الطفل تقديم وصف بسيط لما يشاهده في صفحتين على الأقل من صفحات الكتاب." },
          { id: "comm-er-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يأخذ دورا في سرد القصص بتكرار الجمل التي تعاد فيها باستمرار أو بإكمال كلمات محددة.", applicationMethod: "يسأل المقيم مقدم الرعاية عما اذا كان قد اعتاد سرد القصص على الطفل حتى لو كان من الذاكرة بدون كتاب، ويطلب من الطفل أن يخبره عن أحداث تلك القصة بمساعدة مقدم الرعاية، ويقبل ما اذا سرد الطفل فقط الجمل التي تعاد في القصة باستمرار أو كلمات محددة من القصة (ملاحظة: اذا لم يعتد مقدم الرعاية على سرد القصص للطفل نضع إشارة x." },
          { id: "comm-er-10", itemNumber: "10", ageRange: "3-4 سنوات", behavior: "يتعرف على بعض الرموز والإشارات في بيئته.", applicationMethod: "يستخدم المقيم بطاقات الرموز البيئية (مثال إشارة الحمام الخاص بالرجال والحمام الخاص بالنساء) الموجودة في حقيبته، ويرفع رمز الرجل ورمز المرأة ويسأله أين رمز حمام الرجل؟ ثم أين رمز حمام النساء؟." },
          { id: "comm-er-11", itemNumber: "11", ageRange: "3-4 سنوات", behavior: "يربط أصوات الحروف المكونة للكلمات بأشكالها المكتوبة.", applicationMethod: "يحضر المقيم قلما ورقة من حقيبة التقييم، ويقوم بكتابة حروف العلة مثل (ا) ثم يقرأها أمام الطفل: اااااااااا ثم يطلب منه أن يشير الى حرف الـ (ا) المكتوب، ونكرر ذلك مع أحرف العلة الأخرى (مثال: وووووووووو، أين الـ (و)، ييييييييي، أين الـ (ي))." },
          { id: "comm-er-12", itemNumber: "12", ageRange: "4-5 سنوات", behavior: "يسرد الأغاني والأنغام من الذاكرة.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يخبره عن أنشودة يحفظها، ثم يطلب من الطفل أن ينشدها له." },
          { id: "comm-er-13", itemNumber: "13", ageRange: "4-5 سنوات", behavior: "يعيد سرد الأحداث الرئيسية لقصة مألوفة.", applicationMethod: "يطلب المقيم من الطفل أن يقص عليه قصة مألوفة وتُعتبر الاستجابة صحيحة في حال ذكر الطفل أبرز أحداث القصة وليس جميعها كما تعتبر صحيحة إن لم يسردها بالتسلسل الصحيح." },
          { id: "comm-er-14", itemNumber: "14", ageRange: "4-5 سنوات", behavior: "يقرأ 10 أحرف على الأقل بما فيها الأحرف المكونة ل أسمه.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عن 10 أحرف يتقن الطفل قراءتها، ثم يكتبها حرفا تلو الآخر ويطلب من الطفل قراءتها." },
          { id: "comm-er-15", itemNumber: "15", ageRange: "4-5 سنوات", behavior: "يتعرف على بعض الكلمات المكتوبة.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عن 3 كلمات مكتوبة يتقن الطفل قراءتها صوريا (دون تهجئة)، ثم يكتبها أمام الطفل ويطلب من الطفل قراءتها." },
          { id: "comm-er-16", itemNumber: "16", ageRange: "5-6 سنوات", behavior: "يحدد اليمين واليسار والأعلى والأسفل والمفاهيم المكانية ذات العلاقة.", applicationMethod: "يسأل المقيم الطفل عن يده اليمنى، ومن ثم يده اليسرى، وعينه اليمنى وعينه اليسرى ويطلب منه وضع أداة أعلى الطاولة، ومن ثم أسفلها، ولابد أن يستجيب الطفل لجميع هذه المفاهيم لاعتبار الاستجابة صحيحة." },
          { id: "comm-er-17", itemNumber: "17", ageRange: "5-6 سنوات", behavior: "يسرد 5-3 أحداث من قصة بالتسلسل الصحيح.", applicationMethod: "يطلب المقيم من الطفل أن يقص عليه قصة مألوفة وتُعتبر الاستجابة صحيحة فقط ان ذكر ثلاثة أحداث على الأقل بالتسلسل الصحيح." },
          { id: "comm-er-18", itemNumber: "18", ageRange: "5-6 سنوات", behavior: "يقرأ قصصا بسيطة.", applicationMethod: "يحضر المقيم قصة بسيطة بحيث تتضمن كلمات بسيطة بما لا يزيد عن ثلاث كلمات في الصفحة، ويطلب من الطفل قراءتها (من الممكن أن تكون القراءة تهجئة ولا تكون متقنة، ولكن لا تقبل الاستجابة في حال لم يتمكن الطفل من قراءة %50 من المطلوب منه)." }
        ]
      },
    ],
  },
  {
    id: "motor-activities",
    name: "بعد الأنشطة الحركية الهادفة",
    subCategories: [
      {
        id: "motor-gross",
        name: "المهارات الحركية الكبيرة",
        skills: [
          { id: "motor-gross-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يُحرك ذراعية وقدميه بطريقة عشوائية أثناء الاستلقاء على ظهره أو بطنه.", applicationMethod: "يُلاحظ المقيم أثناء التقييم ما اذا كان الطفل يُحرك ذراعيه وقدميه بطريقة عشوائية أثناء الاستلقاء على ظهره أو بطنه." },
          { id: "motor-gross-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يُحرك رأسه من جانب إلى آخر أثناء استلقائه على معدته.", applicationMethod: "يُلاحظ المقيم أثناء التقييم ما اذا كان الطفل يُحرك رأسه من جانب إلى آخر أثناء استلقائه على معدته، ويمكنه استخدام خشخيشة بتحريكها إلى جانبه لتحفيزه على ذلك." },
          { id: "motor-gross-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يحافظ على استقامة رأسه حين يُحمل بشكل عامودي.", applicationMethod: "يطلب المقيم من مقدم الرعاية حمل الطفل ويُلاحظ المقيم أثناء التقييم ما اذا كان الطفل يحافظ على استقامة رأسه حين يُحمل بشكل عامودي." },
          { id: "motor-gross-4", itemNumber: "4", ageRange: "0-9 أشهر", behavior: "يرفع رأسه، والجزء العلوي من جذعه، وذراعيه أثناء الاستلقاء على بطنه (وضعية الطائرة).", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل على بطنه ويلاحظ ما اذا كان يرفع رأسه، والجزء العلوي من جذعه، وذراعيه أثناء الاستلقاء على بطنه (وضعية الطائرة)، ويمكنه استخدام خشخيشة بتحريكها فوق رأسه أثناء الاستلقاء لتحفيزه على ذلك." },
          { id: "motor-gross-5", itemNumber: "5", ageRange: "0-9 أشهر", behavior: "يتدحرج من جانبه إلى ظهره.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل على جانبه ويلاحظ ما اذا كان يتدحرج من جانبه إلى ظهره، ويمكنه استخدام خشخيشة بتحريكها فوق رأسه أثناء الاستلقاء لتحفيزه على ذلك." },
          { id: "motor-gross-6", itemNumber: "6", ageRange: "0-9 أشهر", behavior: "يجلس بمساندة.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الجلوس ويُساعده في اسناده بوسائد مناسبة، ويلاحظ ما اذا كان يجلس بمساندة." },
          { id: "motor-gross-7", itemNumber: "7", ageRange: "0-9 أشهر", behavior: "يُحافظ على وضعية رأسه في المنتصف عندما يُسحب لوضعية الجلوس.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يسحب الطفل من وضعية الاستلقاء على الظهر إلى وضعية الجلوس بمسكه من تحت ابطيه، يلاحظ ما اذا كان الطفل يُحافظ على وضعية رأسه في المنتصف عندما يُسحب لوضعية الجلوس." },
          { id: "motor-gross-8", itemNumber: "8", ageRange: "0-9 أشهر", behavior: "يقلب من بطنه إلى ظهره وبالعكس بشكل مقصود.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل في وضعية الاستلقاء على بطنه، ويلاحظ ما اذا كان يقلب من بطنه إلى ظهره وبالعكس بشكل مقصود، ويمكنه استخدام خشخيشة بتحريكها إلى جانبه أثناء الاستلقاء لتحفيزه على ذلك." },
          { id: "motor-gross-9", itemNumber: "9", ageRange: "0-9 أشهر", behavior: "يزحف للأمام على معدته ويتقدم بواسطة ذراعيه.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل في وضعية الاستلقاء على معدته، ويلاحظ ما اذا كان يزحف للأمام على معدته ويتقدم بواسطة ذراعيه، ويمكنه استخدام خشخيشة بتحريكها أمامه لتحفيزه على ذلك." },
          { id: "motor-gross-10", itemNumber: "10", ageRange: "9-18 شهرًا", behavior: "يحرك جسمه للأمام والخلف بدفع يديه وركبتيه معا.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل في وضعية الاستلقاء على بطنه، ويلاحظ ما اذا كان يحرك جسمه للأمام والخلف بدفع يديه وركبتيه معا كبداية للحبو، ويمكنه استخدام خشخيشة بتحريكها أمامه أثناء الاستلقاء لتحفيزه على ذلك." },
          { id: "motor-gross-11", itemNumber: "11", ageRange: "9-18 شهرًا", behavior: "يجلس لوحده باعتدال.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الجلوس، ويلاحظ ما اذا كان يجلس لوحده دون اسناد وبشكل معتدل، مع الحرص على توفر سبل الأمان بوجود مقدم الرعاية أمامه لتلقيه اذا ما فقد توازنه." },
          { id: "motor-gross-12", itemNumber: "12", ageRange: "9-18 شهرًا", behavior: "يسحب نفسه إلى وضعية الوقوف.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الجلوس على الأرض قرب طاولة الوسط، ويلاحظ ما اذا كان يسحب نفسه إلى وضعية الوقوف مستندا على الطاولة، ويمكن الاستعانة بخشخيشة وتحريكها من منتصف الطاولة لتحفيزه على ذلك، مع توفير أقصى سبل الحماية حوله ووجود المقيم خفه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه." },
          { id: "motor-gross-13", itemNumber: "13", ageRange: "9-18 شهرًا", behavior: "يُبحر (يمشي مستندا على الأثاث).", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف مستندا على الطاولة، ويلاحظ ما اذا كان يمشي مستندا على الأثاث، ويمكن الاستعانة بخشخيشة وتحريكها أمامه لتحفيزه على ذلك، مع توفير أقصى سبل الحماية حوله ووجود المقيم ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه." },
          { id: "motor-gross-14", itemNumber: "14", ageRange: "9-18 شهرًا", behavior: "يقف لوحده.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف وملاحظة ما اذا كان يقف لوحده، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه." },
          { id: "motor-gross-15", itemNumber: "15", ageRange: "9-18 شهرًا", behavior: "يمشي باستقلالية.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف وملاحظة ما اذا كان يمشي لوحده، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه." },
          { id: "motor-gross-16", itemNumber: "16", ageRange: "18-36 شهرًا", behavior: "يرمي كرة من وضعية الوقوف أو الجلوس نحو الاتجاه المرغوب.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع الطفل بوضعية الوقوف (أو الجلوس إن لم يتمكن من الوقوف بعد) ويحضر المقيم كرة ويطلب من مقدم الرعاية أن يعطيها للطفل ثم يطلب منه أن يرميها باتجاهه." },
          { id: "motor-gross-17", itemNumber: "17", ageRange: "18-36 شهرًا", behavior: "يتسلق على كرسي الكبار، يدير نفسه ويجلس.", applicationMethod: "يطلب المقيم من مقدم الرعاية احضار لعبة أو أداة مفضلة للطفل ويضعها على كرسي الكبار الموجود في غرفة التقييم، ويلاحظ ما اذا كان الطفل يتسلق الكرسي للوصول إلى اللعبة ثم يدير نفسه ويجلس عليه ليلعب باللعبة (من الضروري مراعاة سبل الأمان والتأكد بأن الكرسي ثابت وغير متحرك بصورة قد تيسر سقوط الطفل منه)." },
          { id: "motor-gross-18", itemNumber: "18", ageRange: "18-36 شهرًا", behavior: "ينحني أو يقرفص ليلتقط لعبة من الأرض دون أن يسقط.", applicationMethod: "يضع المقيم خشخيشة على الأرض ويحركها من مكان منخفض، ثم يطلب من الطفل تناولها، ويُلاحظ ما اذا كان ينحني أو يقرفص ليلتقطها من الأرض دون أن يسقط (يحرص المقيم على تعقيم أي أداة توضع على الأرض بعد انتهاء التقييم)." },
          { id: "motor-gross-19", itemNumber: "19", ageRange: "18-36 شهرًا", behavior: "يصعد الدرج بوضع قدمين على كل درجة.", applicationMethod: "يطلب المقيم من مقدم الرعاية اصطحابه الى درج آمن، ويلاحظ ما إذا كان الطفل يصعد الدرج بوضع قدمين على كل درجة مستندا على الحائط أو الدرابزين، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه. في حال لم يكن هناك درج في البيت وأخبر مقدم الرعاية المقيم بأن الطفل لم يتعرض لهذه الخبرة مسبقا، توضع إشارة خطأ، أما إن أخبره بأنه يصعد الدرج بوضع قدمين على كل درجة في بيت جده أو غيره من الأماكن تُقبل الإجابة." },
          { id: "motor-gross-20", itemNumber: "20", ageRange: "18-36 شهرًا", behavior: "يقفز في مكانه برفع كلتا قدميه معا.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يساعده في الطلب من الطفل القفز في مكانه برفع كلتا قدميه، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه." },
          { id: "motor-gross-21", itemNumber: "21", ageRange: "18-36 شهرًا", behavior: "يصعد الدرج ويهبط منه واضعا قدم واحدة على كل درجة أثناء الإمساك بالدرابزين أو بمساعدة من الكبير.", applicationMethod: "يطلب المقيم من مقدم الرعاية اصطحابه الى درج آمن، ويلاحظ ما إذا كان الطفل يصعد الدرج ويهبط منه واضعا قدم واحدة على كل درجة أثناء الإمساك بالدرابزين أو بمساعدة مقدم الرعاية، مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه. في حال لم يكن هناك درج في البيت وأخبر مقدم الرعاية المقيم بأن الطفل لم يتعرض لهذه الخبرة مسبقا، توضع إشارة خطأ." },
          { id: "motor-gross-22", itemNumber: "22", ageRange: "3-4 سنوات", behavior: "يقود الدراجة أو العربات ذات العجلات مستخدما كلتا قدميه في تحريكها.", applicationMethod: "يستفسر المقيم من مقدم الرعاية ما اذا كان لدى الطفل دراجة أو عربة ذات عجلات، ويصطحبه الى مكان اللعب عليه ليلاحظ ما اذا كان الطفل يقود الدراجة أو العربات ذات العجلات مستخدما كلتا قدميه في تحريكها (دون استخدام البدالات) مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه وفي حال أخبر مقدم الرعاية المقيم بأن الطفل يمارس ذلك في الحضانة أو بيت جده أو أي مكان آخر وبتوازن، تقبل الإجابة أما اذا أخبر المقيم بأنه لم يمر بهذه الخبرة نهائيا توضع إشارة x." },
          { id: "motor-gross-23", itemNumber: "23", ageRange: "3-4 سنوات", behavior: "يتسلق درجتين على الأقل من هيكل التسلق.", applicationMethod: "في حال كان التقييم يتم في مركز التعلم المبكر يلاحظ المقيم بنفسه ما اذا كان الطفل يتسلق درجتين على الأقل من هيكل التسلق مع توفير أقصى سبل الحماية حوله ووجود المقيم أمامه ومقدم الرعاية خلفه لتلقيه في حال فقد توازنه، أما اذا كان الطفل في البيت وأخبر مقدم الرعاية المقيم بأن الطفل لم يتعرض لهذه الخبرة مسبقا، توضع إشارة خطأ أما إن أخبره بأنه يفعلها في المنتزه أو الحديقة العامة أو غيرها من الأماكن تُقبل الإجابة." },
          { id: "motor-gross-24", itemNumber: "24", ageRange: "3-4 سنوات", behavior: "يقفز فوق الأشياء أو عن صندوق أو من أعلى درجة ترتفع عن الأرض.", applicationMethod: "اذا كان هناك درج في البيت يتم الاستفادة منه، وان لم يكن هناك درج نضع قطعة كبيرة من المكعبات على الأرض ونطلب من الطفل القفز فوقها ثلاث مرات، مع الحرص على تعقيمها عند العودة الى المركز." },
          { id: "motor-gross-25", itemNumber: "25", ageRange: "3-4 سنوات", behavior: "يركض بخطوات واسعة وبسهولة مُحركا ذراعيه بتناسق.", applicationMethod: "ينسق المقيم مع مقدم الرعاية لملاحظة الطفل أثناء الركض، وذلك بتنفيذ نشاط عادة ما يحفزه على ذلك في مكان واسع وآمن ومن ثم يلاحظ ما إذا كان يركض بخطوات واسعة وبسهولة مُحركا ذراعيه بتناسق. في حال لم يتوفر مكان واسع وآمن، وأخبر مقدم الرعاية المقيم بأن الطفل يمارس ذلك في الحضانة أو بيت جده أو أي مكان آخر وبتوازن، تقبل الإجابة أما اذا أخبر المقيم بأنه لم يمر بهذه الخبرة نهائيا توضع إشارة x." },
          { id: "motor-gross-26", itemNumber: "26", ageRange: "3-4 سنوات", behavior: "يمد كلتا ذراعيه ليلتقط بيديه كرة مقذوفة نحوه.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يقف أمام الطفل ويرمي الكرة باتجاهه، ويلاحظ ما إذا كان الطفل يلتقط الكرة المقذوفة نحوه (من الممكن إعطاء الطفل ثلاث فرص، وملاحظة ما إذا كان يلتقط الكرة في احداها)." },
          { id: "motor-gross-27", itemNumber: "27", ageRange: "4-5 سنوات", behavior: "يعدو بسرعة.", applicationMethod: "اذ كان التقييم يتم في بيئة التعلم المبكر يلاحظ ما اذا كان الطفل يعدو (العدو هو المشي السريع) وان لم يكن كذلك يمشي المقيم بسرعة في مكان آمن ويطلب من الطفل المشي مثله." },
          { id: "motor-gross-28", itemNumber: "28", ageRange: "4-5 سنوات", behavior: "يلعب ألعابا تتضمن القفز وركل الكرة.", applicationMethod: "يطلب المقيم من الطفل أن يقفز من مكان إلى آخر كالأرنب (3 قفزات على الأقل) ومن ثم يلتقط كرة موضوعة على الأرض ليرميها باتجاه المقيم." },
          { id: "motor-gross-29", itemNumber: "29", ageRange: "4-5 سنوات", behavior: "يُحافظ على توازنه أثناء المشي على سطح ضيق أو على خشبة التوازن.", applicationMethod: "يضع المقيم حبلا على أرضية الغرفة ويمشي أمام الطفل على خط مستقيم دون أن يتجاوز الحبل الموضوع على الأرض، ومن ثم يطلب من الطفل أن يمشي مثله فوق الحبل." },
          { id: "motor-gross-30", itemNumber: "30", ageRange: "4-5 سنوات", behavior: "يرمي كرة صغيرة بيد واحدة لشخص آخر ويلتقطها عند قذفها باتجاهه مستخدما كلتا يديه.", applicationMethod: "يستعين المقيم بكرة صغيرة من حقيبة التقييم ويرميها باتجاه الطفل، ويلاحظ ما اذا كان يلتقطها بكلتا يديه وقد يضمها أيضا إلى صدره، ثم يطلب من الطفل أن يرميها باتجاهه." },
          { id: "motor-gross-31", itemNumber: "31", ageRange: "5-6 سنوات", behavior: "يُظهر مرونة وقدرة على الاحتمال أثناء لعبه في الساحة الخارجية.", applicationMethod: "يستفسر من مقدم الرعاية عما اذا كان الطفل يحب اللعب بالألعاب التي تتطلب مرونة في مناطق اللعب العامة مثل ألعاب التسلق، والركض ويتحمل ممارسة هذه الألعاب لفترة مناسبة." },
          { id: "motor-gross-32", itemNumber: "32", ageRange: "5-6 سنوات", behavior: "يثب بتبادل القدمين ليقفز عن الحبل.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يمسك بطرف الحبل بينما يمسك هو بالطرف الآخر ويرفعاه عن الأرض 15 سم، ثم يطلب من الطفل القفز عنه، مع الحرص على أن تكون منطقة التقييم خالية من أية عقبات أو معيقات أو عوامل خطورة." },
          { id: "motor-gross-33", itemNumber: "33", ageRange: "5-6 سنوات", behavior: "يلعب بالكرة بمهارة عالية تتضمن التصويب والالتقاط والتربيت عليها.", applicationMethod: "يستفسر من مقدم الرعاية عما اذا كان الطفل يمارس عادة اللعب بالكرة (ليس مجرد التقاط وتصويب)، وانما أيضا التربيت عليها بمهارة عالية." }
        ]
      },
      {
        id: "motor-fine-writing",
        name: "المهارات الحركية الصغيرة والكتابة المبكرة",
        skills: [
          { id: "motor-fine-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يمسك شيئا وضع بيده لفترة محددة ويتركه عن دون قصد.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل بيده ويلاحظ ما اذا كان يمسكها بيده لفترة دقيقة وقد يتركه عن دون قصد بعد ذلك، (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به)." },
          { id: "motor-fine-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يحرك كلتا ذراعيه باتجاه لعبة تتحرك في خط الوسط.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل بمستوى نظر الطفل في خط الوسط ويلاحظ ما اذا كان يحرك كلتا ذراعيه باتجاهها، (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به)." },
          { id: "motor-fine-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "ينقل لعبة من يد ل أخرى.", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل بيده ويلاحظ ما اذا كان ينقلها من يد لأخرى (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به)." },
          { id: "motor-fine-4", itemNumber: "4", ageRange: "0-9 أشهر", behavior: "يطعم نفسه بيديه.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يتناول أطعمة بسيطة كالبسكويت بيده، وان لاحظ ذلك أثناء تواجده يأخذه بعين الاعتبار دون سؤال الأسرة." },
          { id: "motor-fine-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يستخدم يديه وعينيه معا بتآزر (مثال: تناول الطعام بالأصابع، أو إدخال زر كبير موجود في دميته داخل العروة المخصصة له).", applicationMethod: "يطلب المقيم من مقدم الرعاية وضع خشخيشة الطفل أمامه ويلاحظ ما اذا كان يستخدم يديه وعينيه معا بتآزر لالتقاطها من أمامه (في حال لم تكن لدى الطفل خشخيشة من الممكن استعانة المقيم بخشخيشة من حقيبة التقييم الخاصة به)." },
          { id: "motor-fine-6", itemNumber: "6", ageRange: "9-18 شهرًا", behavior: "يمسك الأقلام في البداية للخربشة.", applicationMethod: "يحضر المقيم ورقة وقلم مناسبين من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل ويضع قلم الفلومستر أمامه ويلاحظ ما اذا بدأ يمسكه للخربشة." },
          { id: "motor-fine-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يلعب بالأشياء مستخدما كلتا يديه بتناسق.", applicationMethod: "يحضر المقيم مجموعة مكعبات من حقيبة التقييم، ويضعها على الطاولة أمام الطفل ويلاحظ ما اذا كان الطفل يلعب بها مستخدمات كلتا يديه بتناسق." },
          { id: "motor-fine-8", itemNumber: "8", ageRange: "18-36 شهرًا", behavior: "يؤدي حركات بأصابعه تتناسب مع كلمات الأغاني.", applicationMethod: "يسأل المقيم مقدم الرعاية عما اذا كان الطفل يؤدي بعض الحركات لأغنية مألوفة (مثال: هيك بيطيروا العصافير)، واذا أفاد بالإيجاب يطلب المقيم من مقدم الرعاية أن يغنيها له ويطلب من الطفل المشاركة في الحركات." },
          { id: "motor-fine-9", itemNumber: "9", ageRange: "18-36 شهرًا", behavior: "يرسم دوائر وخطوطا عامودية وأفقية.", applicationMethod: "يحضر المقيم ورقة وقلم مناسبين من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل ويضع قلم الفلومستر أمامه ويلاحظ ما اذا بدأ برسم خطوط عامودية وأفقية ودوائر (لا يشترط معرفة الطفل لاسمها، ولكن يمكن أن يرسمها المقيم أو مقدم الرعاية أمامه وينتظر من الطفل أن يقلدها حتى لو لم تكن الدوائر مغلقة أو الخطوط مستقيمة بشكل كامل تقبل الاستجابة، ولا تقبل ان كانت الخطوط عشوائية)." },
          { id: "motor-fine-10", itemNumber: "10", ageRange: "3-4 سنوات", behavior: "يستخدم المقص في قص الورقة إلى قسمين.", applicationMethod: "يحضر المقيم ورقة ومقص آمن ومناسب من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل ويطلب من الطفل أن يمسك المقص ويقص الورقة، ويشترط الدقة في مسك المقص وفتحه واغلاقه أثناء قص الورقة لصحة الإجابة." },
          { id: "motor-fine-11", itemNumber: "11", ageRange: "3-4 سنوات", behavior: "يرسم المربع مقلدا.", applicationMethod: "يحضر المقيم ورقة وقلم مناسبين من حقيبة التقييم ويضع الورقة على طاولة أمام الطفل وفوقها قلم الفلومستر أمامه ثم يرسم المقيم أو مقدم الرعاية أمامه مربع ويطلب من الطفل أن يقلده، ويشترط الدقة في الرسم لصحة الإجابة." },
          { id: "motor-fine-12", itemNumber: "12", ageRange: "4-5 سنوات", behavior: "يقص ويلصق أشكالا بسيطة.", applicationMethod: "يحضر مقصا وورقة، ويرسم على الورقة شكل مثلث وشكل مربع وشكل دائرة، ويطلب من الطفل قصها ومن ثم الصاقها على ورقة أخرى." },
          { id: "motor-fine-13", itemNumber: "13", ageRange: "4-5 سنوات", behavior: "ينسخ كلمات بسيطة.", applicationMethod: "يكتب المقيم على الورقة كلمات بسيطة تتضمن حرف علة (مثال: باب، توت، نور، بيت، بوت) ويلاحظ ما اذا كان الطفل ينسخ 3 كلمات على الأقل منها." },
          { id: "motor-fine-14", itemNumber: "14", ageRange: "5-6 سنوات", behavior: "يستخدم كلتا يديه في أداء مهمة معقدة.", applicationMethod: "يحضر مقدم الرعاية رباطا (أو شريطا قماشيا)، ويطلب من الطفل عقده ومن ثم فكه." }
        ]
      },
      {
        id: "motor-senses",
        name: "الحواس",
        skills: [
          { id: "motor-senses-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يُصدر بعض الأصوات، ويُظهر تعبيرات وجهية أو يُحرك جسده كاستجابة للتغيرات الصوتية.", applicationMethod: "نطلب من مقدم الرعاية أن يتحدث للطفل بلطف، ومن ثم بنبرة جادة ونلاحظ مستوى تغير تعبيرات الطفل الوجهية أو الجسدية كاستجابة لهذا التغير في نبرة صوت مقدم الرعاية." },
          { id: "motor-senses-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يسمح بحمله عن قرب لتغيير وضعية الجلوس، أو بحثا عن الراحة أو لتناول الطعام.", applicationMethod: "يلاحظ المقيم ما اذا كان الطفل يتقبل ضم مقدم الرعاية له وحمله أو يتذمر من حمله، وان تذمر يستفسر من مقدم الرعاية ما اذا كان هذا الأمر مستمر أو أنه طارئ بسبب مرض أو تعب." },
          { id: "motor-senses-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يستكشف الأشياء بواسطة فمه.", applicationMethod: "يلاحظ المقيم عند وضع مكعب بيد الطفل ما اذا كان يضعه في فمه مُحاولة منه لاستكشافه (يحرص المقيم على ابعاد المكعب فور تقريبه من فمه، وتعقيمه عند العودة إلى مكان العمل)." },
          { id: "motor-senses-4", itemNumber: "4", ageRange: "0-9 أشهر", behavior: "يتحمل الإضاءة الداخلية التقليدية دون أن يغلق عينيه أو دون أن تنحرفا.", applicationMethod: "يلاحظ الميسر ذلك بشكل مباشر ان كان الطفل يجلس بوجود الإضاءة الداخلية التقليدية دون تذمر أو انحراف للعينين، وان كانت الغرفة معتمة قليلا يطلب من مقدم الرعاية اشعاله ويلاحظ ردة فعل الطفل نحو ذلك." },
          { id: "motor-senses-5", itemNumber: "5", ageRange: "0-9 أشهر", behavior: "يستجيب الطفل لتغيير وضعيته عند تحريك جسمه (مثال: يتحرك للأمام ليتم إنزاله من كرسي مرتفع).", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يمد يده أمام الطفل كمؤشر عن رغبة مقدم الرعاية في حمل الطفل، ويلاحظ ردة الفعل نحو ذلك بتحرك الطفل مثلا للأمام كي يحمله." },
          { id: "motor-senses-6", itemNumber: "6", ageRange: "9-18 شهرًا", behavior: "ينقل انتباهه البصري من مثير إلى آخر.", applicationMethod: "يضع المقيم احدى دمى الأصابع على سبابته اليمنى، ثم دمية مختلفة على سبابته اليسرى، ومن ثم يحرك اليمنى أولا وبعد أن يلتفت الطفل لها يحضر اليسرى بجانبها وقرب خط منتصف الجسم، ويلاحظ ما اذا نقل الطفل انتباهه لها." },
          { id: "motor-senses-7", itemNumber: "7", ageRange: "9-18 شهرًا", behavior: "يسمح للأشياء والملامس بأن تلمس منطقة فمه ولسانه وخده.", applicationMethod: "يطلب المقيم من مقدم الرعاية أن يمرر ابهامه حول منطقة الفم والخد، ويلاحظ ما اذا كان الطفل يسمح له بذلك دون أن يظهر أي ردود فعل دفاعية مقاومة لذلك." },
          { id: "motor-senses-8", itemNumber: "8", ageRange: "9-18 شهرًا", behavior: "يربط المعلومات الحسية بالأنشطة الروتينية اليومية.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يظهر ردود أفعال تجاه المثيرات الحسية التمييزية والمرتبطة ببعض الأعمال الروتينية (مثال: يظهر استعدادا للنوم عند إطفاء الضوء، أو يبدأ بتحريك فمه عند سماء صوت رج زجاجة الرضاعة) هذا السؤال مرتبط بسؤال 6 (تنظيم ذاتي- نفس المرحلة العمرية)، و 12 (تنظيم ذاتي- مرحلة عمرية أكثر تقدما)." },
          { id: "motor-senses-9", itemNumber: "9", ageRange: "18-36 شهرًا", behavior: "يُمارس أنشطة التحمل كالأرجحة والدوران حول النفس وغيرها من الأنشطة التي تُنفذ بعكس الجاذبية.", applicationMethod: "إذا كان التقييم يتم في مركز التعلم المبكر، من المناسب أن يتم اصطحاب الطفل إلى المكان المخصص للمهارات الحركية الكبيرة، وملاحظة ما إذا كان يُمارس أنشطة التحمل كالأرجحة والدوران حول النفس وغيرها من الأنشطة التي تُنفذ بعكس الجاذبية، وان لم يكن كل ذلك متاحا من الممكن سؤال الأسرة عن ذلك." },
          { id: "motor-senses-10", itemNumber: "10", ageRange: "18-36 شهرًا", behavior: "يستمع للأصوات والموسيقى الصادرة بصوت متوسط دون إغلاق أُذنيه أو التذمر منها.", applicationMethod: "يُشغل الميسر أنشودة أطفال من هاتفه المحول، ويلاحظ ما اذا كان الطفل يستمع للأصوات والموسيقى الصادرة بصوت متوسط دون أن يظهر ردود فعل دفاعية مثل إغلاق أُذنيه أو التذمر منها." },
          { id: "motor-senses-11", itemNumber: "11", ageRange: "18-36 شهرًا", behavior: "يأكل أطعمة مختلفة القوام والنكهة.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يأكل أطعمة مختلفة القوام والنكهة مثل جبنة الكيري، والحلاوة الطحينية، وغيرها من المواد لزجة القوام والتي قد تُثير ردود فعل دفاعية لدى بعض الأطفال." },
          { id: "motor-senses-12", itemNumber: "12", ageRange: "18-36 شهرًا", behavior: "يلعب في الأماكن المغلقة (مثال: القنوات المغلقة، والصناديق).", applicationMethod: "اذا كان التقييم يتم في مركز التعلم المبكر، من المناسب أن يتم اصطحاب الطفل إلى المكان المخصص للمهارات الحركية الكبيرة، وملاحظة ما اذا كان يلعب في الأماكن المغلقة مثل القنوات والصناديق، وان لم يتوفر ذلك من المناسب الجلوس مع الطفل على الأرض، وتخصيص مكان جلوسه تحت الطاولة (مع مراعاة متطلبات السلامة)، وملاحظة مستوى استجابته لوجوده في ذلك المكان، وان لم يكن كل ذلك متاحا من الممكن سؤال الأسرة عن ذلك." },
          { id: "motor-senses-13", itemNumber: "13", ageRange: "3-4 سنوات", behavior: "يتحمل قوام وملامس الأدوات والأسطح المختلفة (الغراء، ألوان الأصابع، الرمل) عند استخدامها في الأنشطة.", applicationMethod: "نضع قليل من الصمغ السائل على وسط ورقة، ونطلب من الطفل تحريكها بأصبعه بشكل دائري، ونلاحظ ما اذا كان الطفل يتحمل قوامها أو يظهر ردود فعل تعكس عدم تحمله لملامسته، ومن المفضل إن كانت هناك مساحة من المكان الذي يتم فيه التقييم مخصصة للتلوين بألوان الأصابع أو للعب بالرمل يمكن أن يتم تنفيذ النشاط فيها." },
          { id: "motor-senses-14", itemNumber: "14", ageRange: "3-4 سنوات", behavior: "يتحمل المدخلات الحسية خلال أنشطة العناية الذاتية (تنظيف الأسنان، تصفيف الشعر، غسل الوجه واليدين).", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل لا يتذمر أو يظهر عدم تحمل لوضع معجون الأسنان والفرشاة داخل فمه، أو ملامسة الفرشاة أو المشط (دون ألم) لشعره، أو من ملامس الصابون عند غسل اليدين والوجه." },
          { id: "motor-senses-15", itemNumber: "15", ageRange: "4-5 سنوات", behavior: "يُنظم جسده أثناء اللعب (يتحرك بشكل عامودي أو أفقي في الفراغ بتوازن).", applicationMethod: "يلاحظ المقيم ما إذا كان الطفل يتحرك بتوازن ودون أن يرتطم بالأشياء أثناء التنقل والحركة، ويطلب منه القفز في مكانه، ويلاحظ ما اذا كان يقوم بذلك بشكل صحيح ومتوازن." },
          { id: "motor-senses-16", itemNumber: "16", ageRange: "4-5 سنوات", behavior: "يتخطى العقبات الموضوعة أمامه بنجاح.", applicationMethod: "يضع المقيم خمس مكعبات متفرقة على الأرض، ويطلب من الطفل أن يحضر إليه من بداية الغرفة مع مراعاة تخطي العقبات الموضوعة تارة بالقفز تارة بالمشي إلى اليمين أو اليسار. (على المقيم أن يراعي تعقيم أية أدوات توضع على الأرض بعد انتهاء التقييم، وخلو المنطقة من أي عقبات أو مصادر خطر والتأهب لتلقي الطفل عند أي تعثر محتمل)." },
          { id: "motor-senses-17", itemNumber: "17", ageRange: "5-6 سنوات", behavior: "ينظم المعلومات الحسية بشكل مناسب (يختار الملابس المناسبة للطقس، ويضبط درجة حرارة الماء عند غسل اليدين أو الاستحمام).", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل في العادة يختار ملابس مناسبة للطقس (مثال: لا يطلب ارتداء ملابس خفيفة في الطقس البارد)، وبأن لديه القدرة على ضبط حرارة الماء عند غسل اليدين أو الاستحمام." },
          { id: "motor-senses-18", itemNumber: "18", ageRange: "5-6 سنوات", behavior: "يمُرُ بخبرات مع وسائل الإعلام المختلفة (الحاسوب، التلفاز، أجهزة التسجيل، الخ).", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يقضي وقت الشاشة في أنشطة تفاعلية متنوعة لا تقتصر فقط على التلقي السلبي للمعلومة، وقد يتضمن ذلك استخدام الحاسوب والتلفاز والتطبيقات الذكية على ألا يزيد ذلك عن ساعة واحدة يوميا." }
        ]
      },
      {
        id: "motor-self-regulation",
        name: "التنظيم الذاتي",
        skills: [
          { id: "motor-selfreg-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يُظهر أشكالا متنوعة من الأوضاع الفسيولوجية (النوم بعمق، النوم مع وجود الضوء، التنبيه الخفيف، التنبيه الفعال، البكاء، النعاس).", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يتكيف بالنوم في أوضاع مختلفة عند نعاسه ويحتاج إلى القليل من التهيئة للنوم عند النعاس حتى وان كان هناك ضوء أو كانت الأسرة خارج المنزل، وان لاحظ المقيم بأن الطفل نام أثناء التقييم لا داعي للسؤال." },
          { id: "motor-selfreg-2", itemNumber: "2", ageRange: "0-9 أشهر", behavior: "يبدأ بالانتظار قليلا حتى تلبى حاجاته.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل ينتظر قليلا ويعبر بأصوات بسيطة قبل أن تُلبى احتياجاته، وقد يظهر أصوات بكاء أولية تعكس حاجاته (مثال: نييييه للجوع، وأوووه عند النعاس، وإيه عند التعب)، وان لاحظ المقيم ذلك بنفسه أثناء التقييم لا داعي للسؤال." },
          { id: "motor-selfreg-3", itemNumber: "3", ageRange: "0-9 أشهر", behavior: "يستخدم أساليب التهدئة الذاتية (مثال: يمص إصبعه، يضع اللهاية في فمه).", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يُهدئ نفسه بنفسه كأن يقرب ملابسه من فمه لتصبير نفسه على الجوع، أو مص أصبعه أو تقريب اللهاية من فمه، وان لاحظ المقيم ذلك بنفسه أثناء التقييم لا داعي للسؤال." },
          { id: "motor-selfreg-4", itemNumber: "4", ageRange: "9-18 شهرًا", behavior: "لديه حركة أمعاء طبيعية.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كانت حركة أمعاء الطفل طبيعية (مثال: لا يشكو من أي مشكلات في الإخراج كالإمساك أو الإسهال)." },
          { id: "motor-selfreg-5", itemNumber: "5", ageRange: "9-18 شهرًا", behavior: "يوقف النشاط أو يتردد عن إتمامه حين يقال له (لا).", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يتوقف عن تقريب يده من أي خطر محتمل (مثال: فتح واغلاق مقابس الكهرباء) بعد أن ينبهه شخص كبير بصوت حازم وجاد قائلا له (لا)، وان لاحظ المقيم ذلك بنفسه أثناء التقييم لا داعي للسؤال." },
          { id: "motor-selfreg-6", itemNumber: "6", ageRange: "9-18 شهرًا", behavior: "يتبع نماذج قابلة للتوقع في وقت النوم أو وقت تناول الطعام.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل لديه نماذج قابلة للتوقع للنوم أو وقت تناول الطعام (مثال: يظهر استعدادا للنوم عند إطفاء الضوء، أو يبدأ بتحريك فمه عند سماء صوت رج زجاجة الرضاعة) هذا السؤال مرتبط بسؤال رقم 12 من نفس البعد (مستوى أعلى)، وسؤال 9 حواس (نفس الفئة العمرية)." },
          { id: "motor-selfreg-7", itemNumber: "7", ageRange: "9-18 شهرًا", behavior: "يقبل التغيير في الروتين اليومي أو البيئة دون انزعاج واضح.", applicationMethod: "يلاحظ المقيم ما إذا كان وجوده في بيئة الطفل للتقييم قد أربك الطفل وأزعجه بشكل واضح وغير اعتيادي، وإن حدث ذلك يتأكد من مقدم الرعاية عما إذا كان هذا الانزعاج دائم أم أنه حدث بسبب وجود المقيم أو التغيير في المكان والروتين؟." },
          { id: "motor-selfreg-8", itemNumber: "8", ageRange: "9-18 شهرًا", behavior: "يعرض مستوى صوتي وطبقة صوتية مشابهة للأطفال الآخرين أو لبعض أفراد الأسرة.", applicationMethod: "يلاحظ المقيم ما إذا كان الطفل عند تفاعله داخل بيئة التقييم (بيئته المألوفة) يتحدث بنبرة صوت مشابهة لحديث الأطفال الآخرين من حوله أو بعض أفراد أسرته." },
          { id: "motor-selfreg-9", itemNumber: "9", ageRange: "18-36 شهرًا", behavior: "يتنقل بسهولة بين الأنشطة بتوجيه من الكبار.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يقبل بسهولة الانتقال بين الأنشطة سواء في المنزل أو مركز التعلم المبكر، وهنا من الممكن أن يكون التنقل بين الأنشطة بتوجيه من الكبار أو تذكير منهم." },
          { id: "motor-selfreg-10", itemNumber: "10", ageRange: "18-36 شهرًا", behavior: "يُشارك في نشاط لمدة تتراوح من 15-10 دقيقة دون مساعدة من الكبار.", applicationMethod: "يستفسر من مقدم الرعاية ما اذا كان الطفل يُشارك في نشاط لمدة تتراوح من 15-10 دقيقة دون مساعدة من الكبار (قد يكون نشاط لعب مثل بناء المكعبات)- هذا السؤال مرتبط بسؤال (5) تطور اللعب الاجتماعي." },
          { id: "motor-selfreg-11", itemNumber: "11", ageRange: "18-36 شهرًا", behavior: "ينتظر لوقت قصير حتى تُلبى احتياجاته.", applicationMethod: "يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يراعي مشاغل مقدم الرعاية وينتظر لوقت قصير حتى تُلبى احتياجاته دون أن يبكي مطالبا بتلبية حاجته فورا." },
          { id: "motor-selfreg-12", itemNumber: "12", ageRange: "3-4 سنوات", behavior: "لديه أنماط نوم وطعام روتينية قابلة للتوقع.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل لديه أنماط نوم وطعام روتينية قابلة للتوقع (مثال: يبادر من نفسه بتنظيف أسنانه والذهاب إلى الحمام وغسل اليدين قبل النوم)، هذا السؤال مرتبط بسؤال 6 من نفس البعد وسؤال 9 حواس (كلا السؤالين من فئة عمرية أقل)." },
          { id: "motor-selfreg-13", itemNumber: "13", ageRange: "3-4 سنوات", behavior: "ينام نوما عميقا بمساعدة قليلة.", applicationMethod: "يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل ينام نوما عميقا بمساعدة قليلة تتعلق بتهيئته للنوم من خلال قراءة قصة قصيرة، ولا يستيقظ إلا مرة على الأكثر لقضاء حاجة كالذهاب إلى الحمام أو شرب الماء." },
          { id: "motor-selfreg-14", itemNumber: "14", ageRange: "4-5 سنوات", behavior: "يستخدم كميات مناسبة من الأشياء الموضوعة أمامه (يصب كمية مناسبة من الحليب، يوزع كمية مناسبة من الصمغ السائل).", applicationMethod: "يحضر المقيم صمغا سائلا وورقة من حقيبة التقييم ويطلب من الطفل استخدامه للصق ورقة شقها منه، ويلاحظ ما اذا كان الطفل يوزع كمية مناسبة من الصمغ السائل على الورقة." },
          { id: "motor-selfreg-15", itemNumber: "15", ageRange: "4-5 سنوات", behavior: "ينتقل بين الأنشطة بسهولة.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يقبل بسهولة الانتقال بين الأنشطة سواء في المنزل أو مركز التعلم المبكر، وهنا لابُد أن يكون التنقل بين الأنشطة دون توجيه من الكبار أو تذكير منهم." },
          { id: "motor-selfreg-16", itemNumber: "16", ageRange: "5-6 سنوات", behavior: "يُغير مستوى نشاطه بشكل يتناسب مع الموقف.", applicationMethod: "يلعب المقيم مع الطفل لعبة حركة وصنم، (يستعين بأنشودة من على الهاتف الذكي، ويشرح قواعد اللعبة للطفل بحيث يتوقع منه أن يتحرك عند تشغيلها ويتوقف تماما عن الحركة عند وقفها)." },
          { id: "motor-selfreg-17", itemNumber: "17", ageRange: "5-6 سنوات", behavior: "يستمر في النشاط لفترة زمنية طويلة.", applicationMethod: "يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يستمر في نشاط ما لفترة زمنية طويلة (مثال: حل الواجبات، القراءة،..الخ) - يستثنى من ذلك الأنشطة التي يكون فيها الطفل متلق سلبي مثل عروض الفيديو أو التلفاز." }
        ]
      },
      {
        id: "motor-independence",
        name: "الاستقلالية",
        skills: [
          { id: "motor-ind-1", itemNumber: "1", ageRange: "0-9 أشهر", behavior: "يصبح أكثر نشاطا عند مشاهدة زجاجة الرضاعة أو الثدي", applicationMethod: "الملاحظة المباشرة أو سؤال مقدم الرعاية نلاحظ ما اذا كان يصبح أكثر نشاطا عند مشاهدة زجاجة الرضاعة أو نستفسر من الأم عن ذلك ان كان يرضع رضاعة طبيعية." },
          { id: "motor-ind-2", itemNumber: "2", ageRange: "9-18 شهرًا", behavior: "يتعاون في ارتداء ملابسه", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يتعاون معه بمد يديه إلى الأكمام مثلا عندما يبدأ مقدم الرعاية بإلباسه ملابسه." },
          { id: "motor-ind-3", itemNumber: "3", ageRange: "18-36 شهرًا", behavior: "يُشير إلى حاجته للحمام خلال اليوم", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يعبر بالإشارة أو بكلمات بسيطة عن حاجته للحمام خلال اليوم (مثل: بيبي، نونو، مام)." },
          { id: "motor-ind-4", itemNumber: "4", ageRange: "18-36 شهرًا", behavior: "يُنظف أسنانه بمساعدة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يحرك فرشاة الأسنان داخل فمه في محاولة منه لتنظيف أسنانه بعد أن يكون شخص كبير قد أعدها له بوضع المعجون والماء عليها." },
          { id: "motor-ind-5", itemNumber: "5", ageRange: "18-36 شهرًا", behavior: "يغسل ويجفف يديه بمساعدة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يغسل يديه ويجففها حين يحمل له أحد المنشفة مع بعض المساعدة في موازنة درجة حرارة الماء مثلا." },
          { id: "motor-ind-6", itemNumber: "6", ageRange: "18-36 شهرًا", behavior: "يُنفذ مهام ارتداء الملابس البسيطة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما إذا كان الطفل يلبس ملابسه الفضفاضة بمساعدة بسيطة للتزرير." },
          { id: "motor-ind-7", itemNumber: "7", ageRange: "18-36 شهرًا", behavior: "يستخدم الحمام عند الحاجة مع بعض المساعدة في تنظيف نفسه", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يقضي حاجته في الحمام دون حوادث تذكر خلال النهار (من المقبول تقديم مساعدة بسيطة للطفل فقط لتنظيف نفسه)." },
          { id: "motor-ind-8", itemNumber: "8", ageRange: "3-4 سنوات", behavior: "يساعد في الأعمال المنزلية والصفية البسيطة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يساعد في الأعمال المنزلية البسيطة (أو الصفية ان كان التقييم يتم في بيئة التعلم المبكر) مثل توزيع الصحون والملاعق على مائدة الطعام أو رفعها ووضعها في المجلى." },
          { id: "motor-ind-9", itemNumber: "9", ageRange: "3-4 سنوات", behavior: "يُعد فرشاة الأسنان لينظف أسنانه", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يُعد فرشاة الأسنان بوضع المعجون ومن ثم الماء عليها لينظف أسنانه." },
          { id: "motor-ind-10", itemNumber: "10", ageRange: "3-4 سنوات", behavior: "يستخدم أدوات الطعام التي تتناسب مع عادات وتقاليد الأسرة (الملعقة، الشوكة، الكوب، الخ) في تناول الطعام بشكل مستقل", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية ما اذا كان الطفل يستخدم أدوات الطعام التي تتناسب مع عادات وتقاليد الأسرة في تناول الطعام بشكل مستقل (قد تكون عادات الأسرة تتطلب الأكل باليد، أو بالملعقة، أو الشوكة وينبغي أن يأكل مثلهم تماما)." },
          { id: "motor-ind-11", itemNumber: "11", ageRange: "3-4 سنوات", behavior: "يلبس ويخلع ملابسه بمساعدة بسيطة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يلبس ويخلع ملابسه الفضفاضة (لا الضيقة كالجينز مثلا) بمساعدة بسيطة للتزرير." },
          { id: "motor-ind-12", itemNumber: "12", ageRange: "3-4 سنوات", behavior: "يضع أدواته الشخصية في أماكن مناسبة", applicationMethod: "سؤال مقدم الرعاية. نستفسر من مقدم الرعاية عما اذا كان الطفل يقوم بتنظيم أدواته وألعابه في المكان المخصص لها قبل النوم كل في موقعه." },
          { id: "motor-ind-13", itemNumber: "13", ageRange: "4-5 سنوات", behavior: "يتبع قواعد الصحة والسلامة الأساسية (مثال: أثناء قطع الشارع أو التعرض لخطر الحريق)", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل ينتبه للمخاطر ويبتعد عن مصادرها (مثال: يتجنب الصوبة، وموقد الغاز، وينظر إلى اليمين واليسار قبل قطع الشارع)." },
          { id: "motor-ind-14", itemNumber: "14", ageRange: "4-5 سنوات", behavior: "ينفذ بعض المهمات الروتينية اليومية بمساعدة شخص كبير", applicationMethod: "سؤال مقدم الرعاية. يستفسر من مقدم الرعاية عما إذا كان الطفل يؤدي مهام العناية الذاتية مثل غسل اليدين وتناول الطعام والذهاب إلى الحمّام مع بعض المساعدة فقط." },
          { id: "motor-ind-15", itemNumber: "15", ageRange: "4-5 سنوات", behavior: "يرتدي ملابسه دون مساعدة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يرتدي ملابسه كاملة دون مساعدة نهائيا." },
          { id: "motor-ind-16", itemNumber: "16", ageRange: "5-6 سنوات", behavior: "ينجز مهمات يومية لوحده", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يؤدي مهام يومية لوحده بشكل كامل دون مساعدة مثل ترتيب سريره، أو ترتيب ألعابه." },
          { id: "motor-ind-17", itemNumber: "17", ageRange: "5-6 سنوات", behavior: "يؤدي مهمات العناية الذاتية دون مساعدة", applicationMethod: "سؤال مقدم الرعاية. يستفسر المقيم من مقدم الرعاية عما اذا كان الطفل يؤدي مهام العناية الذاتية كاملة دون مساعدة ويشمل ذلك غسل اليدين وتناول الطعام والذهاب إلى الحمّام بشكل مستقل وارتداء وخلع الملابس بشكل مستقل." }
        ]
      },
    ],
  },
];

// Dummy data for children - replace with actual data fetching
export let MOCK_CHILDREN_DATA: Child[] = [
  {
    id: "child1",
    name: "أحمد خالد",
    birthDate: "2022-05-15",
    enrollmentDate: "2024-01-10",
    specialistName: "د. سارة",
    avatarUrl: "https://placehold.co/100x100.png?text=أخ",
    gender: "male",
    sessionNotes: [
      {
        id: "note-1678886400000",
        date: format(parseISO("2024-03-15"), "yyyy-MM-dd"),
        goalDiscussed: "تحسين مهارة التواصل البصري أثناء اللعب",
        attendees: "الأم, الأخصائية سارة",
        notes: "تمت مناقشة أهمية التواصل البصري. الأم ذكرت أن أحمد يتجنب النظر المباشر أحيانًا. تم اقتراح ألعاب تتطلب النظر للوجه مثل \"أين أنفي؟\".",
        nextSteps: " تطبيق الألعاب المقترحة يوميًا لمدة 10 دقائق. متابعة في الجلسة القادمة."
      },
      {
        id: "note-1679886400000",
        date: format(parseISO("2024-06-20"), "yyyy-MM-dd"),
        goalDiscussed: "تشجيع المشاركة في الألعاب الجماعية البسيطة",
        attendees: "الأم, الأب, الأخصائية سارة",
        notes: "لوحظ أن أحمد يفضل اللعب بمفرده. تم استعراض استراتيجيات لدمجه تدريجيًا في ألعاب مع طفل آخر، مع التركيز على تبادل الأدوار البسيط.",
        nextSteps: "محاولة دمج أحمد مع ابن عمه في لعبة رمي الكرة. ملاحظة تفاعلاته."
      }
    ],
    caseStudy: {
        basicInfo: {
            childName: "أحمد خالد",
            birthDate: "2022-05-15",
            currentAge: "2 سنوات و 2 أشهر",
            gender: "male",
            guardianName: "والدة أحمد",
            guardianPhoneNumber: "**********",
            homeAddress: "الرياض، حي الملز",
            guardianRelationship: "أم",
            hasSiblings: "yes",
            siblingsInfo: "سارة (5 سنوات)"
        },
        pregnancyAndBirthInfo: {
            motherAgeAtPregnancy: "30",
            fullTermPregnancy: "yes",
            motherHealthIssuesDuringPregnancy: "no",
            deliveryType: "natural",
            childHealthIssuesAtBirth: "no"
        },
        reinforcerResponseInfo: {
            favoriteToys: "السيارات الصغيرة، المكعبات",
            enjoyableActivities: "اللعب في الخارج، الاستماع لأغاني الأطفال",
            favoriteFoods: "الموز، البسكويت",
            happinessExpression: "يبتسم ويصفق بيديه",
            motivationMethods: "إعطاؤه سيارة صغيرة، التشجيع اللفظي",
            smilesAtGuardian: "yes",
            communicatesNeeds: "yes"
        }
    }
  },
  { id: "child2", name: "ليلى فارس", birthDate: "2021-11-02", enrollmentDate: "2023-09-01", specialistName: "أ. عمر", avatarUrl: "https://placehold.co/100x100.png?text=لف", gender: "female", caseStudy: defaultCaseStudyData },
  { id: "child3", name: "يوسف علي", birthDate: "2023-01-20", enrollmentDate: "2024-03-05", specialistName: "د. سارة", avatarUrl: "https://placehold.co/100x100.png?text=يع", gender: "male", caseStudy: defaultCaseStudyData },
];

export let MOCK_ASSESSMENTS_DATA: Assessment[] = [
    {
        id: "assess1-child1",
        childId: "child1",
        assessmentDate: "2024-07-01", // Updated to a more recent date for better testing of alerts
        assessedSkills: [
            {
              skillId: "soc-rel-1",
              status: "yes",
              progressStatus: 'mastered',
              implementationStartDate: '2024-07-02',
              targetCompletionDate: '2024-07-09',
              progressNotes: 'أتقن الطفل النظر إلى مقدم الرعاية بسرعة.'
            },
            {
              skillId: "soc-rel-2",
              status: "yes"
            },
            {
              skillId: "soc-rel-3",
              status: "no",
              notes: "يحتاج المزيد من التدريب.",
              progressStatus: 'implemented',
              implementationStartDate: '2024-07-10',
              targetCompletionDate: '2024-08-17', // Date in the future
              progressNotes: 'بدأنا العمل على هذا الهدف، يظهر بعض التحسن.'
            },
            {
              skillId: "soc-int-1",
              status: "yes"
            },
             {
              skillId: "soc-int-4",
              status: "no",
              notes: "سيتم التركيز عليه قريباً.",
              progressStatus: 'pending'
            },
             {
              skillId: "exp-cog-1",
              status: "yes"
            },
            {
              skillId: "exp-cog-2",
              status: "yes"
            },
            {
              skillId: "exp-cog-3",
              status: "no"
            },
             // Add more skills from different dimensions for child1 for better analysis testing
            { skillId: "exp-crit-1", status: "yes" },
            { skillId: "exp-crit-2", status: "yes" },
            { skillId: "exp-crit-3", status: "yes" }, // Forms a baseline for exp-critical-thinking
            { skillId: "exp-crit-4", status: "no" },
            { skillId: "exp-crit-5", status: "no" },
            { skillId: "exp-crit-6", status: "no" }, // Forms a ceiling for exp-critical-thinking

            { skillId: "comm-comm-1", status: "yes"},
            { skillId: "comm-comm-2", status: "yes"},
            { skillId: "comm-comm-3", status: "unclear"},
            { skillId: "comm-comm-4", status: "no"},

            { skillId: "motor-gross-1", status: "yes" },
            { skillId: "motor-gross-2", status: "yes" },
            { skillId: "motor-gross-3", status: "yes" },
            { skillId: "motor-gross-4", status: "yes" },
            { skillId: "motor-gross-5", status: "yes" },
            { skillId: "motor-gross-6", status: "yes" },
            { skillId: "motor-gross-7", status: "yes" },
            { skillId: "motor-gross-8", status: "yes" },
            { skillId: "motor-gross-9", status: "yes" },
            { skillId: "motor-gross-10", status: "no", progressStatus: 'implemented', targetCompletionDate: '2024-07-20' }, // Overdue example
        ]
    }
];

export let MOCK_USERS_DATA: User[] = [
  { id: "user1", name: "مدير النظام", email: "<EMAIL>", role: "super_admin", avatarUrl: "https://placehold.co/40x40.png?text=م ن", specialization: "إدارة عامة" },
  { id: "user2", name: "أخصائي النطق", email: "<EMAIL>", role: "specialist", avatarUrl: "https://placehold.co/40x40.png?text=أ ن", specialization: "علاج النطق واللغة" },
  { id: "user3", name: "معلمة الروضة", email: "<EMAIL>", role: "educator", avatarUrl: "https://placehold.co/40x40.png?text=م ر", specialization: "تعليم الطفولة المبكرة" },
  { id: "user4", name: "مشاهد تجريبي", email: "<EMAIL>", role: "viewer", avatarUrl: "https://placehold.co/40x40.png?text=م ت", specialization: "مراقبة" },
  { id: "user5", name: "مدير وحدة التدخل المبكر", email: "<EMAIL>", role: "eiu_manager", avatarUrl: "https://placehold.co/40x40.png?text=موم", specialization: "إدارة برامج التدخل المبكر" },
  { id: "user6", name: "مدير حالة تجريبي", email: "<EMAIL>", role: "case_manager", avatarUrl: "https://placehold.co/40x40.png?text=م ح", specialization: "إدارة حالات" },
];

    