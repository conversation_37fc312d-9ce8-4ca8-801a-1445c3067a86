
"use client";

import React, { useState, useMemo, useRef } from 'react';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import type { Child, Assessment, CalculatedAge } from '@/lib/types';
import type { DimensionAnalysisData } from '@/app/children/[childId]/plan/page'; // Import the new type
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'; // For displaying structured analysis
import { useToast } from '@/hooks/use-toast';
import { generateComprehensiveReport, GenerateComprehensiveReportInput, GenerateComprehensiveReportOutput } from '@/ai/flows/generate-learning-plan'; // Corrected import path
import { calculateAge, formatDate } from '@/lib/utils';
import { Loader2, FileText, Printer } from 'lucide-react'; // Added FileText for icon & Printer
import { PORTAGE_CHECKLIST_DATA, SKILL_STATUS_OPTIONS } from '@/lib/constants';
import ReportChart from '@/components/report/ReportChart';


const formSchema = z.object({
  additionalFocus: z.string().optional().describe("أي معلومات إضافية أو توجيهات للمحلل ليأخذها في الاعتبار عند إنشاء التقرير."),
});

interface ComprehensiveReportGeneratorFormProps {
  child: Child;
  assessment?: Assessment; // Make assessment optional
  structuredAnalysisData?: DimensionAnalysisData[]; // New prop for structured analysis
}

const deriveAiInputs = (assessment: Assessment, childAgeInMonths: number, childName: string) => {
  let assessmentResultsSummary = `ملخص تقييم لـ ${childName} أُجري بتاريخ ${formatDate(assessment.assessmentDate)}. العمر عند التقييم: ${childAgeInMonths} شهرًا.\nالمهارات المقيمة وملاحظات رئيسية:\n`;
  let zpdSkills: string[] = [];

  assessment.assessedSkills.forEach(assessedSkill => {
    const skillDetail = PORTAGE_CHECKLIST_DATA.flatMap(dim => dim.subCategories.flatMap(sc => sc.skills)).find(s => s.id === assessedSkill.skillId);
    if (skillDetail) {
      const statusLabel = SKILL_STATUS_OPTIONS.find(s => s.value === assessedSkill.status)?.label || assessedSkill.status;
      assessmentResultsSummary += `- المهارة: "${skillDetail.behavior}" (الفئة العمرية: ${skillDetail.ageRange}). الحالة: ${statusLabel}.${assessedSkill.notes ? ` ملاحظة: ${assessedSkill.notes}` : ''}\n`;
      
      if (assessedSkill.status === 'no' || assessedSkill.status === 'unclear') {
        zpdSkills.push(`"${skillDetail.behavior}" (الفئة العمرية: ${skillDetail.ageRange})`);
      }
    }
  });

  if (zpdSkills.length === 0) {
    zpdSkills.push("لم يتم تحديد مهارات معينة بشكل واضح كأهداف مباشرة ضمن منطقة النمو القريبة من هذا التقييم، ولكن يمكن التركيز على المهارات التالية المناسبة للعمر والتي لم تُتقن بعد.");
  }
  
  const zoneOfProximalDevelopment = `المهارات التي يبدو الطفل مستعدًا لتعلمها أو تطويرها أكثر مع التوجيه والدعم (منطقة النمو القريبة) تشمل: ${zpdSkills.join('، ')}. يجب التركيز على الأنشطة التي تبني هذه المهارات أو المهارات التمهيدية لها.`;
  
  return { assessmentResults: assessmentResultsSummary, zoneOfProximalDevelopment };
};


export default function ComprehensiveReportGeneratorForm({ child, assessment, structuredAnalysisData }: ComprehensiveReportGeneratorFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [generatedReport, setGeneratedReport] = useState<GenerateComprehensiveReportOutput | null>(null);
  const { toast } = useToast();
  const reportPrintContentRef = useRef<HTMLDivElement>(null);


  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      additionalFocus: "",
    },
  });
  
  const childAge: CalculatedAge | null = useMemo(() => {
    if (!assessment) return null;
    return calculateAge(child.birthDate, assessment.assessmentDate);
  }, [child.birthDate, assessment]);

  const childAgeInMonths = childAge ? childAge.years * 12 + childAge.months : 0;

  const { assessmentResults, zoneOfProximalDevelopment } = useMemo(() => {
      if (!assessment) return { assessmentResults: "", zoneOfProximalDevelopment: "" };
      return deriveAiInputs(assessment, childAgeInMonths, child.name);
    }, [assessment, childAgeInMonths, child.name]
  );

  async function onSubmit(values: z.infer<typeof formSchema>) {
    if (!assessment) {
        toast({ title: "خطأ", description: "لا يوجد تقييم لتوليد التقرير بناءً عليه.", variant: "destructive"});
        return;
    }
    setIsLoading(true);
    setGeneratedReport(null);

    const aiInput: GenerateComprehensiveReportInput = {
      assessmentResults: assessmentResults,
      zoneOfProximalDevelopment,
      childName: child.name,
      ageInMonths: childAgeInMonths,
      additionalFocus: values.additionalFocus,
    };

    try {
      const result = await generateComprehensiveReport(aiInput);
      setGeneratedReport(result);
      toast({ title: "نجاح!", description: "تم إنشاء التقرير الشامل." });
    } catch (error) {
      console.error("Error generating comprehensive report:", error);
      toast({
        title: "خطأ",
        description: "فشل في إنشاء التقرير الشامل. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  const getReportTextToCopy = () => {
    if (!generatedReport || !assessment) return "";
    return `
التقرير الشامل لـ ${child.name}
تاريخ التقييم: ${formatDate(assessment.assessmentDate)}
العمر عند التقييم: ${childAge?.years} سنوات, ${childAge?.months} أشهر, ${childAge?.days} أيام

الملخص التنفيذي:
${generatedReport.executiveSummary}

نقاط القوة:
${generatedReport.strengths}

مجالات التطوير:
${generatedReport.areasForDevelopment}

أبرز نتائج تحليل البيانات:
${generatedReport.dataAnalysisHighlights}

توصيات عملية:
${generatedReport.actionableRecommendations}
    `.trim();
  };

  const handlePrintReport = () => {
    if (!reportPrintContentRef.current || !assessment) {
      toast({ title: "خطأ في الطباعة", description: "لم يتم العثور على محتوى التقرير للطباعة.", variant: "destructive" });
      return;
    }
    const title = `التقرير الشامل لـ ${child.name} - تاريخ ${formatDate(assessment.assessmentDate)}`;
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write('<html><head><title>' + title + '</title>');
      printWindow.document.write(`
        <style>
          body { font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif; direction: rtl; padding: 20px; margin: 0; }
          h1, h2, h3, h4, h5, h6 { color: #333; margin-bottom: 0.5em; margin-top: 1em; }
          p, pre { margin-bottom: 1em; line-height: 1.6; white-space: pre-wrap; font-family: inherit; }
          pre { background-color: #f9f9f9; border: 1px solid #eee; padding: 10px; border-radius: 4px; }
          .report-card-header { border-bottom: 1px solid #eee; margin-bottom: 15px; padding-bottom: 10px; }
          .report-section h3, .analysis-section h3 { font-weight: bold; color: hsl(var(--primary)); margin-bottom: 0.5em; }
          .analysis-section h4 { font-weight: medium; color: hsl(var(--accent)); margin-bottom: 0.25em; margin-top: 0.75em; }
          .print-chart-container { page-break-inside: avoid; margin-top: 20px; border: 1px solid #ccc; padding:10px; border-radius: 5px; }
          img { max-width: 100%; height: auto; } /* Ensure images (like charts if rendered as img) are responsive */
          ul { list-style-position: inside; padding-right: 1.5rem; } /* RTL list styling */
          .no-print { display: none !important; }
          @media print {
            body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            @page { margin: 20mm; }
            button, .no-print, .card-footer.no-print { display: none !important; }
             /* Ensure structured analysis and AI report sections are not broken across pages if possible */
            .report-section, .analysis-section > div { page-break-inside: avoid; }
            .print-chart-container { display: block; page-break-inside: avoid; }
          }
        </style>
      `);
      printWindow.document.write('</head><body dir="rtl">');
      printWindow.document.write('<h1>' + title + '</h1>');
      
      const reportContentClone = reportPrintContentRef.current.cloneNode(true) as HTMLElement;
      
      reportContentClone.querySelectorAll('.no-print, form, button, .card-footer.no-print').forEach(el => el.remove());


      printWindow.document.write(reportContentClone.innerHTML);
      printWindow.document.write('</body></html>');
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    } else {
      toast({ title: "خطأ", description: "يرجى السماح بالنوافذ المنبثقة لطباعة هذا المحتوى.", variant: "destructive"});
    }
  };


  return (
    <div className="space-y-6">
      {!assessment ? (
         <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>إنشاء تقرير شامل</CardTitle>
              <CardDescription>
                لم يتم العثور على تقييمات لـ {child.name}. يتطلب إنشاء التقرير بيانات تقييم.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center p-8">
                <p className="mb-4 text-lg">يرجى إكمال تقييم لـ {child.name} أولاً.</p>
                <Link href={`/children/${child.id}/assessment/new`}>
                  <Button>بدء تقييم جديد</Button>
                </Link>
              </div>
            </CardContent>
         </Card>
      ) : (
        <>
          {/* Structured Analysis Display Card */}
          {structuredAnalysisData && structuredAnalysisData.length > 0 && (
            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle>تحليل مفصل لآخر تقييم</CardTitle>
                <CardDescription>
                  تاريخ التقييم: {formatDate(assessment.assessmentDate)}. يوضح هذا القسم خط الأساس، الخط السقفي، ومنطقة التعلم المقترح لكل بُعد ومجال فرعي.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="multiple" className="w-full" defaultValue={structuredAnalysisData.map(d => d.dimensionName)}>
                  {structuredAnalysisData.map(dimensionData => (
                    <AccordionItem value={dimensionData.dimensionName} key={dimensionData.dimensionName}>
                      <AccordionTrigger className="text-xl font-semibold text-primary">{dimensionData.dimensionName}</AccordionTrigger>
                      <AccordionContent>
                        {dimensionData.subCategories.map(subCategoryData => (
                          <div key={subCategoryData.subCategoryName} className="mb-4 p-3 border rounded-md bg-muted/50">
                            <h4 className="text-lg font-medium text-accent mb-2">{subCategoryData.subCategoryName}</h4>
                            <p><strong>الخط القاعدي:</strong> {subCategoryData.baselineSkill ? `${subCategoryData.baselineSkill.itemNumber}. ${subCategoryData.baselineSkill.behavior}` : "لم يتم تحديده"}</p>
                            <p><strong>الخط السقفي:</strong> {subCategoryData.ceilingSkill ? `${subCategoryData.ceilingSkill.itemNumber}. ${subCategoryData.ceilingSkill.behavior}` : "لم يتم تحديده"}</p>
                            {subCategoryData.teachingRangeSkills.length > 0 ? (
                              <div>
                                <h5 className="font-semibold mt-2">منطقة التعلم (المهارات المقترحة للتركيز عليها):</h5>
                                <ul className="list-disc pr-5 mt-1 space-y-1 text-sm">
                                  {subCategoryData.teachingRangeSkills.map(skill => (
                                    <li key={skill.skillId}>{skill.itemNumber}. {skill.behavior} ({skill.ageRange})</li>
                                  ))}
                                </ul>
                              </div>
                            ) : (
                              <p className="mt-2 text-sm text-muted-foreground">لا توجد مهارات محددة في منطقة التعلم بناءً على هذا التحليل.</p>
                            )}
                          </div>
                        ))}
                        {dimensionData.subCategories.length === 0 && <p className="text-sm text-muted-foreground">لا توجد بيانات تحليل لهذه الفئة الفرعية ضمن هذا البعد في التقييم.</p>}
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          )}

          {/* AI Report Generation Form and Display */}
          <Card className="shadow-lg">
            <CardHeader>
              <CardTitle>إنشاء تقرير شامل مدعوم بالذكاء الاصطناعي</CardTitle>
              <CardDescription>
                بناءً على التقييم من تاريخ {formatDate(assessment.assessmentDate)}. يمكنك تحديد تركيز إضافي للتحليل إذا لزم الأمر.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <Label htmlFor="additionalFocus">تركيز/ملاحظات إضافية للتحليل (اختياري)</Label>
                  <Textarea
                    id="additionalFocus"
                    placeholder="مثال: يُظهر الطفل اهتمامًا قويًا بالحيوانات، أو، التركيز على مهارات التفاعل الاجتماعي بشكل خاص."
                    {...form.register("additionalFocus")}
                    className="mt-1"
                  />
                </div>
                
                <Button type="submit" disabled={isLoading} className="w-full sm:w-auto no-print">
                  {isLoading ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      جارٍ إنشاء التقرير...
                    </>
                  ) : (
                    <>
                      <FileText className="ml-2 h-4 w-4" />
                      إنشاء التقرير الشامل
                    </>
                  )}
                </Button>
              </form>

              {generatedReport && (
                <div ref={reportPrintContentRef} className="mt-6"> {/* Main div for printing */}
                  <div className="report-card-header no-print"> {/* This header won't be printed */}
                    <h2 className="text-2xl font-bold text-primary">التقرير الشامل لـ {child.name}</h2>
                    <p className="text-muted-foreground">
                      تم إنشاء هذا التقرير بواسطة الذكاء الاصطناعي بناءً على بيانات التقييم.
                    </p>
                  </div>
                  <div className="space-y-4 mt-4"> {/* Content to be printed */}
                    <div className="report-section">
                      <h3 className="text-lg font-semibold text-primary mb-1">الملخص التنفيذي</h3>
                      <pre className="whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border">{generatedReport.executiveSummary}</pre>
                    </div>
                    <div className="report-section">
                      <h3 className="text-lg font-semibold text-primary mb-1">نقاط القوة</h3>
                      <pre className="whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border">{generatedReport.strengths}</pre>
                    </div>
                    <div className="report-section">
                      <h3 className="text-lg font-semibold text-primary mb-1">مجالات التطوير</h3>
                      <pre className="whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border">{generatedReport.areasForDevelopment}</pre>
                    </div>
                    <div className="report-section">
                      <h3 className="text-lg font-semibold text-primary mb-1">أبرز نتائج تحليل البيانات</h3>
                      <pre className="whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border">{generatedReport.dataAnalysisHighlights}</pre>
                    </div>
                    <div className="report-section">
                      <h3 className="text-lg font-semibold text-primary mb-1">توصيات عملية</h3>
                      <pre className="whitespace-pre-wrap text-sm font-sans bg-background p-3 rounded-md border">{generatedReport.actionableRecommendations}</pre>
                    </div>
                  </div>
                  <div className="print-chart-container mt-6">
                    <ReportChart assessment={assessment} childName={child.name} />
                  </div>
                </div>
              )}
            </CardContent>
            {generatedReport && assessment && (
                <CardFooter className="gap-2 no-print">
                    <Button variant="outline" onClick={() => navigator.clipboard.writeText(getReportTextToCopy()).then(() => toast({description: "تم نسخ التقرير إلى الحافظة!"}))}>
                    نسخ التقرير
                    </Button>
                    <Button variant="outline" onClick={handlePrintReport} disabled={isLoading}>
                    <Printer className="ml-2 h-4 w-4" /> طباعة التقرير
                    </Button>
                </CardFooter>
            )}
          </Card>
        </>
      )}
    </div>
  );
}

// Ensure file is renamed to ComprehensiveReportGeneratorForm.tsx
// Old name: LearningPlanGeneratorForm.tsx

    