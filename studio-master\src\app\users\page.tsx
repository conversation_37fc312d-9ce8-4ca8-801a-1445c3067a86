
import UserListClient from '@/components/users/UserListClient';
import { MOCK_USERS_DATA, USER_ROLE_OPTIONS } from '@/lib/constants';

export default async function UsersPage() {
  // In a real app, fetch users data here or pass it to the client component
  // For now, we use mock data directly.
  return (
    <div className="container mx-auto py-8">
      <UserListClient 
        initialUsers={MOCK_USERS_DATA} 
        roleOptions={USER_ROLE_OPTIONS} 
      />
    </div>
  );
}
