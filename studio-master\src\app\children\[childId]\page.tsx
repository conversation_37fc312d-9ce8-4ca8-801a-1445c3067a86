
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import type { Child, Assessment } from '@/lib/types';
import ChildProfileClient from '@/components/children/ChildProfileClient';

export default async function ChildProfilePage({ params }: { params: { childId: string } }) {
  // Data is now managed through local storage via hooks in ChildProfileClient
  return <ChildProfileClient childId={params.childId} />;
}
