
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import type { Child, Assessment } from '@/lib/types';
import { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA } from '@/lib/constants';
import ChildProfileClient from '@/components/children/ChildProfileClient';

// Helper to find child by ID (replace with actual data fetching)
async function getChildById(id: string): Promise<Child | undefined> {
  // In a real app, fetch from a database
  return MOCK_CHILDREN_DATA.find(child => child.id === id);
}

// Helper to find assessments for a child
async function getAssessmentsByChildId(childId: string): Promise<Assessment[]> {
  return MOCK_ASSESSMENTS_DATA.filter(assessment => assessment.childId === childId);
}

export default async function ChildProfilePage({ params }: { params: { childId: string } }) {
  const child = await getChildById(params.childId);
  const assessments = await getAssessmentsByChildId(params.childId);
  const latestAssessment = assessments.sort((a,b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }
  
  return <ChildProfileClient initialChild={child} initialAssessments={assessments} initialLatestAssessment={latestAssessment} />;
}
