
"use client";

import React, { useState, useMemo, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { Child, PortageDimension, PortageSubCategory, PortageSkillItem, AssessedSkill, SkillStatus } from '@/lib/types';
import { SKILL_STATUS_OPTIONS } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2 } from 'lucide-react';
import { Label } from '@/components/ui/label';

const assessedSkillSchema = z.object({
  skillId: z.string(),
  status: z.enum(['yes', 'no', 'unclear']).optional(),
  notes: z.string().optional(),
});

const formSubmissionSchema = z.object({
  assessedSkills: z.array(assessedSkillSchema),
});

interface AssessmentFormProps {
  child: Child;
  portageChecklist: PortageDimension[];
  onSubmit: (data: { assessedSkills: AssessedSkill[] }) => Promise<void>;
  isSubmitting?: boolean;
  initialData?: AssessedSkill[]; // For pre-filling the form in edit mode
}

interface FormValues {
  [key: string]: SkillStatus | string | undefined; 
}

const ALL_DIMENSIONS_VALUE = "all-dimensions";
const ALL_SUBCATEGORIES_VALUE = "all-subcategories";
const ALL_AGERANGES_VALUE = "all-ageranges";

export default function AssessmentFormComponent({ child, portageChecklist, onSubmit, isSubmitting = false, initialData }: AssessmentFormProps) {
  
  const defaultFormValues: FormValues = useMemo(() => {
    if (!initialData) return {};
    const values: FormValues = {};
    initialData.forEach(skill => {
      values[`skill_status_${skill.skillId}`] = skill.status;
      if (skill.notes) {
        values[`skill_notes_${skill.skillId}`] = skill.notes;
      }
    });
    return values;
  }, [initialData]);

  const form = useForm<FormValues>({
    defaultValues: defaultFormValues,
  });

  useEffect(() => {
    // Reset form with new default values if initialData changes (e.g., when switching to edit mode)
    form.reset(defaultFormValues);
  }, [initialData, form, defaultFormValues]);


  const [selectedDimensionId, setSelectedDimensionId] = useState<string>(ALL_DIMENSIONS_VALUE);
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<string>(ALL_SUBCATEGORIES_VALUE);
  const [selectedAgeRange, setSelectedAgeRange] = useState<string>(ALL_AGERANGES_VALUE);

  const dimensionOptions = useMemo(() => {
    return [{ value: ALL_DIMENSIONS_VALUE, label: "الكل - البعد" }, ...portageChecklist.map(d => ({ value: d.id, label: d.name }))];
  }, [portageChecklist]);

  const subCategoryOptions = useMemo(() => {
    const baseOptions = [{ value: ALL_SUBCATEGORIES_VALUE, label: "الكل - المجال" }];
    if (selectedDimensionId === ALL_DIMENSIONS_VALUE) {
      const allSubCategories = new Set<{value: string, label: string}>();
       portageChecklist.forEach(dim => {
        dim.subCategories.forEach(sc => allSubCategories.add({value: sc.id, label: `${sc.name} (${dim.name})`}));
      });
      return [...baseOptions, ...Array.from(allSubCategories).sort((a,b) => a.label.localeCompare(b.label))];
    }
    const dimension = portageChecklist.find(d => d.id === selectedDimensionId);
    if (!dimension) return baseOptions;
    return [...baseOptions, ...dimension.subCategories.map(sc => ({ value: sc.id, label: sc.name }))];
  }, [selectedDimensionId, portageChecklist]);

  const ageRangeOptions = useMemo(() => {
    const allAgeRanges = new Set<string>();
    portageChecklist.forEach(dim => 
      dim.subCategories.forEach(sc => 
        sc.skills.forEach(skill => allAgeRanges.add(skill.ageRange))
      )
    );
    const sortedAgeRanges = Array.from(allAgeRanges).sort((a, b) => {
      const parseAge = (ageStr: string) => {
        if (ageStr.includes("يوم")) return 0; 
        if (ageStr.includes("شهرًا") || ageStr.includes("شهور")) {
            const monthsMatch = ageStr.match(/(\d+)-(\d+)\s*(شهرًا|شهور)/) || ageStr.match(/(\d+)\s*(شهرًا|شهور)/);
            if (monthsMatch) return parseInt(monthsMatch[1]) || 0;
            return 0;
        }
        if (ageStr.includes("سنوات")) {
            const yearsMatch = ageStr.match(/(\d+)-(\d+)\s*سنوات/) || ageStr.match(/(\d+)\s*سنوات/);
            if (yearsMatch) return (parseInt(yearsMatch[1]) || 0) * 12;
            return 0;
        }
        return Infinity; 
      };
      return parseAge(a) - parseAge(b);
    });
    return [{ value: ALL_AGERANGES_VALUE, label: "الكل - الفئة العمرية" }, ...sortedAgeRanges.map(age => ({ value: age, label: age }))];
  }, [portageChecklist]);

  const handleDimensionChange = (value: string) => {
    setSelectedDimensionId(value);
    setSelectedSubCategoryId(ALL_SUBCATEGORIES_VALUE); // Reset sub-category when dimension changes
  };

  const displayPortageChecklist = useMemo(() => {
    return portageChecklist
      .filter(dim => selectedDimensionId === ALL_DIMENSIONS_VALUE || dim.id === selectedDimensionId)
      .map(dim => ({
        ...dim,
        subCategories: dim.subCategories
          .filter(sc => selectedSubCategoryId === ALL_SUBCATEGORIES_VALUE || sc.id === selectedSubCategoryId || (selectedDimensionId === ALL_DIMENSIONS_VALUE && selectedSubCategoryId === sc.id) ) 
          .map(sc => ({
            ...sc,
            skills: sc.skills.filter(skill => selectedAgeRange === ALL_AGERANGES_VALUE || skill.ageRange === selectedAgeRange)
          }))
          .filter(sc => sc.skills.length > 0) 
      }))
      .filter(dim => dim.subCategories.length > 0);
  }, [portageChecklist, selectedDimensionId, selectedSubCategoryId, selectedAgeRange]);

  const handleFormSubmit = async (data: FormValues) => {
    const assessedSkills: AssessedSkill[] = [];
    portageChecklist.forEach(dimension => {
      dimension.subCategories.forEach(subCategory => {
        subCategory.skills.forEach(skill => {
          const statusKey = `skill_status_${skill.id}`;
          const notesKey = `skill_notes_${skill.id}`;
          
          const status = data[statusKey] as SkillStatus | undefined;
          const notes = data[notesKey] as string | undefined;

          if (status) { 
            assessedSkills.push({
              skillId: skill.id,
              status: status,
              notes: notes || undefined,
              // Preserve existing progress tracking fields if editing, or initialize if new
              progressStatus: initialData?.find(s => s.skillId === skill.id)?.progressStatus || 'pending',
              implementationStartDate: initialData?.find(s => s.skillId === skill.id)?.implementationStartDate,
              targetCompletionDate: initialData?.find(s => s.skillId === skill.id)?.targetCompletionDate,
              progressNotes: initialData?.find(s => s.skillId === skill.id)?.progressNotes,
            });
          }
        });
      });
    });
    await onSubmit({ assessedSkills });
  };
  
  const getAccordionDefaultValue = () => {
    if (selectedDimensionId !== ALL_DIMENSIONS_VALUE && displayPortageChecklist.length > 0 && displayPortageChecklist.find(d => d.id === selectedDimensionId)) {
      return [selectedDimensionId];
    }
    if (displayPortageChecklist.length === 1) {
        return [displayPortageChecklist[0].id];
    }
    // If initialData is present (edit mode) and filters are "all", expand all dimensions that have data.
    if (initialData && initialData.length > 0 && 
        selectedDimensionId === ALL_DIMENSIONS_VALUE &&
        selectedSubCategoryId === ALL_SUBCATEGORIES_VALUE &&
        selectedAgeRange === ALL_AGERANGES_VALUE) {
      const dimensionsWithData = new Set<string>();
      initialData.forEach(assessedSkill => {
        const skillDetails = portageChecklist
          .flatMap(dim => dim.subCategories.flatMap(sc => sc.skills.map(s => ({ ...s, dimensionId: dim.id }))))
          .find(s => s.id === assessedSkill.skillId);
        if (skillDetails) {
          dimensionsWithData.add(skillDetails.dimensionId);
        }
      });
      return Array.from(dimensionsWithData);
    }
    return []; 
  }

  return (
    <Form {...form}>
      <div className="mb-6 p-4 border rounded-lg shadow bg-card">
        <h3 className="text-lg font-semibold mb-3 text-primary">فلاتر قائمة التقييم</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="dimensionFilter" className="mb-1 block text-sm font-medium">البعد:</Label>
            <Select value={selectedDimensionId} onValueChange={handleDimensionChange}>
              <SelectTrigger id="dimensionFilter">
                <SelectValue placeholder="اختر البعد" />
              </SelectTrigger>
              <SelectContent>
                {dimensionOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="subCategoryFilter" className="mb-1 block text-sm font-medium">المجال (الفئة الفرعية):</Label>
            <Select value={selectedSubCategoryId} onValueChange={setSelectedSubCategoryId}>
              <SelectTrigger id="subCategoryFilter">
                <SelectValue placeholder="اختر المجال" />
              </SelectTrigger>
              <SelectContent>
                {subCategoryOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="ageRangeFilter" className="mb-1 block text-sm font-medium">الفئة العمرية:</Label>
            <Select value={selectedAgeRange} onValueChange={setSelectedAgeRange}>
              <SelectTrigger id="ageRangeFilter">
                <SelectValue placeholder="اختر الفئة العمرية" />
              </SelectTrigger>
              <SelectContent>
                {ageRangeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-8">
        {displayPortageChecklist.length > 0 ? (
          <Accordion 
            type="multiple" 
            className="w-full" 
            key={selectedDimensionId || 'all-dims'} 
            defaultValue={getAccordionDefaultValue()}
          >
            {displayPortageChecklist.map((dimension) => (
              <AccordionItem value={dimension.id} key={dimension.id}>
                <AccordionTrigger className="text-xl font-semibold text-primary hover:text-primary/90 py-3 px-4 rounded-md hover:bg-muted/50 transition-colors">
                  {dimension.name}
                </AccordionTrigger>
                <AccordionContent className="pt-1 pb-4 pr-4">
                  {dimension.subCategories.map((subCategory) => (
                    <div key={subCategory.id} className="mb-6 last:mb-0">
                      <h4 className="text-lg font-medium text-accent mb-3 mt-2 p-2 bg-accent/10 rounded-md">{subCategory.name}</h4>
                      <div className="space-y-4">
                        {subCategory.skills.map((skill) => (
                          <Card key={skill.id} className="overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                            <CardHeader className="p-4 bg-card/80">
                              <CardTitle className="text-base font-semibold">
                                {skill.itemNumber}. {skill.behavior}
                                <span className="block text-xs text-muted-foreground font-normal mt-1">الفئة العمرية: {skill.ageRange}</span>
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 space-y-3">
                              <p className="text-sm text-foreground/80"><strong className="font-medium">طريقة التطبيق:</strong> {skill.applicationMethod}</p>
                              {skill.tools && <p className="text-sm text-foreground/80"><strong className="font-medium">الأدوات:</strong> {skill.tools}</p>}

                              <FormField
                                control={form.control}
                                name={`skill_status_${skill.id}`}
                                render={({ field }) => (
                                  <FormItem className="space-y-2">
                                    <FormLabel className="text-sm font-medium">الحالة:</FormLabel>
                                    <FormControl>
                                      <RadioGroup
                                        onValueChange={field.onChange}
                                        value={field.value || ""} // Ensure value is not undefined for RadioGroup
                                        className="flex flex-col sm:flex-row gap-2 sm:gap-4"
                                      >
                                        {SKILL_STATUS_OPTIONS.map((option) => (
                                          <FormItem key={option.value} className="flex items-center space-x-2 space-x-reverse">
                                            <FormControl>
                                              <RadioGroupItem value={option.value} id={`${skill.id}-${option.value}`} />
                                            </FormControl>
                                            <FormLabel htmlFor={`${skill.id}-${option.value}`} className="font-normal cursor-pointer">
                                              {option.symbol} {option.label}
                                            </FormLabel>
                                          </FormItem>
                                        ))}
                                      </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`skill_notes_${skill.id}`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel htmlFor={`notes-${skill.id}`} className="text-sm font-medium">ملاحظات:</FormLabel>
                                    <FormControl>
                                      <Textarea
                                        id={`notes-${skill.id}`}
                                        placeholder="أضف ملاحظاتك هنا..."
                                        className="resize-y min-h-[60px]"
                                        {...field}
                                        value={field.value || ""} // Ensure value is not undefined
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ))}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        ) : (
          <div className="text-center py-12">
            <p className="text-lg text-muted-foreground">لا توجد مهارات تطابق معايير الفلترة المحددة.</p>
          </div>
        )}
        <div className="flex justify-start pt-6">
          <Button type="submit" disabled={isSubmitting} size="lg">
            {isSubmitting ? (
              <>
                <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                {initialData ? "جارٍ حفظ التعديلات..." : "جارٍ حفظ التقييم..."}
              </>
            ) : (
              initialData ? "حفظ التعديلات" : "حفظ التقييم"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
