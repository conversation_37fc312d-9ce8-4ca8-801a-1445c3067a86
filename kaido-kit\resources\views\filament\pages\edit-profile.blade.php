<x-filament-panels::page>
    @if (<PERSON><PERSON>\Fortify\Features::canUpdateProfileInformation())
        @livewire(<PERSON><PERSON>\Jetstream\Http\Livewire\UpdateProfileInformationForm::class)

        <x-section-border/>
    @endif

    @if (<PERSON><PERSON>\Fortify\Features::enabled(<PERSON><PERSON>\Fortify\Features::updatePasswords()))
        <div class="mt-10 sm:mt-0">
            @livewire(<PERSON><PERSON>\Jetstream\Http\Livewire\UpdatePasswordForm::class)
        </div>

        <x-section-border/>
    @endif

    @if (<PERSON>vel\Fortify\Features::canManageTwoFactorAuthentication())
        <div class="mt-10 sm:mt-0">
            @livewire(<PERSON><PERSON>\Jetstream\Http\Livewire\TwoFactorAuthenticationForm::class)
        </div>

        <x-section-border/>
    @endif

    <div class="mt-10 sm:mt-0">
        @livewire(<PERSON><PERSON>\Jetstream\Http\Livewire\LogoutOtherBrowserSessionsForm::class)
    </div>

    @if (<PERSON><PERSON>\Jetstream\Jetstream::hasAccountDeletionFeatures())
        <x-section-border/>

        <div class="mt-10 sm:mt-0">
            @livewire(Laravel\Jetstream\Http\Livewire\DeleteUserForm::class)
        </div>
    @endif
</x-filament-panels::page>
