{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { differenceInYears, differenceInMonths, differenceInDays, addYears, addMonths, parseISO, isValid, format } from 'date-fns';\nimport { arSA } from 'date-fns/locale'; // Import Arabic locale\nimport type { CalculatedAge } from './types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function calculateAge(birthDateString: string, assessmentDateString?: string): CalculatedAge {\n  const birthDate = parseISO(birthDateString);\n  const assessmentDate = assessmentDateString ? parseISO(assessmentDateString) : new Date();\n\n  if (!isValid(birthDate) || !isValid(assessmentDate)) {\n    // console.error(\"Invalid date provided for age calculation\", { birthDateString, assessmentDateString });\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  if (birthDate > assessmentDate) {\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  let tempAgeDate = new Date(birthDate);\n  \n  const years = differenceInYears(assessmentDate, tempAgeDate);\n  tempAgeDate = addYears(tempAgeDate, years);\n  \n  const months = differenceInMonths(assessmentDate, tempAgeDate);\n  tempAgeDate = addMonths(tempAgeDate, months);\n  \n  const days = differenceInDays(assessmentDate, tempAgeDate);\n\n  return { years, months, days };\n}\n\nexport function formatDate(dateString: string | Date, dateFormat: string = 'PPP'): string {\n  try {\n    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;\n    if (!isValid(date)) return \"تاريخ غير صالح\";\n    return format(date, dateFormat, { locale: arSA }); // Use Arabic locale\n  } catch (error) {\n    return \"تاريخ غير صالح\";\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,6PAAwC,uBAAuB;;;;;AAGxD,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,eAAuB,EAAE,oBAA6B;IACjF,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;IAC3B,MAAM,iBAAiB,uBAAuB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,wBAAwB,IAAI;IAEnF,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,cAAc,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QACnD,yGAAyG;QACzG,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,YAAY,gBAAgB;QAC9B,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,cAAc,IAAI,KAAK;IAE3B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;IAChD,cAAc,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAEpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB;IAClD,cAAc,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAErC,MAAM,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;IAE9C,OAAO;QAAE;QAAO;QAAQ;IAAK;AAC/B;AAEO,SAAS,WAAW,UAAyB,EAAE,aAAqB,KAAK;IAC9E,IAAI;QACF,MAAM,OAAO,OAAO,eAAe,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QACrE,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAC3B,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,YAAY;YAAE,QAAQ,kJAAA,CAAA,OAAI;QAAC,IAAI,oBAAoB;IACzE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/AgeDistributionChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/dashboard/AgeDistributionChart.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/AgeDistributionChart.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqT,GAClV,mFACA", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/AgeDistributionChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/dashboard/AgeDistributionChart.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/AgeDistributionChart.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/ProgressOverviewChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/dashboard/ProgressOverviewChart.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/ProgressOverviewChart.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/ProgressOverviewChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/dashboard/ProgressOverviewChart.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/ProgressOverviewChart.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/GenderDistributionChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/dashboard/GenderDistributionChart.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/GenderDistributionChart.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/GenderDistributionChart.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/dashboard/GenderDistributionChart.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/dashboard/GenderDistributionChart.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/page.tsx"], "sourcesContent": ["\nimport Link from 'next/link';\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';\nimport { Users, FilePlus2, Activity, BarChart3 as BarChartIcon, UserPlus, UserMinus, TrendingUp, CheckCircle, UserRoundPlus, AlertTriangle, CalendarClock, Info } from 'lucide-react'; // Renamed BarChart3 to BarChartIcon to avoid conflict\nimport Image from 'next/image';\nimport { APP_NAME, MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, AGE_DISTRIBUTION_GROUPS } from '@/lib/constants';\nimport type { Child, Assessment } from '@/lib/types';\nimport { calculateAge, formatDate } from '@/lib/utils';\nimport { differenceInDays, parseISO, isWithinInterval, subDays, addMonths, isPast, isValid } from 'date-fns';\nimport type { ChartConfig } from \"@/components/ui/chart\"; // Keep for ageChartConfig type\nimport AgeDistributionChart from '@/components/dashboard/AgeDistributionChart';\nimport ProgressOverviewChart from '@/components/dashboard/ProgressOverviewChart';\nimport GenderDistributionChart from '@/components/dashboard/GenderDistributionChart'; // Added import\n\n\n// Helper to get the latest assessment for a child\nconst getLatestAssessmentForChild = (childId: string, assessments: Assessment[]): Assessment | undefined => {\n  return assessments\n    .filter(a => a.childId === childId)\n    .sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];\n};\n\nconst getChildNameById = (childId: string): string | undefined => {\n  const child = MOCK_CHILDREN_DATA.find(c => c.id === childId);\n  return child?.name;\n}\n\n\nexport default async function DashboardPage() {\n  // Calculate analytics data\n  const totalChildren = MOCK_CHILDREN_DATA.length;\n  const maleChildren = MOCK_CHILDREN_DATA.filter(c => c.gender === 'male').length;\n  const femaleChildren = MOCK_CHILDREN_DATA.filter(c => c.gender === 'female').length;\n\n  const today = new Date();\n  const newChildrenCount = MOCK_CHILDREN_DATA.filter(c => {\n    try {\n      const enrollmentDate = parseISO(c.enrollmentDate);\n      return differenceInDays(today, enrollmentDate) <= 30;\n    } catch (e) {\n      return false;\n    }\n  }).length;\n\n  const servicesEndedCount = MOCK_CHILDREN_DATA.filter(c => {\n    const age = calculateAge(c.birthDate);\n    return age.years >= 6;\n  }).length;\n\n  const ageDistributionData = AGE_DISTRIBUTION_GROUPS.map(group => ({ ...group, count: 0, fill: `hsl(var(--chart-${(AGE_DISTRIBUTION_GROUPS.indexOf(group) % 5) + 1}))` }));\n  MOCK_CHILDREN_DATA.forEach(child => {\n    const age = calculateAge(child.birthDate);\n    const ageInMonths = age.years * 12 + age.months;\n    const groupFound = ageDistributionData.find(group => ageInMonths >= group.minMonths && ageInMonths < group.maxMonths);\n    if (groupFound) {\n      groupFound.count++;\n    }\n  });\n\n  const ageChartConfig = {\n    count: {\n      label: \"عدد الأطفال\",\n    },\n    // Dynamically add labels for each age group to the config for the tooltip\n    ...ageDistributionData.reduce((acc, group) => {\n      acc[group.label] = { label: group.label, color: group.fill };\n      return acc;\n    }, {} as Record<string, { label: string; color: string }>)\n  } satisfies ChartConfig;\n\n\n  let childrenWithMasteredGoals = 0;\n  let childrenWithImplementedGoalsOnly = 0;\n  let childrenWithPendingOrNoTrackableGoals = 0;\n\n  MOCK_CHILDREN_DATA.forEach(child => {\n    const latestAssessment = getLatestAssessmentForChild(child.id, MOCK_ASSESSMENTS_DATA);\n    if (latestAssessment) {\n      const hasMastered = latestAssessment.assessedSkills.some(s => s.progressStatus === 'mastered');\n      const hasImplemented = latestAssessment.assessedSkills.some(s => s.progressStatus === 'implemented');\n      const hasTrackableGoals = latestAssessment.assessedSkills.some(s => s.status === 'no' || s.status === 'unclear');\n\n      if (hasMastered) {\n        childrenWithMasteredGoals++;\n      } else if (hasImplemented) {\n        childrenWithImplementedGoalsOnly++;\n      } else if (hasTrackableGoals) {\n        childrenWithPendingOrNoTrackableGoals++;\n      } else {\n         childrenWithPendingOrNoTrackableGoals++;\n      }\n    } else {\n      childrenWithPendingOrNoTrackableGoals++;\n    }\n  });\n\n  // Recent Activity Data\n  const sevenDaysAgo = subDays(today, 7);\n  const recentlyAddedChildren = MOCK_CHILDREN_DATA.filter(child => {\n    try {\n      const enrollmentDate = parseISO(child.enrollmentDate);\n      return isWithinInterval(enrollmentDate, { start: sevenDaysAgo, end: today });\n    } catch (e) { return false; }\n  }).sort((a,b) => parseISO(b.enrollmentDate).getTime() - parseISO(a.enrollmentDate).getTime());\n\n  const recentAssessments = MOCK_ASSESSMENTS_DATA.filter(assessment => {\n     try {\n      const assessmentDate = parseISO(assessment.assessmentDate);\n      return isWithinInterval(assessmentDate, { start: sevenDaysAgo, end: today });\n    } catch (e) { return false; }\n  }).sort((a,b) => parseISO(b.assessmentDate).getTime() - parseISO(a.assessmentDate).getTime());\n\n  // --- Logic for Alerts and Notifications Card ---\n  const overdueReassessmentChildren: Child[] = [];\n  const dueSoonReassessmentChildren: { child: Child; dueDate: string }[] = [];\n  const serviceCompletionChildren: Child[] = [];\n  const initialAssessmentNeededChildren: Child[] = [];\n\n  MOCK_CHILDREN_DATA.forEach(child => {\n    const age = calculateAge(child.birthDate, today.toISOString());\n    let isServiceCompleted = false;\n    if (age.years >= 6) {\n      serviceCompletionChildren.push(child);\n      isServiceCompleted = true;\n    }\n\n    // Only process other alerts if service is not completed\n    if (!isServiceCompleted) {\n      const latestAssessment = getLatestAssessmentForChild(child.id, MOCK_ASSESSMENTS_DATA);\n      if (latestAssessment) {\n        try {\n          const lastAssessmentDate = parseISO(latestAssessment.assessmentDate);\n          if (isValid(lastAssessmentDate)) {\n            const reassessmentDueDate = addMonths(lastAssessmentDate, 4);\n            const formattedDueDate = formatDate(reassessmentDueDate.toISOString());\n\n            if (isPast(reassessmentDueDate)) {\n              overdueReassessmentChildren.push(child);\n            } else {\n              const daysUntilReassessment = differenceInDays(reassessmentDueDate, today);\n              if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {\n                dueSoonReassessmentChildren.push({ child, dueDate: formattedDueDate });\n              }\n            }\n          }\n        } catch (error) {\n          // console.error(\"Error processing assessment date for alerts:\", error);\n        }\n      } else {\n        initialAssessmentNeededChildren.push(child);\n      }\n    }\n  });\n\n\n  return (\n    <div className=\"container mx-auto py-8\">\n      <div className=\"mb-12 text-center\">\n        <h1 className=\"text-4xl font-bold tracking-tight text-primary sm:text-5xl\">\n          مرحباً بكم في {APP_NAME}\n        </h1>\n        <p className=\"mt-4 text-lg leading-8 text-foreground/80\">\n          أداتك الشاملة لتقييم وتنمية الطفولة المبكرة.\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3\">\n        <Card className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Users className=\"h-6 w-6 text-accent\" />\n              إدارة الأطفال\n            </CardTitle>\n            <CardDescription>\n              الوصول إلى ملفات تعريف الأطفال وإدارتها، وتتبع التقدم، وعرض سجل التقييمات.\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <Image \n              src=\"https://placehold.co/600x400.png\" \n              alt=\"أطفال يلعبون\"\n              width={600}\n              height={400}\n              className=\"rounded-md mb-4\"\n              data-ai-hint=\"children playing\"\n            />\n            <Link href=\"/children\" passHref>\n              <Button className=\"w-full\">\n                الانتقال إلى ملفات تعريف الأطفال\n              </Button>\n            </Link>\n          </CardContent>\n        </Card>\n\n        {/* Alerts and Notifications Card */}\n        <Card className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <CalendarClock className=\"h-6 w-6 text-accent\" />\n              التنبيهات والإشعارات\n            </CardTitle>\n            <CardDescription>\n              متابعة التقييمات الهامة وحالة الأطفال.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-3 text-sm max-h-96 overflow-y-auto\">\n            <Image\n              src=\"https://placehold.co/600x200.png\" \n              alt=\"تنبيهات\"\n              width={600}\n              height={200}\n              className=\"rounded-md mb-3\"\n              data-ai-hint=\"notification bell\"\n            />\n\n            {serviceCompletionChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-yellow-600 dark:text-yellow-400 mb-1 flex items-center gap-1\">\n                  <Info className=\"h-4 w-4\" />\n                  اكتمال الخدمة (محتمل):\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {serviceCompletionChildren.map(c => (\n                    <li key={`completed-${c.id}`}>\n                      <Link href={`/children/${c.id}`} className=\"text-primary hover:underline\">\n                        {c.name}\n                      </Link> - بلغ 6 سنوات أو أكثر.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {overdueReassessmentChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-destructive mb-1 flex items-center gap-1\">\n                  <AlertTriangle className=\"h-4 w-4\" />\n                  إعادة تقييم مطلوبة فورًا:\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {overdueReassessmentChildren.map(c => (\n                    <li key={`overdue-${c.id}`}>\n                      <Link href={`/children/${c.id}/assessment`} className=\"text-primary hover:underline\">\n                        {c.name}\n                      </Link> - آخر تقييم منذ أكثر من 4 أشهر.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {dueSoonReassessmentChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-orange-600 dark:text-orange-400 mb-1 flex items-center gap-1\">\n                  <CalendarClock className=\"h-4 w-4\" />\n                  إعادة تقييم قريبة:\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {dueSoonReassessmentChildren.map(item => (\n                    <li key={`due-soon-${item.child.id}`}>\n                      <Link href={`/children/${item.child.id}/assessment`} className=\"text-primary hover:underline\">\n                        {item.child.name}\n                      </Link> - الموعد المتوقع: {item.dueDate}.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n\n            {initialAssessmentNeededChildren.length > 0 && (\n              <div className=\"p-2 border-t\">\n                <h4 className=\"font-semibold text-blue-600 dark:text-blue-400 mb-1 flex items-center gap-1\">\n                  <FilePlus2 className=\"h-4 w-4\" />\n                  تقييم أولي مطلوب:\n                </h4>\n                <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                  {initialAssessmentNeededChildren.map(c => (\n                    <li key={`initial-${c.id}`}>\n                      <Link href={`/children/${c.id}/assessment/new`} className=\"text-primary hover:underline\">\n                        {c.name}\n                      </Link> - لا يوجد تقييم مسجل.\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n            \n            {overdueReassessmentChildren.length === 0 &&\n             dueSoonReassessmentChildren.length === 0 &&\n             initialAssessmentNeededChildren.length === 0 &&\n             serviceCompletionChildren.length === 0 && (\n              <p className=\"text-muted-foreground text-center py-4\">لا توجد تنبيهات أو إشعارات حاليًا.</p>\n            )}\n          </CardContent>\n        </Card>\n\n        <Card className=\"shadow-lg hover:shadow-xl transition-shadow duration-300\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Activity className=\"h-6 w-6 text-accent\" />\n              النشاط الأخير (آخر 7 أيام)\n            </CardTitle>\n            <CardDescription>\n              نظرة عامة على أحدث الإضافات والتقييمات.\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4 max-h-96 overflow-y-auto\">\n             <Image \n              src=\"https://placehold.co/600x200.png\" \n              alt=\"تقويم أو موجز النشاط\"\n              width={600}\n              height={200}\n              className=\"rounded-md mb-2\"\n              data-ai-hint=\"activity feed\"\n            />\n            {recentlyAddedChildren.length === 0 && recentAssessments.length === 0 ? (\n              <p className=\"text-muted-foreground text-center\">لا يوجد نشاط حديث لعرضه.</p>\n            ) : (\n              <>\n                {recentlyAddedChildren.length > 0 && (\n                  <div className=\"p-2 border-t\">\n                    <h4 className=\"font-semibold text-sm mb-1 flex items-center gap-1\"><UserRoundPlus className=\"h-4 w-4 text-green-500\" /> أطفال أضيفوا حديثًا:</h4>\n                    <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                      {recentlyAddedChildren.map(child => (\n                        <li key={child.id}>\n                          <Link href={`/children/${child.id}`} className=\"text-primary hover:underline\">\n                            {child.name}\n                          </Link> - أضيف في: {formatDate(child.enrollmentDate)}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                )}\n                {recentAssessments.length > 0 && (\n                  <div className=\"p-2 border-t\">\n                    <h4 className=\"font-semibold text-sm mb-1 flex items-center gap-1\"><CheckCircle className=\"h-4 w-4 text-blue-500\" /> تقييمات مكتملة حديثًا:</h4>\n                    <ul className=\"list-disc list-inside space-y-1 pr-4 text-xs\">\n                      {recentAssessments.map(assessment => {\n                        const childName = getChildNameById(assessment.childId);\n                        return (\n                          <li key={assessment.id}>\n                            <Link href={`/children/${assessment.childId}/assessment/${assessment.id}`} className=\"text-primary hover:underline\">\n                              تقييم لـ {childName || 'طفل غير معروف'}\n                            </Link> - بتاريخ: {formatDate(assessment.assessmentDate)}\n                          </li>\n                        );\n                      })}\n                    </ul>\n                  </div>\n                )}\n              </>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Comprehensive Data Analysis Card */}\n      <Card className=\"mt-8 shadow-lg hover:shadow-xl transition-shadow duration-300 col-span-1 md:col-span-2 lg:col-span-3\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <BarChartIcon className=\"h-6 w-6 text-accent\" />\n            تحليل بيانات شامل\n          </CardTitle>\n          <CardDescription>\n            نظرة عامة على إحصائيات الأطفال والتقدم المحرز.\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6 text-sm\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h4 className=\"font-semibold mb-2 text-primary flex items-center gap-1\"><Users className=\"h-5 w-5\" />نظرة عامة على الأطفال:</h4>\n              <ul className=\"list-disc list-inside space-y-1 pr-5\">\n                <li>إجمالي الأطفال المسجلين: {totalChildren}</li>\n                <li>عدد الذكور: {maleChildren}</li>\n                <li>عدد الإناث: {femaleChildren}</li>\n                <li><UserPlus className=\"inline h-4 w-4 mr-1 text-green-500\" /> أطفال جدد (آخر 30 يومًا): {newChildrenCount}</li>\n                <li><UserMinus className=\"inline h-4 w-4 mr-1 text-red-500\" /> أطفال انتهت خدماتهم (6+ سنوات): {servicesEndedCount}</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2 text-primary flex items-center gap-1\"><TrendingUp className=\"h-5 w-5\" />نظرة عامة على التقدم:</h4>\n              <p className=\"text-xs text-muted-foreground mb-1\">(بناءً على آخر تقييم لكل طفل)</p>\n              <ul className=\"list-disc list-inside space-y-1 pr-5\">\n                <li>أطفال لديهم أهداف أتقنوها: {childrenWithMasteredGoals}</li>\n                <li>أطفال لديهم أهداف قيد التنفيذ (فقط): {childrenWithImplementedGoalsOnly}</li>\n                <li>أطفال لديهم أهداف معلقة أو بدون تقييم: {childrenWithPendingOrNoTrackableGoals}</li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 pt-4\">\n            <div className=\"lg:col-span-1\">\n              <h4 className=\"font-semibold mb-3 text-primary text-center lg:text-right\">توزيع الأعمار:</h4>\n              <AgeDistributionChart data={ageDistributionData} config={ageChartConfig} />\n            </div>\n            <div className=\"lg:col-span-1\">\n              <h4 className=\"font-semibold mb-3 text-primary text-center lg:text-right\">نظرة عامة على تقدم الأهداف:</h4>\n              <ProgressOverviewChart \n                mastered={childrenWithMasteredGoals} \n                implemented={childrenWithImplementedGoalsOnly} \n                pending={childrenWithPendingOrNoTrackableGoals} \n              />\n            </div>\n             <div className=\"lg:col-span-1\">\n              <h4 className=\"font-semibold mb-3 text-primary text-center lg:text-right\">توزيع الأطفال حسب الجنس:</h4>\n              <GenderDistributionChart\n                maleCount={maleChildren}\n                femaleCount={femaleChildren}\n              />\n            </div>\n          </div>\n\n        </CardContent>\n      </Card>\n\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA,oVAAuL,sDAAsD;AAA7O;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA,mRAAsF,eAAe;;;;;;;;;;;;;AAGrG,kDAAkD;AAClD,MAAM,8BAA8B,CAAC,SAAiB;IACpD,OAAO,YACJ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAC1B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,GAAG,CAAC,EAAE;AACnG;AAEA,MAAM,mBAAmB,CAAC;IACxB,MAAM,QAAQ,uHAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACpD,OAAO,OAAO;AAChB;AAGe,eAAe;IAC5B,2BAA2B;IAC3B,MAAM,gBAAgB,uHAAA,CAAA,qBAAkB,CAAC,MAAM;IAC/C,MAAM,eAAe,uHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;IAC/E,MAAM,iBAAiB,uHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAEnF,MAAM,QAAQ,IAAI;IAClB,MAAM,mBAAmB,uHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA;QACjD,IAAI;YACF,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc;YAChD,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,mBAAmB;QACpD,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF,GAAG,MAAM;IAET,MAAM,qBAAqB,uHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA;QACnD,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,EAAE,SAAS;QACpC,OAAO,IAAI,KAAK,IAAI;IACtB,GAAG,MAAM;IAET,MAAM,sBAAsB,uHAAA,CAAA,0BAAuB,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;YAAE,GAAG,KAAK;YAAE,OAAO;YAAG,MAAM,CAAC,gBAAgB,EAAE,AAAC,uHAAA,CAAA,0BAAuB,CAAC,OAAO,CAAC,SAAS,IAAK,EAAE,EAAE,CAAC;QAAC,CAAC;IACvK,uHAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC,CAAA;QACzB,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS;QACxC,MAAM,cAAc,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM;QAC/C,MAAM,aAAa,oBAAoB,IAAI,CAAC,CAAA,QAAS,eAAe,MAAM,SAAS,IAAI,cAAc,MAAM,SAAS;QACpH,IAAI,YAAY;YACd,WAAW,KAAK;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;YACL,OAAO;QACT;QACA,0EAA0E;QAC1E,GAAG,oBAAoB,MAAM,CAAC,CAAC,KAAK;YAClC,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG;gBAAE,OAAO,MAAM,KAAK;gBAAE,OAAO,MAAM,IAAI;YAAC;YAC3D,OAAO;QACT,GAAG,CAAC,EAAsD;IAC5D;IAGA,IAAI,4BAA4B;IAChC,IAAI,mCAAmC;IACvC,IAAI,wCAAwC;IAE5C,uHAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC,CAAA;QACzB,MAAM,mBAAmB,4BAA4B,MAAM,EAAE,EAAE,uHAAA,CAAA,wBAAqB;QACpF,IAAI,kBAAkB;YACpB,MAAM,cAAc,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK;YACnF,MAAM,iBAAiB,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,cAAc,KAAK;YACtF,MAAM,oBAAoB,iBAAiB,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,EAAE,MAAM,KAAK;YAEtG,IAAI,aAAa;gBACf;YACF,OAAO,IAAI,gBAAgB;gBACzB;YACF,OAAO,IAAI,mBAAmB;gBAC5B;YACF,OAAO;gBACJ;YACH;QACF,OAAO;YACL;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IACpC,MAAM,wBAAwB,uHAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI;YACF,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,cAAc;YACpD,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;gBAAE,OAAO;gBAAc,KAAK;YAAM;QAC5E,EAAE,OAAO,GAAG;YAAE,OAAO;QAAO;IAC9B,GAAG,IAAI,CAAC,CAAC,GAAE,IAAM,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO,KAAK,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO;IAE1F,MAAM,oBAAoB,uHAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,CAAA;QACpD,IAAI;YACH,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,cAAc;YACzD,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;gBAAE,OAAO;gBAAc,KAAK;YAAM;QAC5E,EAAE,OAAO,GAAG;YAAE,OAAO;QAAO;IAC9B,GAAG,IAAI,CAAC,CAAC,GAAE,IAAM,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO,KAAK,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,EAAE,cAAc,EAAE,OAAO;IAE1F,kDAAkD;IAClD,MAAM,8BAAuC,EAAE;IAC/C,MAAM,8BAAmE,EAAE;IAC3E,MAAM,4BAAqC,EAAE;IAC7C,MAAM,kCAA2C,EAAE;IAEnD,uHAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC,CAAA;QACzB,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS,EAAE,MAAM,WAAW;QAC3D,IAAI,qBAAqB;QACzB,IAAI,IAAI,KAAK,IAAI,GAAG;YAClB,0BAA0B,IAAI,CAAC;YAC/B,qBAAqB;QACvB;QAEA,wDAAwD;QACxD,IAAI,CAAC,oBAAoB;YACvB,MAAM,mBAAmB,4BAA4B,MAAM,EAAE,EAAE,uHAAA,CAAA,wBAAqB;YACpF,IAAI,kBAAkB;gBACpB,IAAI;oBACF,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,cAAc;oBACnE,IAAI,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;wBAC/B,MAAM,sBAAsB,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;wBAC1D,MAAM,mBAAmB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,oBAAoB,WAAW;wBAEnE,IAAI,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,EAAE,sBAAsB;4BAC/B,4BAA4B,IAAI,CAAC;wBACnC,OAAO;4BACL,MAAM,wBAAwB,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,qBAAqB;4BACpE,IAAI,yBAAyB,MAAM,yBAAyB,GAAG;gCAC7D,4BAA4B,IAAI,CAAC;oCAAE;oCAAO,SAAS;gCAAiB;4BACtE;wBACF;oBACF;gBACF,EAAE,OAAO,OAAO;gBACd,wEAAwE;gBAC1E;YACF,OAAO;gBACL,gCAAgC,IAAI,CAAC;YACvC;QACF;IACF;IAGA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA6D;4BAC1D,uHAAA,CAAA,WAAQ;;;;;;;kCAEzB,8OAAC;wBAAE,WAAU;kCAA4C;;;;;;;;;;;;0BAK3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG3C,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,gBAAa;;;;;;kDAEf,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,QAAQ;kDAC7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;kCAQjC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGnD,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,gBAAa;;;;;;oCAGd,0BAA0B,MAAM,GAAG,mBAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,8OAAC;gDAAG,WAAU;0DACX,0BAA0B,GAAG,CAAC,CAAA,kBAC7B,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;gEAAE,WAAU;0EACxC,EAAE,IAAI;;;;;;4DACF;;uDAHA,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE;;;;;;;;;;;;;;;;oCAUnC,4BAA4B,MAAM,GAAG,mBACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGvC,8OAAC;gDAAG,WAAU;0DACX,4BAA4B,GAAG,CAAC,CAAA,kBAC/B,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,WAAW,CAAC;gEAAE,WAAU;0EACnD,EAAE,IAAI;;;;;;4DACF;;uDAHA,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;;;;;;;;;;;;;;;;oCAUjC,4BAA4B,MAAM,GAAG,mBACpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGvC,8OAAC;gDAAG,WAAU;0DACX,4BAA4B,GAAG,CAAC,CAAA,qBAC/B,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC;gEAAE,WAAU;0EAC5D,KAAK,KAAK,CAAC,IAAI;;;;;;4DACX;4DAAoB,KAAK,OAAO;4DAAC;;uDAHjC,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE;;;;;;;;;;;;;;;;oCAU3C,gCAAgC,MAAM,GAAG,mBACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGnC,8OAAC;gDAAG,WAAU;0DACX,gCAAgC,GAAG,CAAC,CAAA,kBACnC,8OAAC;;0EACC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,CAAC,eAAe,CAAC;gEAAE,WAAU;0EACvD,EAAE,IAAI;;;;;;4DACF;;uDAHA,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE;;;;;;;;;;;;;;;;oCAUjC,4BAA4B,MAAM,KAAK,KACvC,4BAA4B,MAAM,KAAK,KACvC,gCAAgC,MAAM,KAAK,KAC3C,0BAA0B,MAAM,KAAK,mBACpC,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;kCAK5D,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG9C,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACpB,8OAAC,6HAAA,CAAA,UAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,gBAAa;;;;;;oCAEd,sBAAsB,MAAM,KAAK,KAAK,kBAAkB,MAAM,KAAK,kBAClE,8OAAC;wCAAE,WAAU;kDAAoC;;;;;6DAEjD;;4CACG,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EAAqD,8OAAC,4NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;4DAA2B;;;;;;;kEACvH,8OAAC;wDAAG,WAAU;kEACX,sBAAsB,GAAG,CAAC,CAAA,sBACzB,8OAAC;;kFACC,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE;wEAAE,WAAU;kFAC5C,MAAM,IAAI;;;;;;oEACN;oEAAa,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,cAAc;;+DAH5C,MAAM,EAAE;;;;;;;;;;;;;;;;4CASxB,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;;0EAAqD,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAA0B;;;;;;;kEACpH,8OAAC;wDAAG,WAAU;kEACX,kBAAkB,GAAG,CAAC,CAAA;4DACrB,MAAM,YAAY,iBAAiB,WAAW,OAAO;4DACrD,qBACE,8OAAC;;kFACC,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAM,CAAC,UAAU,EAAE,WAAW,OAAO,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE;wEAAE,WAAU;;4EAA+B;4EACxG,aAAa;;;;;;;oEAClB;oEAAY,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,WAAW,cAAc;;+DAHhD,WAAW,EAAE;;;;;wDAM1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,kNAAA,CAAA,YAAY;wCAAC,WAAU;;;;;;oCAAwB;;;;;;;0CAGlD,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEAA0D,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DACrG,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;4DAA0B;;;;;;;kEAC9B,8OAAC;;4DAAG;4DAAa;;;;;;;kEACjB,8OAAC;;4DAAG;4DAAa;;;;;;;kEACjB,8OAAC;;0EAAG,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAuC;4DAA4B;;;;;;;kEAC3F,8OAAC;;0EAAG,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAqC;4DAAkC;;;;;;;;;;;;;;;;;;;kDAGpG,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEAA0D,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAC1G,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;4DAA4B;;;;;;;kEAChC,8OAAC;;4DAAG;4DAAsC;;;;;;;kEAC1C,8OAAC;;4DAAG;4DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC,uJAAA,CAAA,UAAoB;gDAAC,MAAM;gDAAqB,QAAQ;;;;;;;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC,wJAAA,CAAA,UAAqB;gDACpB,UAAU;gDACV,aAAa;gDACb,SAAS;;;;;;;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAC1E,8OAAC,0JAAA,CAAA,UAAuB;gDACtB,WAAW;gDACX,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7B", "debugId": null}}]}