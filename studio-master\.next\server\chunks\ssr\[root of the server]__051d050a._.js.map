{"version": 3, "sources": [], "sections": [{"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\nimport {googleAI} from '@genkit-ai/googleai';\n\nexport const ai = genkit({\n  plugins: [googleAI()],\n  model: 'googleai/gemini-2.0-flash',\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/flows/analyze-skill-for-daily-routine.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview Provides AI-driven analysis for integrating a child's skill into daily routines.\n *\n * - analyzeSkillForDailyRoutine - Function to generate routine integration suggestions for a skill.\n * - SkillAnalysisInput - Input type for the analysis.\n * - SkillAnalysisOutput - Output type for the analysis.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst SkillAnalysisInputSchema = z.object({\n  skillBehavior: z.string().describe('وصف سلوك المهارة أو الهدف المطلوب تحليله.'),\n  ageRange: z.string().describe('الفئة العمرية للطفل المستهدف بالمهارة.'),\n  childName: z.string().describe('اسم الطفل.'),\n});\nexport type SkillAnalysisInput = z.infer<typeof SkillAnalysisInputSchema>;\n\nconst SkillAnalysisOutputSchema = z.object({\n  mealtime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت الطعام، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  bathroom: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت استخدام الحمام، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  playtime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت اللعب، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  outings: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة عند الخروج من المنزل، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  bedtime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت النوم، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n  toolsIntegration: z.string().describe(\"شرح لكيفية استخدام الأدوات المذكورة أو المقترحة في الأنشطة الروتينية، مقدم بلسان الطفل وباللهجة الأردنية.\"),\n  generalTips: z.string().describe(\"نصائح عامة للأهل أو مقدمي الرعاية لضمان قابلية تطبيق الاقتراحات بشكل واقعي وممتع للطفل، مقدمة بلسان الطفل وباللهجة الأردنية.\"),\n});\nexport type SkillAnalysisOutput = z.infer<typeof SkillAnalysisOutputSchema>;\n\nexport async function analyzeSkillForDailyRoutine(\n  input: SkillAnalysisInput\n): Promise<SkillAnalysisOutput> {\n  return analyzeSkillFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'analyzeSkillForDailyRoutinePrompt',\n  input: {schema: SkillAnalysisInputSchema},\n  output: {schema: SkillAnalysisOutputSchema},\n  prompt: `يا جماعة، أنا {{{childName}}} (عمري {{{ageRange}}}). بدي تساعدوني أتعلم شغلة جديدة ومهمة إلي، وهي: \"{{{skillBehavior}}}\".\n\nاحكوا معي بالعامية الأردنية، وخلوني أحكيلكم كيف بنقدر نخلي تعلم هالشغلة جزء من يومنا العادي، وبطريقة حلوة ومسلية.\n\nأعطوني أفكار واضحة لكل وقت من هالأوقات، كأني أنا اللي بقترحها عليكم:\n\n1.  **وقت الأكل (mealtime):** (يا ماما ويا بابا، وقت الأكل، كيف ممكن نخلي تعلم \"{{{skillBehavior}}}\" إشي زاكي وممتع؟ مثلاً، أنا جاي عبالي...)\n2.  **وقت الحمام (bathroom):** (لما أكون بالحمام، كيف بتقدروا تساعدوني بـ \"{{{skillBehavior}}}\"؟ بلكي بنقدر نعمل...)\n3.  **وقت اللعب (playtime):** (يا سلااام على وقت اللعب! عشان أتعلم \"{{{skillBehavior}}}\", شو الألعاب اللي بنقدر نلعبها سوا؟ أنا بخطر عبالي...)\n4.  **لما نطلع من البيت (outings):** (لما نطلع مشوار، كيف ممكن نستغل الفرص عشان أتدرب على \"{{{skillBehavior}}}\"؟ مثلاً، بالسوق أو لما نزور قرايبنا...)\n5.  **وقت النوم (bedtime):** (قبل ما أنام، في طريقة لطيفة نتذكر فيها \"{{{skillBehavior}}}\"؟ يمكن عن طريق قصة أو لعبة هادية؟)\n\n**الأدوات اللي بنستخدمها (toolsIntegration):** (إذا في أدوات معينة بتساعدني أتعلم \"{{{skillBehavior}}}\", اشرحولي كيف بنقدر نستخدمها بالأشياء اللي بنعملها كل يوم. أو إذا مافي أدوات معينة، شو ممكن نستخدم أشياء بسيطة من البيت؟)\n\n**نصايح إلكم (generalTips):** (يا أحسن أهل بالدنيا، عشان هالشغلات تكون ممتعة ومفيدة إلي، اتذكروا إني لساتني صغير (عمري {{{ageRange}}}) وبحب التشجيع واللعب. شو كمان شغلات ممكن تعملوها عشان يكون كل إشي سهل وحلو إلي وإلكم؟)\n\nتأكدوا إنه الجواب يكون كامل ومفصل لكل قسم، وكأنه طالع مني أنا، الطفل {{{childName}}}، وبحكي أردني.\n`,\n});\n\nconst analyzeSkillFlow = ai.defineFlow(\n  {\n    name: 'analyzeSkillFlow',\n    inputSchema: SkillAnalysisInputSchema,\n    outputSchema: SkillAnalysisOutputSchema,\n  },\n  async (input) => {\n    const {output} = await prompt(input);\n    if (!output) {\n        throw new Error(\"لم يتمكن الذكاء الاصطناعي من إنشاء تحليل للمهارة.\");\n    }\n    return output;\n  }\n);\n\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACjC;AAGA,MAAM,4BAA4B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,kBAAkB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACtC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACnC;AAGO,eAAe,uCAAyB,GAAzB,4BACpB,KAAyB;IAEzB,OAAO,iBAAiB;AAC1B;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAAwB;IACxC,QAAQ;QAAC,QAAQ;IAAyB;IAC1C,QAAQ,CAAC;;;;;;;;;;;;;;;;;AAiBX,CAAC;AACD;AAEA,MAAM,mBAAmB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACpC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,IAAI,CAAC,QAAQ;QACT,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACT;;;IA1CoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/flows/analyze-skill-for-preschool-routine.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview Provides AI-driven analysis for integrating a child's skill into preschool/kindergarten routines.\n *\n * - analyzeSkillForPreschoolRoutine - Function to generate routine integration suggestions for a skill in a preschool setting.\n * - PreschoolSkillAnalysisInput - Input type for the analysis.\n * - PreschoolSkillAnalysisOutput - Output type for the analysis.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst PreschoolSkillAnalysisInputSchema = z.object({\n  skillBehavior: z.string().describe('وصف سلوك المهارة أو الهدف المطلوب تحليله.'),\n  ageRange: z.string().describe('الفئة العمرية للطفل المستهدف بالمهارة.'),\n  childName: z.string().describe('اسم الطفل.'),\n});\nexport type PreschoolSkillAnalysisInput = z.infer<typeof PreschoolSkillAnalysisInputSchema>;\n\nconst PreschoolSkillAnalysisOutputSchema = z.object({\n  arrivalTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال وقت الوصول إلى الروضة/الحضانة.\"),\n  morningCircle: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال أنشطة الدائرة الصباحية.\"),\n  activityTransition: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال أوقات الانتقال بين الأنشطة.\"),\n  learningCenters: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في المراكز/أركان التعلم المختلفة.\"),\n  outdoorPlay: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة أثناء اللعب في الساحة الخارجية.\"),\n  preschoolBathroom: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت استخدام الحمام داخل الروضة/الحضانة.\"),\n  preschoolSnackTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة في وقت الوجبة الخفيفة/الغداء في الروضة/الحضانة.\"),\n  storyTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال وقت القصة.\"),\n  departureTime: z.string().describe(\"اقتراحات عملية ومفصلة لدمج المهارة خلال وقت المغادرة من الروضة/الحضانة.\"),\n  preschoolToolsIntegration: z.string().describe(\"شرح لكيفية استخدام الأدوات المذكورة (إن وجدت في بيانات المهارة الأصلية) في هذه الأنشطة الروتينية أو اقتراح أدوات بسيطة مناسبة للروضة.\"),\n  teacherGeneralTips: z.string().describe(\"نصائح عامة للمعلمين لضمان قابلية تطبيق الاقتراحات بشكل واقعي وممتع في بيئة جماعية.\"),\n});\nexport type PreschoolSkillAnalysisOutput = z.infer<typeof PreschoolSkillAnalysisOutputSchema>;\n\nexport async function analyzeSkillForPreschoolRoutine(\n  input: PreschoolSkillAnalysisInput\n): Promise<PreschoolSkillAnalysisOutput> {\n  return analyzePreschoolSkillFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'analyzeSkillForPreschoolRoutinePrompt',\n  input: {schema: PreschoolSkillAnalysisInputSchema},\n  output: {schema: PreschoolSkillAnalysisOutputSchema},\n  prompt: `أنت خبير في تنمية الطفولة المبكرة وبرنامج بورتيج، متخصص في مساعدة معلمي الحضانات ورياض الأطفال على دمج الأهداف التعليمية للأطفال في أنشطتهم اليومية داخل بيئة الروضة/الحضانة بشكل عملي ومبتكر.\n\nالطفل: {{{childName}}}\nالمهارة المستهدفة (الهدف): \"{{{skillBehavior}}}\"\nالفئة العمرية للمهارة: {{{ageRange}}}\n\nالرجاء تقديم تحليل مفصل واقتراحات عملية حول كيفية دمج هذه المهارة في الروتين اليومي للطفل ضمن بيئة الحضانة أو الروضة. يجب أن تكون الاقتراحات قابلة للتطبيق بسهولة من قبل المعلمين.\n\nحلل الهدف وقدم اقتراحات محددة لكل من الأوقات والأنشطة التالية في الروضة/الحضانة:\n1.  **وقت الوصول:** (كيف يمكن استقبال الطفل ودمج المهارة خلال اللحظات الأولى في الروضة؟)\n2.  **الدائرة الصباحية:** (كيف يمكن دمج المهارة أثناء أنشطة الدائرة الصباحية مثل الأناشيد، عرض التاريخ، الطقس، أو مناقشة موضوع اليوم؟)\n3.  **الانتقال بين الأنشطة:** (كيف يمكن استغلال أوقات الانتقال لتعزيز المهارة بشكل سلس؟)\n4.  **المراكز/أركان التعلم:** (اقترح أنشطة محددة في أركان التعلم المختلفة - مثل ركن البناء، ركن القراءة، ركن الفن، ركن الاكتشاف - التي تعزز هذه المهارة)\n5.  **الساحة الخارجية:** (كيف يمكن تعزيز المهارة أثناء اللعب في الخارج؟)\n6.  **وقت الحمام (في الروضة):** (كيف يمكن دمج المهارة أثناء روتين استخدام الحمام في الروضة؟ سمِ هذا القسم preschoolBathroom)\n7.  **الوجبة الخفيفة/الغداء (في الروضة):** (كيف يمكن ممارسة أو تعزيز المهارة أثناء تناول الوجبات في الروضة؟ سمِ هذا القسم preschoolSnackTime)\n8.  **وقت القصة:** (هل هناك طرق لدمج المهارة أثناء سرد القصص أو الأنشطة المتعلقة بها؟)\n9.  **المغادرة:** (كيف يمكن تعزيز المهارة أو تلخيص ما تم تعلمه خلال الاستعداد للمغادرة؟)\n\nبالنسبة لـ \"استخدام الأدوات في الروضة\": اشرح كيف يمكن استخدام الأدوات المذكورة عادةً لهذه المهارة (حتى لو لم تُذكر صراحةً في الإدخال الحالي، يمكنك اقتراح أدوات شائعة أو بسيطة متوفرة في الروضة إذا كانت مناسبة) في هذه الأنشطة الروتينية. سمِ هذا القسم preschoolToolsIntegration.\n\nقدم \"نصائح عامة للتطبيق للمعلمين\": لضمان أن تكون الاقتراحات ممتعة وقابلة للتطبيق بشكل واقعي في بيئة جماعية، مع مراعاة الفئة العمرية. سمِ هذا القسم teacherGeneralTips.\n\nتأكد من أن الإجابة تكون شاملة ومفصلة لكل قسم.\n`,\n});\n\nconst analyzePreschoolSkillFlow = ai.defineFlow(\n  {\n    name: 'analyzePreschoolSkillFlow',\n    inputSchema: PreschoolSkillAnalysisInputSchema,\n    outputSchema: PreschoolSkillAnalysisOutputSchema,\n  },\n  async (input) => {\n    const {output} = await prompt(input);\n    if (!output) {\n        throw new Error(\"لم يتمكن الذكاء الاصطناعي من إنشاء تحليل للمهارة في سياق الروضة.\");\n    }\n    return output;\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,oCAAoC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjD,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC9B,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACjC;AAGA,MAAM,qCAAqC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClD,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACxC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACrC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,mBAAmB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACvC,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACxC,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACnC,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/C,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC1C;AAGO,eAAe,uCAA6B,GAA7B,gCACpB,KAAkC;IAElC,OAAO,0BAA0B;AACnC;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAAiC;IACjD,QAAQ;QAAC,QAAQ;IAAkC;IACnD,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;AAwBX,CAAC;AACD;AAEA,MAAM,4BAA4B,mHAAA,CAAA,KAAE,CAAC,UAAU,CAC7C;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,IAAI,CAAC,QAAQ;QACT,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACT;;;IAjDoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/children/%5BchildId%5D/assessment/%5BassessmentId%5D/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/children/[childId]/assessment/[assessmentId]/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/children/[childId]/assessment/[assessmentId]/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiU,GAC9V,+FACA", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/children/%5BchildId%5D/assessment/%5BassessmentId%5D/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/children/[childId]/assessment/[assessmentId]/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/children/[childId]/assessment/[assessmentId]/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}