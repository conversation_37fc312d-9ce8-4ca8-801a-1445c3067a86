{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { differenceInYears, differenceInMonths, differenceInDays, addYears, addMonths, parseISO, isValid, format } from 'date-fns';\nimport { arSA } from 'date-fns/locale'; // Import Arabic locale\nimport type { CalculatedAge } from './types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function calculateAge(birthDateString: string, assessmentDateString?: string): CalculatedAge {\n  const birthDate = parseISO(birthDateString);\n  const assessmentDate = assessmentDateString ? parseISO(assessmentDateString) : new Date();\n\n  if (!isValid(birthDate) || !isValid(assessmentDate)) {\n    // console.error(\"Invalid date provided for age calculation\", { birthDateString, assessmentDateString });\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  if (birthDate > assessmentDate) {\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  let tempAgeDate = new Date(birthDate);\n\n  const years = differenceInYears(assessmentDate, tempAgeDate);\n  tempAgeDate = addYears(tempAgeDate, years);\n\n  const months = differenceInMonths(assessmentDate, tempAgeDate);\n  tempAgeDate = addMonths(tempAgeDate, months);\n\n  const days = differenceInDays(assessmentDate, tempAgeDate);\n\n  return { years, months, days };\n}\n\nexport function formatDate(dateString: string | Date, dateFormat: string = 'PPP'): string {\n  try {\n    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;\n    if (!isValid(date)) return \"تاريخ غير صالح\";\n    return format(date, dateFormat, { locale: arSA }); // Use Arabic locale\n  } catch (error) {\n    return \"تاريخ غير صالح\";\n  }\n}\n\n// Generate a unique child ID number\nexport function generateChildIdNumber(): string {\n  const currentYear = new Date().getFullYear();\n  const randomNumber = Math.floor(Math.random() * 999) + 1;\n  return `CH-${currentYear}-${randomNumber.toString().padStart(3, '0')}`;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,6PAAwC,uBAAuB;;;;;AAGxD,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,eAAuB,EAAE,oBAA6B;IACjF,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;IAC3B,MAAM,iBAAiB,uBAAuB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,wBAAwB,IAAI;IAEnF,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,cAAc,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QACnD,yGAAyG;QACzG,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,YAAY,gBAAgB;QAC9B,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,cAAc,IAAI,KAAK;IAE3B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;IAChD,cAAc,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAEpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB;IAClD,cAAc,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAErC,MAAM,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;IAE9C,OAAO;QAAE;QAAO;QAAQ;IAAK;AAC/B;AAEO,SAAS,WAAW,UAAyB,EAAE,aAAqB,KAAK;IAC9E,IAAI;QACF,MAAM,OAAO,OAAO,eAAe,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QACrE,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAC3B,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,YAAY;YAAE,QAAQ,kJAAA,CAAA,OAAI;QAAC,IAAI,oBAAoB;IACzE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;IACvD,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,EAAE,aAAa,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACxE", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/children/ChildProfileClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/children/ChildProfileClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/children/ChildProfileClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/children/ChildProfileClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/children/ChildProfileClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/children/ChildProfileClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/children/%5BchildId%5D/page.tsx"], "sourcesContent": ["\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport type { Child, Assessment } from '@/lib/types';\nimport { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA } from '@/lib/constants';\nimport ChildProfileClient from '@/components/children/ChildProfileClient';\n\n// Helper to find child by ID (replace with actual data fetching)\nasync function getChildById(id: string): Promise<Child | undefined> {\n  // In a real app, fetch from a database\n  return MOCK_CHILDREN_DATA.find(child => child.id === id);\n}\n\n// Helper to find assessments for a child\nasync function getAssessmentsByChildId(childId: string): Promise<Assessment[]> {\n  return MOCK_ASSESSMENTS_DATA.filter(assessment => assessment.childId === childId);\n}\n\nexport default async function ChildProfilePage({ params }: { params: { childId: string } }) {\n  const child = await getChildById(params.childId);\n  const assessments = await getAssessmentsByChildId(params.childId);\n  const latestAssessment = assessments.sort((a,b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];\n\n  if (!child) {\n    return (\n      <div className=\"container mx-auto py-8 text-center\">\n        <h1 className=\"text-2xl font-semibold\">لم يتم العثور على الطفل</h1>\n        <Link href=\"/children\">\n          <Button variant=\"link\">العودة إلى قائمة الأطفال</Button>\n        </Link>\n      </div>\n    );\n  }\n  \n  return <ChildProfileClient initialChild={child} initialAssessments={assessments} initialLatestAssessment={latestAssessment} />;\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;;;;;;AAEA,iEAAiE;AACjE,eAAe,aAAa,EAAU;IACpC,uCAAuC;IACvC,OAAO,uHAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AACvD;AAEA,yCAAyC;AACzC,eAAe,wBAAwB,OAAe;IACpD,OAAO,uHAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,CAAA,aAAc,WAAW,OAAO,KAAK;AAC3E;AAEe,eAAe,iBAAiB,EAAE,MAAM,EAAmC;IACxF,MAAM,QAAQ,MAAM,aAAa,OAAO,OAAO;IAC/C,MAAM,cAAc,MAAM,wBAAwB,OAAO,OAAO;IAChE,MAAM,mBAAmB,YAAY,IAAI,CAAC,CAAC,GAAE,IAAM,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,GAAG,CAAC,EAAE;IAElI,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyB;;;;;;8BACvC,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;kCAAO;;;;;;;;;;;;;;;;;IAI/B;IAEA,qBAAO,8OAAC,oJAAA,CAAA,UAAkB;QAAC,cAAc;QAAO,oBAAoB;QAAa,yBAAyB;;;;;;AAC5G", "debugId": null}}]}