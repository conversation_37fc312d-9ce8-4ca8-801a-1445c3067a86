<x-filament-panels::page>
    @livewire(<PERSON><PERSON>\Jetstream\Http\Livewire\UpdateTeamNameForm::class, compact('team'))

    @livewire(<PERSON><PERSON>\Jetstream\Http\Livewire\TeamMemberManager::class, compact('team'))

    @if (Gate::check('delete', $team) && ! $team->personal_team)
        <x-section-border/>
        
        @livewire(Lara<PERSON>\Jetstream\Http\Livewire\DeleteTeamForm::class, compact('team'))
    @endif
</x-filament-panels::page>
