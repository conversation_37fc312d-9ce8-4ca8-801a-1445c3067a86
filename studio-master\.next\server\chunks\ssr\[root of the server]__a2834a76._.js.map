{"version": 3, "sources": [], "sections": [{"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\nimport {googleAI} from '@genkit-ai/googleai';\n\nexport const ai = genkit({\n  plugins: [googleAI()],\n  model: 'googleai/gemini-2.0-flash',\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/ai/flows/generate-learning-plan.ts"], "sourcesContent": ["\n'use server';\n\n/**\n * @fileOverview يقوم بإنشاء تقرير شامل ومخصص لطفل بناءً على نتائج التقييم وتحليل البيانات.\n *\n * - generateComprehensiveReport - دالة تقوم بإنشاء التقرير الشامل.\n * - GenerateComprehensiveReportInput - نوع الإدخال لدالة generateComprehensiveReport.\n * - GenerateComprehensiveReportOutput - نوع الإرجاع لدالة generateComprehensiveReport.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'genkit';\n\nconst GenerateComprehensiveReportInputSchema = z.object({\n  assessmentResults: z\n    .string()\n    .describe('ملخص لنتائج تقييم الطفل بما في ذلك المهارات المتقنة والتي تحتاج إلى تطوير وأي ملاحظات مهمة.'),\n  zoneOfProximalDevelopment: z\n    .string()\n    .describe(\n      'وصف لمنطقة التطور القريبة للطفل، والتي تشير إلى المهارات التي يكون الطفل مستعدًا لتعلمها بالتوجيه.'\n    ),\n  childName: z.string().describe('اسم الطفل.'),\n  ageInMonths: z.number().describe('عمر الطفل بالأشهر.'),\n  additionalFocus: z.string().optional().describe('أي معلومات أو توجيهات إضافية للمحلل ليأخذها في الاعتبار عند إنشاء التقرير.'),\n});\nexport type GenerateComprehensiveReportInput = z.infer<\n  typeof GenerateComprehensiveReportInputSchema\n>;\n\nconst GenerateComprehensiveReportOutputSchema = z.object({\n  executiveSummary: z.string().describe('ملخص تنفيذي موجز للحالة التنموية الحالية للطفل.'),\n  strengths: z.string().describe('وصف لنقاط القوة الملحوظة لدى الطفل بناءً على التقييم.'),\n  areasForDevelopment: z.string().describe('تحديد المجالات التي تحتاج إلى مزيد من الدعم والتطوير.'),\n  dataAnalysisHighlights: z.string().describe('أبرز الاستنتاجات والرؤى المستخلصة من تحليل بيانات التقييم، مثل الأنماط أو العلاقات بين المهارات المختلفة.'),\n  actionableRecommendations: z.string().describe('توصيات عملية ومحددة يمكن للأهل أو مقدمي الرعاية تطبيقها لدعم نمو الطفل.'),\n});\nexport type GenerateComprehensiveReportOutput = z.infer<\n  typeof GenerateComprehensiveReportOutputSchema\n>;\n\nexport async function generateComprehensiveReport(\n  input: GenerateComprehensiveReportInput\n): Promise<GenerateComprehensiveReportOutput> {\n  return generateComprehensiveReportFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'generateComprehensiveReportPrompt',\n  input: {schema: GenerateComprehensiveReportInputSchema},\n  output: {schema: GenerateComprehensiveReportOutputSchema},\n  prompt: `أنت خبير في تحليل بيانات تقييم نمو الطفولة المبكرة ومتخصص في إعداد تقارير شاملة وقابلة للتنفيذ بناءً على منهج بورتيج.\nمهمتك هي تحليل البيانات المقدمة عن الطفل \"{{{childName}}}\" (العمر: {{{ageInMonths}}} شهرًا) وإنشاء تقرير مفصل.\n\nالبيانات المتاحة:\n1.  **ملخص نتائج التقييم:**\n    {{{assessmentResults}}}\n\n2.  **منطقة التطور القريبة (ZPD):**\n    {{{zoneOfProximalDevelopment}}}\n\n3.  **تركيز إضافي من المستخدم (إن وجد):**\n    {{#if additionalFocus}}\n    {{{additionalFocus}}}\n    {{else}}\n    لا يوجد تركيز إضافي.\n    {{/if}}\n\nالرجاء إنشاء التقرير الشامل بحيث يتضمن الأقسام التالية:\n\n*   **الملخص التنفيذي:** قدم نظرة عامة موجزة ومكثفة عن الوضع النمائي الحالي للطفل.\n*   **نقاط القوة:** استعرض المهارات التي يتقنها الطفل والجوانب الإيجابية التي ظهرت في التقييم.\n*   **مجالات التطوير:** حدد المهارات التي تحتاج إلى دعم وتركيز، ووضح أهميتها في سياق النمو الشامل للطفل.\n*   **أبرز نتائج تحليل البيانات:** هذا قسم بالغ الأهمية. قم بتحليل معمق للبيانات المقدمة (نتائج التقييم، ZPD). ابحث عن أنماط، أو ارتباطات بين أداء الطفل في مجالات مختلفة، أو أي ملاحظات تحليلية مهمة. على سبيل المثال: \"يُظهر الطفل قوة واضحة في المهارات الحركية الدقيقة، بينما تحتاج مهارات التفاعل الاجتماعي إلى دعم أكبر.\" أو \"غالبية المهارات في الفئة العمرية X للتواصل لم يتم إتقانها بعد، مما يشير إلى أهمية التركيز على هذا الجانب.\" أو \"تحليل منطقة النمو القريبة يشير إلى استعداد الطفل للتعامل مع مهام حل المشكلات الأكثر تعقيدًا.\" يجب أن يكون هذا التحليل أكثر من مجرد سرد للمهارات، بل تفسير واستنتاج.\n*   **توصيات عملية:** بناءً على التحليل، قدم توصيات واضحة، محددة، وقابلة للتطبيق يمكن للأهل والمعلمين استخدامها لدعم تطور الطفل في مختلف المجالات. يجب أن تكون هذه التوصيات عملية وموجهة نحو الأنشطة اليومية والبيئة المحيطة بالطفل.\n\nاجعل التقرير منظماً وسهل القراءة.\n`,\n});\n\nconst generateComprehensiveReportFlow = ai.defineFlow(\n  {\n    name: 'generateComprehensiveReportFlow',\n    inputSchema: GenerateComprehensiveReportInputSchema,\n    outputSchema: GenerateComprehensiveReportOutputSchema,\n  },\n  async input => {\n    const {output} = await prompt(input);\n    if (!output) {\n        throw new Error(\"لم يتمكن الذكاء الاصطناعي من إنشاء التقرير الشامل.\");\n    }\n    return output;\n  }\n);\n\n// Ensure the file is renamed to generate-comprehensive-report.ts\n// Old name: generate-learning-plan.ts\n"], "names": [], "mappings": ";;;;;AAGA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,yCAAyC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtD,mBAAmB,uIAAA,CAAA,IAAC,CACjB,MAAM,GACN,QAAQ,CAAC;IACZ,2BAA2B,uIAAA,CAAA,IAAC,CACzB,MAAM,GACN,QAAQ,CACP;IAEJ,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAClD;AAKA,MAAM,0CAA0C,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvD,kBAAkB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACtC,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC/B,qBAAqB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACzC,wBAAwB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC5C,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACjD;AAKO,eAAe,uCAAyB,GAAzB,4BACpB,KAAuC;IAEvC,OAAO,gCAAgC;AACzC;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAAsC;IACtD,QAAQ;QAAC,QAAQ;IAAuC;IACxD,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BX,CAAC;AACD;AAEA,MAAM,kCAAkC,mHAAA,CAAA,KAAE,CAAC,UAAU,CACnD;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAM;IACJ,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,IAAI,CAAC,QAAQ;QACT,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACT,IAGF,iEAAiE;CACjE,sCAAsC;;;IAvDhB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { differenceInYears, differenceInMonths, differenceInDays, addYears, addMonths, parseISO, isValid, format } from 'date-fns';\nimport { arSA } from 'date-fns/locale'; // Import Arabic locale\nimport type { CalculatedAge } from './types';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function calculateAge(birthDateString: string, assessmentDateString?: string): CalculatedAge {\n  const birthDate = parseISO(birthDateString);\n  const assessmentDate = assessmentDateString ? parseISO(assessmentDateString) : new Date();\n\n  if (!isValid(birthDate) || !isValid(assessmentDate)) {\n    // console.error(\"Invalid date provided for age calculation\", { birthDateString, assessmentDateString });\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  if (birthDate > assessmentDate) {\n    return { years: 0, months: 0, days: 0 };\n  }\n\n  let tempAgeDate = new Date(birthDate);\n\n  const years = differenceInYears(assessmentDate, tempAgeDate);\n  tempAgeDate = addYears(tempAgeDate, years);\n\n  const months = differenceInMonths(assessmentDate, tempAgeDate);\n  tempAgeDate = addMonths(tempAgeDate, months);\n\n  const days = differenceInDays(assessmentDate, tempAgeDate);\n\n  return { years, months, days };\n}\n\nexport function formatDate(dateString: string | Date, dateFormat: string = 'PPP'): string {\n  try {\n    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;\n    if (!isValid(date)) return \"تاريخ غير صالح\";\n    return format(date, dateFormat, { locale: arSA }); // Use Arabic locale\n  } catch (error) {\n    return \"تاريخ غير صالح\";\n  }\n}\n\n// Generate a unique child ID number\nexport function generateChildIdNumber(): string {\n  const currentYear = new Date().getFullYear();\n  const randomNumber = Math.floor(Math.random() * 999) + 1;\n  return `CH-${currentYear}-${randomNumber.toString().padStart(3, '0')}`;\n}\n\n// Generate unique IDs for various entities\nexport function generateUniqueId(prefix: string = ''): string {\n  const timestamp = Date.now();\n  const random = Math.floor(Math.random() * 1000);\n  return prefix ? `${prefix}-${timestamp}-${random}` : `${timestamp}-${random}`;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,6PAAwC,uBAAuB;;;;;AAGxD,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,eAAuB,EAAE,oBAA6B;IACjF,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;IAC3B,MAAM,iBAAiB,uBAAuB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,wBAAwB,IAAI;IAEnF,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,cAAc,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;QACnD,yGAAyG;QACzG,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,YAAY,gBAAgB;QAC9B,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,MAAM;QAAE;IACxC;IAEA,IAAI,cAAc,IAAI,KAAK;IAE3B,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;IAChD,cAAc,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;IAEpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB;IAClD,cAAc,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAErC,MAAM,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB;IAE9C,OAAO;QAAE;QAAO;QAAQ;IAAK;AAC/B;AAEO,SAAS,WAAW,UAAyB,EAAE,aAAqB,KAAK;IAC9E,IAAI;QACF,MAAM,OAAO,OAAO,eAAe,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QACrE,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,OAAO;QAC3B,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,YAAY;YAAE,QAAQ,kJAAA,CAAA,OAAI;QAAC,IAAI,oBAAoB;IACzE,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;IACvD,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,EAAE,aAAa,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACxE;AAGO,SAAS,iBAAiB,SAAiB,EAAE;IAClD,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IAC1C,OAAO,SAAS,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ,GAAG,GAAG,UAAU,CAAC,EAAE,QAAQ;AAC/E", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/learning-plan/LearningPlanGeneratorForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/learning-plan/LearningPlanGeneratorForm.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/learning-plan/LearningPlanGeneratorForm.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8T,GAC3V,4FACA", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/learning-plan/LearningPlanGeneratorForm.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/learning-plan/LearningPlanGeneratorForm.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/learning-plan/LearningPlanGeneratorForm.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/app/children/%5BchildId%5D/plan/page.tsx"], "sourcesContent": ["\nimport { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, PORTAGE_CHECKLIST_DATA } from '@/lib/constants';\nimport type { Child, Assessment, PortageDimension, PortageSubCategory, PortageSkillItem } from '@/lib/types';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { ArrowRight, FileText } from 'lucide-react'; // Changed Lightbulb to FileText\nimport ComprehensiveReportGeneratorForm from '@/components/learning-plan/LearningPlanGeneratorForm'; // Updated import path\nimport { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';\nimport { formatDate } from '@/lib/utils';\nimport type { AnalyzableItemType } from '@/components/assessment/AnalyzableSkillItem'; // For type consistency\n\nasync function getChildById(id: string): Promise<Child | undefined> {\n  return MOCK_CHILDREN_DATA.find(child => child.id === id);\n}\n\nasync function getLatestAssessment(childId: string): Promise<Assessment | undefined> {\n  const childAssessments = MOCK_ASSESSMENTS_DATA.filter(a => a.childId === childId);\n  if (childAssessments.length === 0) return undefined;\n  return childAssessments.sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];\n}\n\n// --- Helper functions adapted from ViewAssessmentPage ---\nconst getSkillDetails = (skillId: string): (PortageSkillItem & { dimensionName: string; subCategoryName: string; }) | null => {\n  for (const dimension of PORTAGE_CHECKLIST_DATA) {\n    for (const subCategory of dimension.subCategories) {\n      const skill = subCategory.skills.find(s => s.id === skillId);\n      if (skill) return { ...skill, dimensionName: dimension.name, subCategoryName: subCategory.name };\n    }\n  }\n  return null;\n};\n\ninterface AnalysisResult {\n  baselineSkill: AnalyzableItemType | null;\n  ceilingSkill: AnalyzableItemType | null;\n  teachingRangeSkills: AnalyzableItemType[];\n}\n\ninterface SubCategoryAnalysisData extends AnalysisResult {\n  subCategoryName: string;\n  skillsCount: number;\n}\n\nexport interface DimensionAnalysisData { // Exporting for use in ComprehensiveReportGeneratorForm\n  dimensionName: string;\n  subCategories: SubCategoryAnalysisData[];\n}\n\nfunction calculateAnalysisForSubCategory(skillsInSubCategory: AnalyzableItemType[]): AnalysisResult {\n  const sortedSkills = [...skillsInSubCategory].sort((a, b) => {\n    const numA = parseInt(a.itemNumber, 10);\n    const numB = parseInt(b.itemNumber, 10);\n    return numA - numB;\n  });\n\n  let baselineSkill: AnalyzableItemType | null = null;\n  let ceilingSkill: AnalyzableItemType | null = null;\n  const teachingRangeSkills: AnalyzableItemType[] = [];\n\n  for (let i = 0; i <= sortedSkills.length - 3; i++) {\n    if (\n      sortedSkills[i].status === 'yes' &&\n      sortedSkills[i + 1].status === 'yes' &&\n      sortedSkills[i + 2].status === 'yes'\n    ) {\n      baselineSkill = sortedSkills[i + 2];\n      break;\n    }\n  }\n  if (!baselineSkill) {\n    const firstYesSkill = sortedSkills.find(skill => skill.status === 'yes');\n    if (firstYesSkill) {\n      baselineSkill = firstYesSkill;\n    }\n  }\n\n  for (let i = 0; i <= sortedSkills.length - 3; i++) {\n    if (\n      sortedSkills[i].status === 'no' &&\n      sortedSkills[i + 1].status === 'no' &&\n      sortedSkills[i + 2].status === 'no'\n    ) {\n      ceilingSkill = sortedSkills[i];\n      break;\n    }\n  }\n  if (!ceilingSkill) {\n    let highestNoSkill: AnalyzableItemType | null = null;\n    for (let i = sortedSkills.length - 1; i >= 0; i--) {\n        if (sortedSkills[i].status === 'no') {\n            highestNoSkill = sortedSkills[i];\n            break;\n        }\n    }\n    if (highestNoSkill) {\n        ceilingSkill = highestNoSkill;\n    }\n  }\n\n  let teachingStartIndex = 0;\n  if (baselineSkill) {\n    const baselineIdx = sortedSkills.findIndex(s => s.skillId === baselineSkill!.skillId);\n    if (baselineIdx !== -1) {\n      teachingStartIndex = baselineIdx + 1;\n    }\n  }\n\n  for (let i = teachingStartIndex; i < sortedSkills.length; i++) {\n    const skill = sortedSkills[i];\n    if (ceilingSkill && parseInt(skill.itemNumber, 10) > parseInt(ceilingSkill.itemNumber, 10)) {\n      break;\n    }\n    if (skill.status === 'no' || skill.status === 'unclear') {\n      teachingRangeSkills.push(skill);\n    }\n  }\n  \n  return { baselineSkill, ceilingSkill, teachingRangeSkills };\n}\n// --- End of helper functions ---\n\nexport default async function ComprehensiveReportPage({ params }: { params: { childId: string } }) { // Renamed page function\n  const child = await getChildById(params.childId);\n  const latestAssessment = await getLatestAssessment(params.childId);\n\n  if (!child) {\n    return (\n      <div className=\"container mx-auto py-8 text-center\">\n        <h1 className=\"text-2xl font-semibold\">لم يتم العثور على الطفل</h1>\n        <Link href=\"/children\">\n          <Button variant=\"link\">العودة إلى قائمة الأطفال</Button>\n        </Link>\n      </div>\n    );\n  }\n  \n  let structuredAnalysisData: DimensionAnalysisData[] | undefined = undefined;\n\n  if (latestAssessment) {\n    structuredAnalysisData = PORTAGE_CHECKLIST_DATA.map(dimension => {\n      const subCategoriesData: SubCategoryAnalysisData[] = dimension.subCategories.map(subCategory => {\n        const skillsForSubCategory: AnalyzableItemType[] = [];\n        latestAssessment.assessedSkills.forEach(assessedSkill => {\n          const details = getSkillDetails(assessedSkill.skillId);\n          if (details && details.dimensionName === dimension.name && details.subCategoryName === subCategory.name) {\n            skillsForSubCategory.push({\n              ...assessedSkill,\n              ...details,\n              skillId: assessedSkill.skillId, // Ensure skillId from assessedSkill is prioritized\n            });\n          }\n        });\n        skillsForSubCategory.sort((a, b) => parseInt(a.itemNumber, 10) - parseInt(b.itemNumber, 10));\n        const analysis = calculateAnalysisForSubCategory(skillsForSubCategory);\n        return {\n          subCategoryName: subCategory.name,\n          skillsCount: skillsForSubCategory.length,\n          ...analysis,\n        };\n      });\n\n      return {\n        dimensionName: dimension.name,\n        subCategories: subCategoriesData.filter(sc => sc.skillsCount > 0),\n      };\n    }).filter(dim => dim.subCategories.length > 0);\n  }\n\n\n  return (\n    <div className=\"container mx-auto py-8\">\n      <Link href={`/children/${child.id}`} className=\"inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4\">\n        العودة إلى ملف {child.name}\n        <ArrowRight className=\"h-4 w-4\" />\n      </Link>\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4\">\n        <div className=\"flex items-center gap-3\">\n          <FileText className=\"h-10 w-10 text-primary\" />\n          <div>\n            <h1 className=\"text-3xl font-bold text-primary\">التقرير الشامل لـ {child.name}</h1>\n            <p className=\"text-muted-foreground\">إنشاء تقارير شاملة مع تحليل بيانات مدعومة بالذكاء الاصطناعي.</p>\n          </div>\n        </div>\n      </div>\n\n      <ComprehensiveReportGeneratorForm \n        child={child} \n        assessment={latestAssessment} \n        structuredAnalysisData={latestAssessment ? structuredAnalysisData : undefined} \n      />\n\n      <Card className=\"mt-8 shadow-lg\">\n        <CardHeader>\n          <CardTitle>التقارير السابقة</CardTitle>\n          <CardDescription>مراجعة التقارير الشاملة التي تم إنشاؤها مسبقًا لـ {child.name}. (قريباً)</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-muted-foreground\">سيعرض هذا القسم التقارير الشاملة السابقة.</p>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA,6WAAqD,gCAAgC;AAArF;AACA,kSAAqG,sBAAsB;AAC3H;;;;;;;;AAIA,eAAe,aAAa,EAAU;IACpC,OAAO,uHAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AACvD;AAEA,eAAe,oBAAoB,OAAe;IAChD,MAAM,mBAAmB,uHAAA,CAAA,wBAAqB,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IACzE,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;IAC1C,OAAO,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,GAAG,CAAC,EAAE;AACxH;AAEA,2DAA2D;AAC3D,MAAM,kBAAkB,CAAC;IACvB,KAAK,MAAM,aAAa,uHAAA,CAAA,yBAAsB,CAAE;QAC9C,KAAK,MAAM,eAAe,UAAU,aAAa,CAAE;YACjD,MAAM,QAAQ,YAAY,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,IAAI,OAAO,OAAO;gBAAE,GAAG,KAAK;gBAAE,eAAe,UAAU,IAAI;gBAAE,iBAAiB,YAAY,IAAI;YAAC;QACjG;IACF;IACA,OAAO;AACT;AAkBA,SAAS,gCAAgC,mBAAyC;IAChF,MAAM,eAAe;WAAI;KAAoB,CAAC,IAAI,CAAC,CAAC,GAAG;QACrD,MAAM,OAAO,SAAS,EAAE,UAAU,EAAE;QACpC,MAAM,OAAO,SAAS,EAAE,UAAU,EAAE;QACpC,OAAO,OAAO;IAChB;IAEA,IAAI,gBAA2C;IAC/C,IAAI,eAA0C;IAC9C,MAAM,sBAA4C,EAAE;IAEpD,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG,IAAK;QACjD,IACE,YAAY,CAAC,EAAE,CAAC,MAAM,KAAK,SAC3B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,SAC/B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,OAC/B;YACA,gBAAgB,YAAY,CAAC,IAAI,EAAE;YACnC;QACF;IACF;IACA,IAAI,CAAC,eAAe;QAClB,MAAM,gBAAgB,aAAa,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAClE,IAAI,eAAe;YACjB,gBAAgB;QAClB;IACF;IAEA,IAAK,IAAI,IAAI,GAAG,KAAK,aAAa,MAAM,GAAG,GAAG,IAAK;QACjD,IACE,YAAY,CAAC,EAAE,CAAC,MAAM,KAAK,QAC3B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,QAC/B,YAAY,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,MAC/B;YACA,eAAe,YAAY,CAAC,EAAE;YAC9B;QACF;IACF;IACA,IAAI,CAAC,cAAc;QACjB,IAAI,iBAA4C;QAChD,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC/C,IAAI,YAAY,CAAC,EAAE,CAAC,MAAM,KAAK,MAAM;gBACjC,iBAAiB,YAAY,CAAC,EAAE;gBAChC;YACJ;QACJ;QACA,IAAI,gBAAgB;YAChB,eAAe;QACnB;IACF;IAEA,IAAI,qBAAqB;IACzB,IAAI,eAAe;QACjB,MAAM,cAAc,aAAa,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,cAAe,OAAO;QACpF,IAAI,gBAAgB,CAAC,GAAG;YACtB,qBAAqB,cAAc;QACrC;IACF;IAEA,IAAK,IAAI,IAAI,oBAAoB,IAAI,aAAa,MAAM,EAAE,IAAK;QAC7D,MAAM,QAAQ,YAAY,CAAC,EAAE;QAC7B,IAAI,gBAAgB,SAAS,MAAM,UAAU,EAAE,MAAM,SAAS,aAAa,UAAU,EAAE,KAAK;YAC1F;QACF;QACA,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,MAAM,KAAK,WAAW;YACvD,oBAAoB,IAAI,CAAC;QAC3B;IACF;IAEA,OAAO;QAAE;QAAe;QAAc;IAAoB;AAC5D;AAGe,eAAe,wBAAwB,EAAE,MAAM,EAAmC;IAC/F,MAAM,QAAQ,MAAM,aAAa,OAAO,OAAO;IAC/C,MAAM,mBAAmB,MAAM,oBAAoB,OAAO,OAAO;IAEjE,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyB;;;;;;8BACvC,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;kCAAO;;;;;;;;;;;;;;;;;IAI/B;IAEA,IAAI,yBAA8D;IAElE,IAAI,kBAAkB;QACpB,yBAAyB,uHAAA,CAAA,yBAAsB,CAAC,GAAG,CAAC,CAAA;YAClD,MAAM,oBAA+C,UAAU,aAAa,CAAC,GAAG,CAAC,CAAA;gBAC/E,MAAM,uBAA6C,EAAE;gBACrD,iBAAiB,cAAc,CAAC,OAAO,CAAC,CAAA;oBACtC,MAAM,UAAU,gBAAgB,cAAc,OAAO;oBACrD,IAAI,WAAW,QAAQ,aAAa,KAAK,UAAU,IAAI,IAAI,QAAQ,eAAe,KAAK,YAAY,IAAI,EAAE;wBACvG,qBAAqB,IAAI,CAAC;4BACxB,GAAG,aAAa;4BAChB,GAAG,OAAO;4BACV,SAAS,cAAc,OAAO;wBAChC;oBACF;gBACF;gBACA,qBAAqB,IAAI,CAAC,CAAC,GAAG,IAAM,SAAS,EAAE,UAAU,EAAE,MAAM,SAAS,EAAE,UAAU,EAAE;gBACxF,MAAM,WAAW,gCAAgC;gBACjD,OAAO;oBACL,iBAAiB,YAAY,IAAI;oBACjC,aAAa,qBAAqB,MAAM;oBACxC,GAAG,QAAQ;gBACb;YACF;YAEA,OAAO;gBACL,eAAe,UAAU,IAAI;gBAC7B,eAAe,kBAAkB,MAAM,CAAC,CAAA,KAAM,GAAG,WAAW,GAAG;YACjE;QACF,GAAG,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,CAAC,MAAM,GAAG;IAC9C;IAGA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE;gBAAE,WAAU;;oBAA2E;oBACxG,MAAM,IAAI;kCAC1B,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;0BAExB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;wCAAkC;wCAAmB,MAAM,IAAI;;;;;;;8CAC7E,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAK3C,8OAAC,mKAAA,CAAA,UAAgC;gBAC/B,OAAO;gBACP,YAAY;gBACZ,wBAAwB,mBAAmB,yBAAyB;;;;;;0BAGtE,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;;oCAAC;oCAAmD,MAAM,IAAI;oCAAC;;;;;;;;;;;;;kCAEjF,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAK/C", "debugId": null}}]}