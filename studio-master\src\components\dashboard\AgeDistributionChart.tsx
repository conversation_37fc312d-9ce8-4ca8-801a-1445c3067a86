"use client";

import type { ChartConfig } from "@/components/ui/chart";
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, YAxis, Tooltip, Cell } from "recharts";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";

interface AgeDistributionDataPoint {
  label: string;
  minMonths: number;
  maxMonths: number;
  count: number;
  fill: string;
}

interface AgeDistributionChartProps {
  data: AgeDistributionDataPoint[];
  config: ChartConfig;
}

export default function AgeDistributionChart({ data, config }: AgeDistributionChartProps) {
  if (!data || data.length === 0) {
    return <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض توزيع الأعمار.</p>;
  }

  return (
    <div className="h-[300px] w-full">
      <ChartContainer config={config} className="w-full h-full">
        <BarChart accessibilityLayer data={data} layout="vertical" margin={{ right: 20, left: 20 }}>
          <CartesianGrid horizontal={false} />
          <XAxis type="number" dataKey="count" allowDecimals={false} stroke="hsl(var(--muted-foreground))" fontSize={12} />
          <YAxis
            type="category"
            dataKey="label"
            stroke="hsl(var(--muted-foreground))"
            fontSize={12}
            width={80}
            tickLine={false}
            axisLine={false}
          />
          <ChartTooltip
            cursor={{ fill: 'hsl(var(--muted))' }}
            content={<ChartTooltipContent />}
          />
          <Bar dataKey="count" radius={4}>
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Bar>
        </BarChart>
      </ChartContainer>
    </div>
  );
}
