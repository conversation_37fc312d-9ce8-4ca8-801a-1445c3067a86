
export interface Child {
  id: string;
  name: string;
  birthDate: string; // ISO string format (YYYY-MM-DD)
  enrollmentDate: string; // ISO string format (YYYY-MM-DD) - Added
  specialistName: string;
  assessmentIds?: string[]; // Store IDs of assessments
  avatarUrl?: string; // Optional URL for child's image
  gender?: 'male' | 'female' | 'other' | 'unknown';
  sessionNotes?: SessionNote[]; // Added for session documentation
  caseStudyNotes?: string; // Added for case study
}

export type SkillStatus = 'yes' | 'no' | 'unclear';
export type ProgressStatus = 'pending' | 'implemented' | 'mastered' | 'postponed';

export interface AssessedSkill {
  skillId: string; // Corresponds to PortageSkillItem.id
  status: SkillStatus;
  notes?: string;
  mediaUrls?: string[]; // URLs to images/videos

  // Fields for progress tracking
  progressStatus?: ProgressStatus;
  implementationStartDate?: string; // ISO string format (YYYY-MM-DD)
  targetCompletionDate?: string; // ISO string format (YYYY-MM-DD)
  progressNotes?: string;
}

export interface Assessment {
  id: string;
  childId: string;
  assessmentDate: string; // ISO string format (YYYY-MM-DD)
  assessedSkills: AssessedSkill[];
  // Calculated fields (optional, can be derived on the fly or stored)
  baselines?: Record<string, string>; // { [subCategoryId]: skillId_of_last_3rd_yes }
  ceilings?: Record<string, string>;  // { [subCategoryId]: skillId_of_1st_of_3_no }
  // Teaching range is dynamically determined based on baselines and ceilings
}

export interface LearningPlan {
  id: string;
  childId: string;
  assessmentId: string;
  generatedDate: string; // ISO string format
  planDetails: string; // Generated by AI (main content)
  suggestedDailyGoals?: string; // Generated by AI
}

// Portage Checklist Structure
export interface PortageSkillItem {
  id: string; // Unique ID for the skill, e.g., "soc-rel-0to9-1"
  itemNumber: string; // Display number like "1.1" or "A1"
  ageRange: string; // e.g., "0-9 months", "9-18 months"
  behavior: string; // Description of the skill/behavior in Arabic
  applicationMethod: string; // "ملاحظة مباشرة", "سؤال الأهل", "تجريب مباشر"
  tools?: string; // Optional tools needed, in Arabic
}

export interface PortageSubCategory {
  id: string; // e.g., "soc-rel" (Social-Relationships)
  name: string; // Name of the sub-category in Arabic, e.g., "العلاقات"
  skills: PortageSkillItem[];
}

export interface PortageDimension {
  id: string; // e.g., "soc" (Social)
  name: string; // Name of the dimension in Arabic, e.g., "البعد الاجتماعي"
  subCategories: PortageSubCategory[];
}

// For age calculation display
export interface CalculatedAge {
  years: number;
  months: number;
  days: number;
}

// User Management Types
export type UserRole =
  | 'super_admin'       // إداري الشركة (المتحكم بالموقع)
  | 'eiu_manager'       // مدير وحدة التدخل المبكر
  | 'case_manager'      // مدير الحالة (سابقاً المدير الحالي)
  | 'specialist'        // الأخصائي
  | 'educator'          // المعلم
  | 'viewer';           // مشاهد

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatarUrl?: string;
  specialization?: string; // Added specialization field
}

// Session Documentation Type
export interface SessionNote {
  id: string;
  date: string; // ISO string format YYYY-MM-DD
  goalDiscussed: string;
  attendees: string; // Comma-separated or free text
  notes: string;
  nextSteps?: string;
}

