const CHUNK_PUBLIC_PATH = "server/app/children/[childId]/assessment/[assessmentId]/page.js";
const runtime = require("../../../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_36bb0a82._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__b6b8c913._.js");
runtime.loadChunk("server/chunks/ssr/src_app_a4430781._.js");
runtime.loadChunk("server/chunks/ssr/src_434156f6._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_de021099._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_96715ba7._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_forbidden-error_ea7ea172.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_client_components_unauthorized-error_c8949b27.js");
runtime.loadChunk("server/chunks/ssr/node_modules_node-fetch_src_index_02e99c9e.js");
runtime.loadChunk("server/chunks/ssr/node_modules_next_dist_ad6f9aa5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@genkit-ai_core_lib_7fa917ad._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod_lib_9973ecc6._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_ajv_dist_e6327f72._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_zod-to-json-schema_dist_c1dfbad3._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_d2960040._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_714181f1._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_46cc0f05._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_b57e52c4._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_5ebe4a09._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_protobufjs_c6ba58b0._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@opentelemetry_otlp-transformer_build_esm_7b4c78bd._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@grpc_grpc-js_cc2986af._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_handlebars_097a2825._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_yaml_dist_5cbea403._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_@genkit-ai_ai_lib_e3351302._.js");
runtime.loadChunk("server/chunks/ssr/node_modules_0727de3b._.js");
runtime.loadChunk("server/chunks/ssr/[root of the server]__051d050a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/children/[childId]/assessment/[assessmentId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/ai/flows/analyze-skill-for-daily-routine.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/ai/flows/analyze-skill-for-preschool-routine.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/children/[childId]/assessment/[assessmentId]/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/children/[childId]/assessment/[assessmentId]/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/children/[childId]/assessment/[assessmentId]/page { METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/src/app/children/[childId]/assessment/[assessmentId]/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
