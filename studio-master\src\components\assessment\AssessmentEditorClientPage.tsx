
"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import type { Child, PortageDimension, Assessment, AssessedSkill } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import AssessmentFormComponent from '@/components/assessment/AssessmentForm';
import { useAssessments } from '@/hooks/use-storage';
import { useToast } from '@/hooks/use-toast';
import { formatDate, generateUniqueId } from '@/lib/utils';

interface AssessmentEditorClientPageProps {
  child: Child;
  portageChecklist: PortageDimension[];
  existingAssessment?: Assessment; // Optional: for editing existing assessment
}

export default function AssessmentEditorClientPage({ child, portageChecklist, existingAssessment }: AssessmentEditorClientPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { addAssessment, updateAssessment } = useAssessments();
  const [isSaving, setIsSaving] = useState(false);

  const pageTitle = existingAssessment
    ? `تعديل تقييم لـ ${child.name}`
    : `تقييم جديد لـ ${child.name}`;

  const assessmentDateToShow = existingAssessment
    ? existingAssessment.assessmentDate
    : new Date().toISOString();

  const initialSkillsData = existingAssessment ? existingAssessment.assessedSkills : undefined;

  const handleSaveAssessment = async (formData: { assessedSkills: AssessedSkill[] }) => {
    setIsSaving(true);
    const assessedSkillsFiltered = formData.assessedSkills.filter(s => s.status);

    if (existingAssessment) {
      // Edit mode
      const updatedAssessment: Assessment = {
        ...existingAssessment,
        assessedSkills: assessedSkillsFiltered,
        assessmentDate: new Date().toISOString(), // Update date to reflect edit time
      };

      const success = updateAssessment(updatedAssessment);
      if (success) {
        toast({
          title: "نجاح",
          description: `تم تعديل تقييم ${child.name} بنجاح.`,
        });
        router.push(`/children/${child.id}/assessment/${existingAssessment.id}`);
      } else {
        toast({
          title: "خطأ",
          description: "فشل في تعديل التقييم.",
          variant: "destructive",
        });
        setIsSaving(false);
      }
    } else {
      // New assessment mode
      const newAssessmentId = generateUniqueId('assessment');
      const currentAssessmentDate = new Date().toISOString();

      const newAssessment: Assessment = {
        id: newAssessmentId,
        childId: child.id,
        assessmentDate: currentAssessmentDate,
        assessedSkills: assessedSkillsFiltered,
      };

      const success = addAssessment(newAssessment);
      if (success) {
        toast({
          title: "نجاح",
          description: `تم حفظ تقييم ${child.name} بنجاح.`,
        });
        router.push(`/children/${child.id}/assessment/${newAssessmentId}`);
      } else {
        toast({
          title: "خطأ",
          description: "فشل في حفظ التقييم.",
          variant: "destructive",
        });
        setIsSaving(false);
      }
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Link href={`/children/${child.id}/assessment`} className="inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4">
        العودة إلى سجل تقييمات {child.name}
        <ArrowRight className="h-4 w-4" />
      </Link>
      <h1 className="text-3xl font-bold mb-2 text-primary">{pageTitle}</h1>
      <p className="text-muted-foreground mb-6">
        {existingAssessment ? `تعديل التقييم المؤرخ في: ${formatDate(existingAssessment.assessmentDate)}.` : `تاريخ التقييم: ${formatDate(new Date().toISOString())}`}
      </p>

      <AssessmentFormComponent
        child={child}
        portageChecklist={portageChecklist}
        onSubmit={handleSaveAssessment}
        isSubmitting={isSaving}
        initialData={initialSkillsData}
      />
    </div>
  );
}
