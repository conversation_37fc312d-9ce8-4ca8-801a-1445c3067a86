
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import {
  Chart<PERSON>ontainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig
} from "@/components/ui/chart";

interface GenderDistributionChartProps {
  maleCount: number;
  femaleCount: number;
}

export default function GenderDistributionChart({ maleCount, femaleCount }: GenderDistributionChartProps) {
  const chartData = [
    { name: 'ذكور', value: maleCount, fill: 'hsl(var(--chart-1))' }, // Primary blue
    { name: 'إناث', value: femaleCount, fill: 'hsl(var(--chart-3))' }, // Tealish or another contrasting color
  ].filter(item => item.value > 0);

  if (chartData.length === 0) {
    return <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض توزيع الجنس.</p>;
  }

  const chartConfig = chartData.reduce((acc, item) => {
    acc[item.name] = { label: item.name, color: item.fill };
    return acc;
  }, {} as ChartConfig);

  return (
    <div className="h-[300px] w-full">
      <ChartContainer config={chartConfig} className="w-full h-full">
        <PieChart>
          <ChartTooltip content={<ChartTooltipContent nameKey="name" hideLabel />} />
          <Pie
            data={chartData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            outerRadius={80} // Adjusted outerRadius for better fit with label
            labelLine={false}
            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {
              const RADIAN = Math.PI / 180;
              // Position label slightly outside the pie slice for clarity
              const radius = outerRadius + 15; 
              const x = cx + radius * Math.cos(-midAngle * RADIAN);
              const y = cy + radius * Math.sin(-midAngle * RADIAN);
              if (chartData[index].value === 0) return null;
              return (
                <text
                  x={x}
                  y={y}
                  fill="hsl(var(--foreground))" // Use foreground for better visibility
                  textAnchor={x > cx ? 'start' : 'end'}
                  dominantBaseline="central"
                  fontSize="12px"
                >
                  {`${name}: ${(percent * 100).toFixed(0)}%`}
                </text>
              );
            }}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Pie>
          <ChartLegend
            content={<ChartLegendContent nameKey="name" wrapperStyle={{paddingTop: 10}} />}
            verticalAlign="bottom"
            align="center"
          />
        </PieChart>
      </ChartContainer>
    </div>
  );
}

