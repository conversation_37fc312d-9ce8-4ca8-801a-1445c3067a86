self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"4098cb20d0bb4270082ef9403a594235c45035e36a\": {\n      \"workers\": {\n        \"app/children/[childId]/plan/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/children/[childId]/plan/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/generate-learning-plan.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/children/[childId]/plan/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"2j7wetXNVcEuas0AAq4fMATANCB2VwOdg4wsHWhIyK8=\"\n}"