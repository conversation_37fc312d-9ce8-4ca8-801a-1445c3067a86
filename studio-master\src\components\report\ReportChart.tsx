"use client";

import type { Assessment } from '@/lib/types';
import { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import { Bar, Bar<PERSON>hart, CartesianGrid, XAxis, YAxis, <PERSON>ltip, Legend, ResponsiveContainer } from "recharts";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from "@/components/ui/chart";
import { formatDate } from '@/lib/utils';

interface ReportChartProps {
  assessment: Assessment;
  childName: string;
}

interface ChartDataPoint {
  dimensionName: string;
  "مهارات متقنة": number;
  "مهارات تحتاج تطوير": number;
}

const getSkillDimensionName = (skillId: string): string | null => {
  for (const dimension of PORTAGE_CHECKLIST_DATA) {
    for (const subCategory of dimension.subCategories) {
      const skill = subCategory.skills.find(s => s.id === skillId);
      if (skill) return dimension.name;
    }
  }
  return null;
};

export default function ReportChart({ assessment, childName }: ReportChartProps) {
  const chartData = PORTAGE_CHECKLIST_DATA.map(dimension => {
    let achieved = 0;
    let needsDevelopment = 0;

    assessment.assessedSkills.forEach(assessedSkill => {
      const skillDimensionName = getSkillDimensionName(assessedSkill.skillId);
      if (skillDimensionName === dimension.name) {
        if (assessedSkill.status === 'yes') {
          achieved++;
        } else { // 'no' or 'unclear'
          needsDevelopment++;
        }
      }
    });
    return {
      dimensionName: dimension.name,
      "مهارات متقنة": achieved,
      "مهارات تحتاج تطوير": needsDevelopment,
    };
  }).filter(d => d["مهارات متقنة"] > 0 || d["مهارات تحتاج تطوير"] > 0);

  const chartConfig = {
    "مهارات متقنة": {
      label: "مهارات متقنة",
      color: "hsl(var(--chart-2))", 
    },
    "مهارات تحتاج تطوير": {
      label: "مهارات تحتاج تطوير",
      color: "hsl(var(--chart-5))", 
    },
  } satisfies ChartConfig;

  if (chartData.length === 0) {
    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-4">لا توجد بيانات كافية لعرض الرسم البياني لهذا التقييم.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mt-8 shadow-lg">
      <CardHeader>
        <CardTitle>ملخص أداء المهارات حسب البُعد</CardTitle>
        <CardDescription>
          رسم بياني يوضح عدد المهارات المتقنة والتي تحتاج إلى تطوير لكل بُعد في تقييم {childName} بتاريخ {formatDate(assessment.assessmentDate)}.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="min-h-[300px] w-full aspect-video">
          <BarChart 
            data={chartData} 
            layout="vertical" 
            margin={{ right: 20, left: 20, top:5, bottom: 5 }}
            barCategoryGap="20%"
          >
            <CartesianGrid horizontal={false} strokeDasharray="3 3" />
            <XAxis type="number" allowDecimals={false} stroke="hsl(var(--muted-foreground))" fontSize={12} />
            <YAxis 
              dataKey="dimensionName" 
              type="category" 
              tickLine={false} 
              axisLine={false}
              width={180} 
              stroke="hsl(var(--muted-foreground))"
              tick={{fontSize: 12, fill: 'hsl(var(--foreground))', textAnchor: 'start', dx:5 }} // dx to move text slightly from axis
              interval={0} // Show all ticks
            />
            <ChartTooltip 
                cursor={{fill: 'hsl(var(--muted))'}}
                content={<ChartTooltipContent indicator="dot" />} 
            />
            <ChartLegend content={<ChartLegendContent wrapperStyle={{paddingTop: 20}} />} />
            <Bar dataKey="مهارات متقنة" fill="var(--color-مهارات متقنة)" radius={[0, 4, 4, 0]} />
            <Bar dataKey="مهارات تحتاج تطوير" fill="var(--color-مهارات تحتاج تطوير)" radius={[0, 4, 4, 0]} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
