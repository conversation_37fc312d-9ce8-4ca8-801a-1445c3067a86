{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/assessment/AgeDisplay.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { calculateAge, formatDate } from '@/lib/utils';\nimport type { CalculatedAge } from '@/lib/types';\nimport { Skeleton } from '@/components/ui/skeleton';\n\ninterface AgeDisplayProps {\n  birthDate: string; // ISO date string\n  assessmentDate?: string; // Optional ISO date string, defaults to today\n  className?: string;\n  label?: string;\n}\n\nexport default function AgeDisplay({ birthDate, assessmentDate, className, label = \"العمر:\" }: AgeDisplayProps) {\n  const [age, setAge] = useState<CalculatedAge | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    try {\n      const calculated = calculateAge(birthDate, assessmentDate);\n      setAge(calculated);\n    } catch (error) {\n      // console.error(\"Error calculating age:\", error);\n      setAge(null); // Set to null or a default error state if needed\n    } finally {\n      setIsLoading(false);\n    }\n  }, [birthDate, assessmentDate]);\n\n  if (isLoading) {\n    return <Skeleton className={`h-5 w-32 ${className}`} />;\n  }\n\n  if (!age) {\n    return <span className={className}>تاريخ غير صالح</span>;\n  }\n\n  return (\n    <span className={className}>\n      {label}{' '}\n      {age.years > 0 && `${age.years}س `}\n      {age.months > 0 && `${age.months}ش `}\n      {`${age.days}ي`}\n      {! (age.years > 0 || age.months > 0 || age.days > 0) && assessmentDate && birthDate > assessmentDate! && '(تاريخ ميلاد مستقبلي)'}\n      {! (age.years > 0 || age.months > 0 || age.days > 0) && !assessmentDate && new Date(birthDate) > new Date() && '(تاريخ ميلاد مستقبلي)'}\n    </span>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAce,SAAS,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAmB;IAC5G,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,WAAW;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,kDAAkD;YAClD,OAAO,OAAO,iDAAiD;QACjE,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAW;KAAe;IAE9B,IAAI,WAAW;QACb,qBAAO,8OAAC,oIAAA,CAAA,WAAQ;YAAC,WAAW,CAAC,SAAS,EAAE,WAAW;;;;;;IACrD;IAEA,IAAI,CAAC,KAAK;QACR,qBAAO,8OAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBACE,8OAAC;QAAK,WAAW;;YACd;YAAO;YACP,IAAI,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC;YACnC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;YACd,CAAE,CAAC,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,kBAAkB,YAAY,kBAAmB;YACxG,CAAE,CAAC,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,kBAAkB,IAAI,KAAK,aAAa,IAAI,UAAU;;;;;;;AAGrH", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/lib/storage.ts"], "sourcesContent": ["import type { Child, Assessment, User, LearningPlan } from './types';\nimport { MOCK_CHILDREN_DATA, MOCK_ASSESSMENTS_DATA, MOCK_USERS_DATA } from './constants';\n\n// Storage keys\nconst STORAGE_KEYS = {\n  CHILDREN: 'portage_plus_children',\n  ASSESSMENTS: 'portage_plus_assessments',\n  USERS: 'portage_plus_users',\n  LEARNING_PLANS: 'portage_plus_learning_plans',\n  APP_VERSION: 'portage_plus_version',\n  LAST_SYNC: 'portage_plus_last_sync',\n} as const;\n\nconst APP_VERSION = '1.0.0';\n\n// Check if we're in a browser environment\nconst isBrowser = typeof window !== 'undefined';\n\n// Initialize storage with default data if empty\nexport function initializeStorage(): void {\n  if (!isBrowser) return;\n\n  try {\n    // Check if this is the first time or if we need to migrate data\n    const currentVersion = localStorage.getItem(STORAGE_KEYS.APP_VERSION);\n    const existingChildren = localStorage.getItem(STORAGE_KEYS.CHILDREN);\n\n    if (!currentVersion || !existingChildren) {\n      // First time setup - populate with mock data\n      localStorage.setItem(STORAGE_KEYS.CHILDREN, JSON.stringify(MOCK_CHILDREN_DATA));\n      localStorage.setItem(STORAGE_KEYS.ASSESSMENTS, JSON.stringify(MOCK_ASSESSMENTS_DATA));\n      localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(MOCK_USERS_DATA));\n      localStorage.setItem(STORAGE_KEYS.LEARNING_PLANS, JSON.stringify([]));\n      localStorage.setItem(STORAGE_KEYS.APP_VERSION, APP_VERSION);\n      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());\n      \n      console.log('✅ Local storage initialized with default data');\n    }\n  } catch (error) {\n    console.error('❌ Failed to initialize storage:', error);\n  }\n}\n\n// Generic storage functions\nfunction getFromStorage<T>(key: string, defaultValue: T): T {\n  if (!isBrowser) return defaultValue;\n\n  try {\n    const item = localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`❌ Failed to get ${key} from storage:`, error);\n    return defaultValue;\n  }\n}\n\nfunction setToStorage<T>(key: string, value: T): boolean {\n  if (!isBrowser) return false;\n\n  try {\n    localStorage.setItem(key, JSON.stringify(value));\n    localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());\n    return true;\n  } catch (error) {\n    console.error(`❌ Failed to set ${key} to storage:`, error);\n    return false;\n  }\n}\n\n// Children storage functions\nexport function getChildren(): Child[] {\n  return getFromStorage(STORAGE_KEYS.CHILDREN, []);\n}\n\nexport function getChildById(id: string): Child | undefined {\n  const children = getChildren();\n  return children.find(child => child.id === id);\n}\n\nexport function saveChild(child: Child): boolean {\n  const children = getChildren();\n  const existingIndex = children.findIndex(c => c.id === child.id);\n  \n  if (existingIndex >= 0) {\n    children[existingIndex] = child;\n  } else {\n    children.unshift(child); // Add new children at the beginning\n  }\n  \n  return setToStorage(STORAGE_KEYS.CHILDREN, children);\n}\n\nexport function deleteChild(childId: string): boolean {\n  const children = getChildren();\n  const filteredChildren = children.filter(c => c.id !== childId);\n  \n  // Also delete related assessments and learning plans\n  const assessments = getAssessments();\n  const filteredAssessments = assessments.filter(a => a.childId !== childId);\n  setToStorage(STORAGE_KEYS.ASSESSMENTS, filteredAssessments);\n  \n  const learningPlans = getLearningPlans();\n  const filteredPlans = learningPlans.filter(p => p.childId !== childId);\n  setToStorage(STORAGE_KEYS.LEARNING_PLANS, filteredPlans);\n  \n  return setToStorage(STORAGE_KEYS.CHILDREN, filteredChildren);\n}\n\n// Assessment storage functions\nexport function getAssessments(): Assessment[] {\n  return getFromStorage(STORAGE_KEYS.ASSESSMENTS, []);\n}\n\nexport function getAssessmentsByChildId(childId: string): Assessment[] {\n  const assessments = getAssessments();\n  return assessments.filter(assessment => assessment.childId === childId);\n}\n\nexport function getAssessmentById(id: string): Assessment | undefined {\n  const assessments = getAssessments();\n  return assessments.find(assessment => assessment.id === id);\n}\n\nexport function saveAssessment(assessment: Assessment): boolean {\n  const assessments = getAssessments();\n  const existingIndex = assessments.findIndex(a => a.id === assessment.id);\n  \n  if (existingIndex >= 0) {\n    assessments[existingIndex] = assessment;\n  } else {\n    assessments.unshift(assessment); // Add new assessments at the beginning\n  }\n  \n  return setToStorage(STORAGE_KEYS.ASSESSMENTS, assessments);\n}\n\nexport function deleteAssessment(assessmentId: string): boolean {\n  const assessments = getAssessments();\n  const filteredAssessments = assessments.filter(a => a.id !== assessmentId);\n  \n  // Also delete related learning plans\n  const learningPlans = getLearningPlans();\n  const filteredPlans = learningPlans.filter(p => p.assessmentId !== assessmentId);\n  setToStorage(STORAGE_KEYS.LEARNING_PLANS, filteredPlans);\n  \n  return setToStorage(STORAGE_KEYS.ASSESSMENTS, filteredAssessments);\n}\n\n// User storage functions\nexport function getUsers(): User[] {\n  return getFromStorage(STORAGE_KEYS.USERS, []);\n}\n\nexport function getUserById(id: string): User | undefined {\n  const users = getUsers();\n  return users.find(user => user.id === id);\n}\n\nexport function saveUser(user: User): boolean {\n  const users = getUsers();\n  const existingIndex = users.findIndex(u => u.id === user.id);\n  \n  if (existingIndex >= 0) {\n    users[existingIndex] = user;\n  } else {\n    users.push(user);\n  }\n  \n  return setToStorage(STORAGE_KEYS.USERS, users);\n}\n\n// Learning Plans storage functions\nexport function getLearningPlans(): LearningPlan[] {\n  return getFromStorage(STORAGE_KEYS.LEARNING_PLANS, []);\n}\n\nexport function getLearningPlansByChildId(childId: string): LearningPlan[] {\n  const plans = getLearningPlans();\n  return plans.filter(plan => plan.childId === childId);\n}\n\nexport function saveLearningPlan(plan: LearningPlan): boolean {\n  const plans = getLearningPlans();\n  const existingIndex = plans.findIndex(p => p.id === plan.id);\n  \n  if (existingIndex >= 0) {\n    plans[existingIndex] = plan;\n  } else {\n    plans.unshift(plan);\n  }\n  \n  return setToStorage(STORAGE_KEYS.LEARNING_PLANS, plans);\n}\n\n// Data management functions\nexport function exportAllData() {\n  if (!isBrowser) return null;\n\n  return {\n    version: APP_VERSION,\n    exportedAt: new Date().toISOString(),\n    data: {\n      children: getChildren(),\n      assessments: getAssessments(),\n      users: getUsers(),\n      learningPlans: getLearningPlans(),\n    }\n  };\n}\n\nexport function importAllData(data: any): boolean {\n  if (!isBrowser) return false;\n\n  try {\n    if (data.data) {\n      if (data.data.children) setToStorage(STORAGE_KEYS.CHILDREN, data.data.children);\n      if (data.data.assessments) setToStorage(STORAGE_KEYS.ASSESSMENTS, data.data.assessments);\n      if (data.data.users) setToStorage(STORAGE_KEYS.USERS, data.data.users);\n      if (data.data.learningPlans) setToStorage(STORAGE_KEYS.LEARNING_PLANS, data.data.learningPlans);\n      \n      localStorage.setItem(STORAGE_KEYS.APP_VERSION, data.version || APP_VERSION);\n      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());\n      \n      return true;\n    }\n    return false;\n  } catch (error) {\n    console.error('❌ Failed to import data:', error);\n    return false;\n  }\n}\n\nexport function clearAllData(): boolean {\n  if (!isBrowser) return false;\n\n  try {\n    Object.values(STORAGE_KEYS).forEach(key => {\n      localStorage.removeItem(key);\n    });\n    return true;\n  } catch (error) {\n    console.error('❌ Failed to clear data:', error);\n    return false;\n  }\n}\n\n// Get storage info\nexport function getStorageInfo() {\n  if (!isBrowser) return null;\n\n  return {\n    version: localStorage.getItem(STORAGE_KEYS.APP_VERSION),\n    lastSync: localStorage.getItem(STORAGE_KEYS.LAST_SYNC),\n    childrenCount: getChildren().length,\n    assessmentsCount: getAssessments().length,\n    usersCount: getUsers().length,\n    learningPlansCount: getLearningPlans().length,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA,eAAe;AACf,MAAM,eAAe;IACnB,UAAU;IACV,aAAa;IACb,OAAO;IACP,gBAAgB;IAChB,aAAa;IACb,WAAW;AACb;AAEA,MAAM,cAAc;AAEpB,0CAA0C;AAC1C,MAAM,YAAY,gBAAkB;AAG7B,SAAS;IACd,wCAAgB;;AAqBlB;AAEA,4BAA4B;AAC5B,SAAS,eAAkB,GAAW,EAAE,YAAe;IACrD,wCAAgB,OAAO;;AASzB;AAEA,SAAS,aAAgB,GAAW,EAAE,KAAQ;IAC5C,wCAAgB,OAAO;;AAUzB;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,QAAQ,EAAE,EAAE;AACjD;AAEO,SAAS,aAAa,EAAU;IACrC,MAAM,WAAW;IACjB,OAAO,SAAS,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC7C;AAEO,SAAS,UAAU,KAAY;IACpC,MAAM,WAAW;IACjB,MAAM,gBAAgB,SAAS,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;IAE/D,IAAI,iBAAiB,GAAG;QACtB,QAAQ,CAAC,cAAc,GAAG;IAC5B,OAAO;QACL,SAAS,OAAO,CAAC,QAAQ,oCAAoC;IAC/D;IAEA,OAAO,aAAa,aAAa,QAAQ,EAAE;AAC7C;AAEO,SAAS,YAAY,OAAe;IACzC,MAAM,WAAW;IACjB,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEvD,qDAAqD;IACrD,MAAM,cAAc;IACpB,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IAClE,aAAa,aAAa,WAAW,EAAE;IAEvC,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;IAC9D,aAAa,aAAa,cAAc,EAAE;IAE1C,OAAO,aAAa,aAAa,QAAQ,EAAE;AAC7C;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,WAAW,EAAE,EAAE;AACpD;AAEO,SAAS,wBAAwB,OAAe;IACrD,MAAM,cAAc;IACpB,OAAO,YAAY,MAAM,CAAC,CAAA,aAAc,WAAW,OAAO,KAAK;AACjE;AAEO,SAAS,kBAAkB,EAAU;IAC1C,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC,CAAA,aAAc,WAAW,EAAE,KAAK;AAC1D;AAEO,SAAS,eAAe,UAAsB;IACnD,MAAM,cAAc;IACpB,MAAM,gBAAgB,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,EAAE;IAEvE,IAAI,iBAAiB,GAAG;QACtB,WAAW,CAAC,cAAc,GAAG;IAC/B,OAAO;QACL,YAAY,OAAO,CAAC,aAAa,uCAAuC;IAC1E;IAEA,OAAO,aAAa,aAAa,WAAW,EAAE;AAChD;AAEO,SAAS,iBAAiB,YAAoB;IACnD,MAAM,cAAc;IACpB,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE7D,qCAAqC;IACrC,MAAM,gBAAgB;IACtB,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;IACnE,aAAa,aAAa,cAAc,EAAE;IAE1C,OAAO,aAAa,aAAa,WAAW,EAAE;AAChD;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,KAAK,EAAE,EAAE;AAC9C;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AACxC;AAEO,SAAS,SAAS,IAAU;IACjC,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IAE3D,IAAI,iBAAiB,GAAG;QACtB,KAAK,CAAC,cAAc,GAAG;IACzB,OAAO;QACL,MAAM,IAAI,CAAC;IACb;IAEA,OAAO,aAAa,aAAa,KAAK,EAAE;AAC1C;AAGO,SAAS;IACd,OAAO,eAAe,aAAa,cAAc,EAAE,EAAE;AACvD;AAEO,SAAS,0BAA0B,OAAe;IACvD,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;AAC/C;AAEO,SAAS,iBAAiB,IAAkB;IACjD,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;IAE3D,IAAI,iBAAiB,GAAG;QACtB,KAAK,CAAC,cAAc,GAAG;IACzB,OAAO;QACL,MAAM,OAAO,CAAC;IAChB;IAEA,OAAO,aAAa,aAAa,cAAc,EAAE;AACnD;AAGO,SAAS;IACd,wCAAgB,OAAO;;AAYzB;AAEO,SAAS,cAAc,IAAS;IACrC,wCAAgB,OAAO;;AAmBzB;AAEO,SAAS;IACd,wCAAgB,OAAO;;AAWzB;AAGO,SAAS;IACd,wCAAgB,OAAO;;AAUzB", "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/hooks/use-storage.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport type { Child, Assessment, User, LearningPlan } from '@/lib/types';\nimport {\n  initializeStorage,\n  getChildren,\n  getChildById,\n  saveChild,\n  deleteChild,\n  getAssessments,\n  getAssessmentsByChildId,\n  getAssessmentById,\n  saveAssessment,\n  deleteAssessment,\n  getUsers,\n  getUserById,\n  saveUser,\n  getLearningPlans,\n  getLearningPlansByChildId,\n  saveLearningPlan,\n  exportAllData,\n  importAllData,\n  clearAllData,\n  getStorageInfo,\n} from '@/lib/storage';\n\n// Custom hook for children management\nexport function useChildren() {\n  const [children, setChildren] = useState<Child[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshChildren = useCallback(() => {\n    setChildren(getChildren());\n  }, []);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshChildren();\n    setLoading(false);\n  }, [refreshChildren]);\n\n  const addChild = useCallback((child: Child) => {\n    const success = saveChild(child);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const updateChild = useCallback((child: Child) => {\n    const success = saveChild(child);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const removeChild = useCallback((childId: string) => {\n    const success = deleteChild(childId);\n    if (success) {\n      refreshChildren();\n    }\n    return success;\n  }, [refreshChildren]);\n\n  const getChild = useCallback((childId: string) => {\n    return getChildById(childId);\n  }, []);\n\n  return {\n    children,\n    loading,\n    addChild,\n    updateChild,\n    removeChild,\n    getChild,\n    refreshChildren,\n  };\n}\n\n// Custom hook for assessments management\nexport function useAssessments(childId?: string) {\n  const [assessments, setAssessments] = useState<Assessment[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshAssessments = useCallback(() => {\n    if (childId) {\n      setAssessments(getAssessmentsByChildId(childId));\n    } else {\n      setAssessments(getAssessments());\n    }\n  }, [childId]);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshAssessments();\n    setLoading(false);\n  }, [refreshAssessments]);\n\n  const addAssessment = useCallback((assessment: Assessment) => {\n    const success = saveAssessment(assessment);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const updateAssessment = useCallback((assessment: Assessment) => {\n    const success = saveAssessment(assessment);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const removeAssessment = useCallback((assessmentId: string) => {\n    const success = deleteAssessment(assessmentId);\n    if (success) {\n      refreshAssessments();\n    }\n    return success;\n  }, [refreshAssessments]);\n\n  const getAssessment = useCallback((assessmentId: string) => {\n    return getAssessmentById(assessmentId);\n  }, []);\n\n  return {\n    assessments,\n    loading,\n    addAssessment,\n    updateAssessment,\n    removeAssessment,\n    getAssessment,\n    refreshAssessments,\n  };\n}\n\n// Custom hook for users management\nexport function useUsers() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshUsers = useCallback(() => {\n    setUsers(getUsers());\n  }, []);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshUsers();\n    setLoading(false);\n  }, [refreshUsers]);\n\n  const addUser = useCallback((user: User) => {\n    const success = saveUser(user);\n    if (success) {\n      refreshUsers();\n    }\n    return success;\n  }, [refreshUsers]);\n\n  const updateUser = useCallback((user: User) => {\n    const success = saveUser(user);\n    if (success) {\n      refreshUsers();\n    }\n    return success;\n  }, [refreshUsers]);\n\n  const getUser = useCallback((userId: string) => {\n    return getUserById(userId);\n  }, []);\n\n  return {\n    users,\n    loading,\n    addUser,\n    updateUser,\n    getUser,\n    refreshUsers,\n  };\n}\n\n// Custom hook for learning plans management\nexport function useLearningPlans(childId?: string) {\n  const [learningPlans, setLearningPlans] = useState<LearningPlan[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const refreshLearningPlans = useCallback(() => {\n    if (childId) {\n      setLearningPlans(getLearningPlansByChildId(childId));\n    } else {\n      setLearningPlans(getLearningPlans());\n    }\n  }, [childId]);\n\n  useEffect(() => {\n    initializeStorage();\n    refreshLearningPlans();\n    setLoading(false);\n  }, [refreshLearningPlans]);\n\n  const addLearningPlan = useCallback((plan: LearningPlan) => {\n    const success = saveLearningPlan(plan);\n    if (success) {\n      refreshLearningPlans();\n    }\n    return success;\n  }, [refreshLearningPlans]);\n\n  const updateLearningPlan = useCallback((plan: LearningPlan) => {\n    const success = saveLearningPlan(plan);\n    if (success) {\n      refreshLearningPlans();\n    }\n    return success;\n  }, [refreshLearningPlans]);\n\n  return {\n    learningPlans,\n    loading,\n    addLearningPlan,\n    updateLearningPlan,\n    refreshLearningPlans,\n  };\n}\n\n// Custom hook for data management\nexport function useDataManagement() {\n  const [storageInfo, setStorageInfo] = useState<any>(null);\n\n  const refreshStorageInfo = useCallback(() => {\n    setStorageInfo(getStorageInfo());\n  }, []);\n\n  useEffect(() => {\n    refreshStorageInfo();\n  }, [refreshStorageInfo]);\n\n  const exportData = useCallback(() => {\n    return exportAllData();\n  }, []);\n\n  const importData = useCallback((data: any) => {\n    const success = importAllData(data);\n    if (success) {\n      refreshStorageInfo();\n    }\n    return success;\n  }, [refreshStorageInfo]);\n\n  const clearData = useCallback(() => {\n    const success = clearAllData();\n    if (success) {\n      refreshStorageInfo();\n    }\n    return success;\n  }, [refreshStorageInfo]);\n\n  return {\n    storageInfo,\n    exportData,\n    importData,\n    clearData,\n    refreshStorageInfo,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAwBO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IACxB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAgB;IAEpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;QAC1B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;QAC1B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAgB;IAEpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,OAAO,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE;IACtB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,eAAe,OAAgB;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,SAAS;YACX,eAAe,CAAA,GAAA,qHAAA,CAAA,0BAAuB,AAAD,EAAE;QACzC,OAAO;YACL,eAAe,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;QAC9B;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAmB;IAEvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;QAC/B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE;QAC/B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,OAAO,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,SAAS,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD;IAClB,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;QACzB,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3B,OAAO,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD,EAAE;IACrB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS,iBAAiB,OAAgB;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,SAAS;YACX,iBAAiB,CAAA,GAAA,qHAAA,CAAA,4BAAyB,AAAD,EAAE;QAC7C,OAAO;YACL,iBAAiB,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD;QAClC;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,qHAAA,CAAA,oBAAiB,AAAD;QAChB;QACA,WAAW;IACb,GAAG;QAAC;KAAqB;IAEzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAqB;IAEzB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAqB;IAEzB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,eAAe,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAmB;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,OAAO,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD;IACrB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,gBAAa,AAAD,EAAE;QAC9B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QAC3B,IAAI,SAAS;YACX;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAmB;IAEvB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\nimport { DayPicker } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: CalendarProps) {\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\"p-3\", className)}\n      classNames={{\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\n        month: \"space-y-4\",\n        caption: \"flex justify-center pt-1 relative items-center\",\n        caption_label: \"text-sm font-medium\",\n        nav: \"space-x-1 flex items-center\",\n        nav_button: cn(\n          buttonVariants({ variant: \"outline\" }),\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\n        ),\n        nav_button_previous: \"absolute left-1\",\n        nav_button_next: \"absolute right-1\",\n        table: \"w-full border-collapse space-y-1\",\n        head_row: \"flex\",\n        head_cell:\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\n        row: \"flex w-full mt-2\",\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n        day: cn(\n          buttonVariants({ variant: \"ghost\" }),\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\n        ),\n        day_range_end: \"day-range-end\",\n        day_selected:\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n        day_today: \"bg-accent text-accent-foreground\",\n        day_outside:\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\n        day_disabled: \"text-muted-foreground opacity-50\",\n        day_range_middle:\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n        day_hidden: \"invisible\",\n        ...classNames,\n      }}\n      components={{\n        IconLeft: ({ className, ...props }) => (\n          <ChevronLeft className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n        IconRight: ({ className, ...props }) => (\n          <ChevronRight className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = \"Calendar\"\n\nexport { Calendar }\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAWA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM;YACN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,eAAe;YACf,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;YAE7D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;QAEhE;QACC,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/date-picker.tsx"], "sourcesContent": ["\n\"use client\"\n\nimport * as React from \"react\"\nimport { format } from \"date-fns\"\nimport { arSA } from \"date-fns/locale\" // For Arabic locale\nimport { Calendar as CalendarIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Calendar } from \"@/components/ui/calendar\"\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\"\n\ninterface DatePickerProps {\n  date: Date | undefined;\n  setDate: (date: Date | undefined) => void;\n  buttonClassName?: string;\n  placeholder?: string;\n}\n\nexport function DatePicker({ date, setDate, buttonClassName, placeholder = \"اختر تاريخًا\" }: DatePickerProps) {\n  return (\n    <Popover>\n      <PopoverTrigger asChild>\n        <Button\n          variant={\"outline\"}\n          className={cn(\n            \"w-full justify-start text-right font-normal\", // Changed text-left to text-right for RTL\n            !date && \"text-muted-foreground\",\n            buttonClassName\n          )}\n        >\n          <CalendarIcon className=\"ml-2 h-4 w-4\" /> {/* Changed mr-2 to ml-2 for RTL */}\n          {date ? format(date, \"PPP\", { locale: arSA }) : <span>{placeholder}</span>}\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-auto p-0\">\n        <Calendar\n          mode=\"single\"\n          selected={date}\n          onSelect={setDate}\n          initialFocus\n          locale={arSA} // Set locale for Calendar\n          dir=\"rtl\" // Ensure calendar itself is RTL\n        />\n      </PopoverContent>\n    </Popover>\n  )\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA,6PAAuC,oBAAoB;AAC3D;AAEA;AACA;AACA;AACA;AAVA;;;;;;;;;AAuBO,SAAS,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,cAAc,EAAmB;IAC1G,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+CACA,CAAC,QAAQ,yBACT;;sCAGF,8OAAC,0MAAA,CAAA,WAAY;4BAAC,WAAU;;;;;;wBAAiB;wBACxC,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO;4BAAE,QAAQ,kJAAA,CAAA,OAAI;wBAAC,mBAAK,8OAAC;sCAAM;;;;;;;;;;;;;;;;;0BAG3D,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oBACP,MAAK;oBACL,UAAU;oBACV,UAAU;oBACV,YAAY;oBACZ,QAAQ,kJAAA,CAAA,OAAI;oBACZ,KAAI,MAAM,gCAAgC;;;;;;;;;;;;;;;;;AAKpD", "debugId": null}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/children/ChildProfileClient.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { useState, useEffect, useRef } from 'react'; // Added useRef\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { CalendarCheck2, FileText, Edit, PlusCircle, Users, AlertTriangle, Info, Briefcase, UserCircle2, ClipboardList, FilePenLine, StickyNote, Check, X, Printer, Hash, Upload } from 'lucide-react'; // Added Upload\nimport type { Child, Assessment, CalculatedAge, SessionNote, CaseStudyData, CaseStudyBasicInfo, CaseStudyPregnancyAndBirthInfo, CaseStudyReinforcerResponseInfo } from '@/lib/types';\nimport AgeDisplay from '@/components/assessment/AgeDisplay';\nimport { calculateAge, formatDate, generateUniqueId } from '@/lib/utils';\nimport { useChildren, useAssessments } from '@/hooks/use-storage';\nimport Image from 'next/image';\nimport { Separator } from '@/components/ui/separator';\nimport { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { useToast } from '@/hooks/use-toast';\nimport { useRouter } from 'next/navigation';\nimport { MOCK_CHILDREN_DATA, defaultCaseStudyData } from '@/lib/constants';\nimport { Alert, AlertTitle, AlertDescription as ShadCnAlertDescription } from \"@/components/ui/alert\";\nimport { addMonths, isPast, parseISO, differenceInDays, isValid, format } from 'date-fns';\nimport { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';\nimport { DatePicker } from '@/components/ui/date-picker';\nimport { ScrollArea } from '@/components/ui/scroll-area';\n\ninterface ChildProfileClientProps {\n  childId: string;\n}\n\nexport default function ChildProfileClient({ childId }: ChildProfileClientProps) {\n  const { getChild, updateChild } = useChildren();\n  const { assessments, loading: assessmentsLoading } = useAssessments(childId);\n\n  const [childData, setChildData] = useState<Child | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [latestAssessment, setLatestAssessment] = useState<Assessment | undefined>(undefined);\n  const [isProfileFormDialogOpen, setIsProfileFormDialogOpen] = useState(false);\n  const [profileFormData, setProfileFormData] = useState<Child | null>(null);\n  const [profileAvatarPreview, setProfileAvatarPreview] = useState<string | null>(null);\n\n  const [isSessionNoteDialogOpen, setIsSessionNoteDialogOpen] = useState(false);\n  const [currentSessionNote, setCurrentSessionNote] = useState<Partial<SessionNote>>({});\n  const [sessionNoteDate, setSessionNoteDate] = useState<Date | undefined>(new Date());\n\n  const [isCaseStudyDialogOpen, setIsCaseStudyDialogOpen] = useState(false);\n  const [currentCaseStudyData, setCurrentCaseStudyData] = useState<CaseStudyData>(defaultCaseStudyData);\n  const caseStudyPrintRef = useRef<HTMLDivElement>(null); // Ref for printing case study\n\n\n  const { toast } = useToast();\n  const router = useRouter();\n\n  const [alerts, setAlerts] = useState<{\n    needsReassessment: boolean;\n    reassessmentSoon: boolean;\n    noAssessment: boolean;\n    serviceCompleted: boolean;\n    reassessmentDueDateString: string | null;\n  }>({\n    needsReassessment: false,\n    reassessmentSoon: false,\n    noAssessment: false,\n    serviceCompleted: false,\n    reassessmentDueDateString: null,\n  });\n\n  // Initialize child data from storage\n  useEffect(() => {\n    const child = getChild(childId);\n    if (child) {\n      setChildData(child);\n      setProfileFormData(child);\n      setProfileAvatarPreview(child.avatarUrl || null);\n      setCurrentCaseStudyData(child.caseStudy ? JSON.parse(JSON.stringify(child.caseStudy)) : JSON.parse(JSON.stringify(defaultCaseStudyData)));\n    }\n    setLoading(false);\n  }, [childId, getChild]);\n\n  // Update latest assessment when assessments change\n  useEffect(() => {\n    if (assessments.length > 0) {\n      const latest = assessments.sort((a, b) => new Date(b.assessmentDate).getTime() - new Date(a.assessmentDate).getTime())[0];\n      setLatestAssessment(latest);\n    } else {\n      setLatestAssessment(undefined);\n    }\n  }, [assessments]);\n\n  useEffect(() => {\n    if (!childData) return;\n\n    const today = new Date();\n    let needsReassessmentFlag = false;\n    let reassessmentSoonFlag = false;\n    let noAssessmentFlag = false;\n    let serviceCompletedFlag = false;\n    let dueDateString: string | null = null;\n\n    const ageNow = calculateAge(childData.birthDate, today.toISOString());\n    if (ageNow.years >= 6) {\n      serviceCompletedFlag = true;\n    }\n\n    if (latestAssessment) {\n      try {\n        const lastAssessmentDate = parseISO(latestAssessment.assessmentDate);\n        if (isValid(lastAssessmentDate)) {\n          const reassessmentDueDate = addMonths(lastAssessmentDate, 4);\n          dueDateString = formatDate(reassessmentDueDate.toISOString());\n\n          if (isPast(reassessmentDueDate)) {\n            needsReassessmentFlag = true;\n          } else {\n            const daysUntilReassessment = differenceInDays(reassessmentDueDate, today);\n            if (daysUntilReassessment <= 14 && daysUntilReassessment >= 0) {\n              reassessmentSoonFlag = true;\n            }\n          }\n        } else {\n          noAssessmentFlag = true;\n        }\n      } catch (error) {\n        noAssessmentFlag = true;\n      }\n    } else {\n      noAssessmentFlag = true;\n    }\n\n    setAlerts({\n      needsReassessment: needsReassessmentFlag && !serviceCompletedFlag,\n      reassessmentSoon: reassessmentSoonFlag && !serviceCompletedFlag,\n      noAssessment: noAssessmentFlag && !serviceCompletedFlag,\n      serviceCompleted: serviceCompletedFlag,\n      reassessmentDueDateString: dueDateString,\n    });\n\n  }, [childData, latestAssessment]);\n\n\n  const handleOpenProfileFormDialog = () => {\n    if (childData) {\n      setProfileFormData(childData);\n      setProfileAvatarPreview(childData.avatarUrl || null);\n      setIsProfileFormDialogOpen(true);\n    }\n  };\n\n  const handleProfileInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setProfileFormData(prev => ({ ...prev, [name]: value } as Child));\n  };\n\n  const handleProfileGenderChange = (value: Child['gender']) => {\n    setProfileFormData(prev => ({ ...prev, gender: value }));\n  };\n\n  const handleProfileAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files && e.target.files[0]) {\n      const file = e.target.files[0];\n      const reader = new FileReader();\n      reader.onloadend = () => {\n        setProfileFormData(prev => ({ ...prev, avatarUrl: reader.result as string } as Child));\n        setProfileAvatarPreview(reader.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  const handleRemoveProfileAvatar = () => {\n    setProfileFormData(prev => ({ ...prev, avatarUrl: undefined } as Child));\n    setProfileAvatarPreview(null);\n    const fileInput = document.getElementById('avatarUrl-edit-profile') as HTMLInputElement;\n    if (fileInput) {\n      fileInput.value = \"\";\n    }\n  };\n\n  const handleProfileFormSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!profileFormData || !profileFormData.name || !profileFormData.birthDate || !profileFormData.enrollmentDate || !profileFormData.specialistName) {\n      toast({ title: \"خطأ\", description: \"يرجى ملء جميع الحقول الإلزامية.\", variant: \"destructive\" });\n      return;\n    }\n\n    let finalAvatarUrl = profileFormData.avatarUrl;\n    if (!finalAvatarUrl && !profileAvatarPreview) {\n      const initials = profileFormData.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase() || '؟؟';\n      finalAvatarUrl = `https://placehold.co/100x100.png?text=${encodeURIComponent(initials)}`;\n    } else if (!finalAvatarUrl && profileAvatarPreview) {\n      finalAvatarUrl = profileAvatarPreview;\n    }\n\n    const updatedChildDetails: Child = {\n      ...childData!,\n      name: profileFormData.name,\n      birthDate: profileFormData.birthDate,\n      enrollmentDate: profileFormData.enrollmentDate,\n      specialistName: profileFormData.specialistName,\n      avatarUrl: finalAvatarUrl,\n      gender: profileFormData.gender,\n    };\n\n    const success = updateChild(updatedChildDetails);\n    if (success) {\n      setChildData(updatedChildDetails);\n      toast({ title: \"نجاح\", description: `تم تحديث بيانات ${updatedChildDetails.name} بنجاح.` });\n      setIsProfileFormDialogOpen(false);\n    } else {\n      toast({ title: \"خطأ\", description: \"فشل في تحديث بيانات الطفل.\", variant: \"destructive\" });\n    }\n  };\n\n  // Loading and error states\n  if (loading || !childData) {\n    return (\n      <div className=\"container mx-auto py-8\">\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-muted-foreground\">جاري تحميل بيانات الطفل...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Child not found\n  if (!childData) {\n    return (\n      <div className=\"container mx-auto py-8 text-center\">\n        <h1 className=\"text-2xl font-semibold\">لم يتم العثور على الطفل</h1>\n        <Link href=\"/children\">\n          <Button variant=\"link\">العودة إلى قائمة الأطفال</Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const fallbackName = childData.name.split(' ').map(n => n[0]).join('').toUpperCase() || 'C';\n\n  function isValidISO(dateString: string | undefined | null): boolean {\n    if (!dateString) return false;\n    try {\n      return isValid(parseISO(dateString));\n    } catch {\n      return false;\n    }\n  }\n\n  const handleOpenSessionNoteDialog = () => {\n    setCurrentSessionNote({});\n    setSessionNoteDate(new Date());\n    setIsSessionNoteDialogOpen(true);\n  };\n\n  const handleSessionNoteInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setCurrentSessionNote(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSaveSessionNote = () => {\n    if (!sessionNoteDate || !currentSessionNote.goalDiscussed || !currentSessionNote.attendees || !currentSessionNote.notes) {\n      toast({ title: \"خطأ\", description: \"يرجى ملء جميع الحقول المطلوبة للمحضر.\", variant: \"destructive\" });\n      return;\n    }\n    const newNote: SessionNote = {\n      id: generateUniqueId('note'),\n      date: format(sessionNoteDate, \"yyyy-MM-dd\"),\n      goalDiscussed: currentSessionNote.goalDiscussed || \"\",\n      attendees: currentSessionNote.attendees || \"\",\n      notes: currentSessionNote.notes || \"\",\n      nextSteps: currentSessionNote.nextSteps,\n    };\n\n    const updatedChild = {\n      ...childData!,\n      sessionNotes: [...(childData!.sessionNotes || []), newNote]\n    };\n\n    const success = updateChild(updatedChild);\n    if (success) {\n      setChildData(updatedChild);\n      toast({ title: \"نجاح\", description: \"تم حفظ محضر الجلسة بنجاح.\" });\n      setIsSessionNoteDialogOpen(false);\n    } else {\n      toast({ title: \"خطأ\", description: \"فشل في حفظ محضر الجلسة.\", variant: \"destructive\" });\n    }\n  };\n\n  const handleOpenCaseStudyDialog = () => {\n    if (!childData) return;\n\n    const ageNow = calculateAge(childData.birthDate);\n    const currentAgeString = `${ageNow.years} سنوات و ${ageNow.months} أشهر`;\n\n    let newCaseStudyDataState: CaseStudyData = JSON.parse(JSON.stringify(defaultCaseStudyData));\n\n    if (childData.caseStudy) {\n      newCaseStudyDataState = {\n        basicInfo: {\n          ...newCaseStudyDataState.basicInfo,\n          ...(childData.caseStudy.basicInfo || {}),\n        },\n        pregnancyAndBirthInfo: {\n          ...newCaseStudyDataState.pregnancyAndBirthInfo,\n          ...(childData.caseStudy.pregnancyAndBirthInfo || {}),\n        },\n        reinforcerResponseInfo: {\n          ...newCaseStudyDataState.reinforcerResponseInfo,\n          ...(childData.caseStudy.reinforcerResponseInfo || {}),\n        },\n      };\n    }\n\n    newCaseStudyDataState.basicInfo.childName = childData.name || newCaseStudyDataState.basicInfo.childName || \"\";\n    newCaseStudyDataState.basicInfo.birthDate = childData.birthDate || newCaseStudyDataState.basicInfo.birthDate || \"\";\n    newCaseStudyDataState.basicInfo.currentAge = currentAgeString;\n    newCaseStudyDataState.basicInfo.gender = childData.gender || newCaseStudyDataState.basicInfo.gender || \"unknown\";\n\n    setCurrentCaseStudyData(newCaseStudyDataState);\n    setIsCaseStudyDialogOpen(true);\n  };\n\n\n  const handleCaseStudyInputChange = (section: keyof CaseStudyData, field: string, value: string | Date | undefined) => {\n    setCurrentCaseStudyData(prev => {\n        const newState = JSON.parse(JSON.stringify(prev));\n        if (!newState[section]) {\n            newState[section] = {} as any;\n        }\n        (newState[section] as any)[field] = value instanceof Date ? format(value, \"yyyy-MM-dd\") : value;\n        return newState;\n    });\n  };\n\n  const handleSaveCaseStudy = () => {\n    const updatedChild = {\n      ...childData!,\n      caseStudy: currentCaseStudyData\n    };\n\n    const success = updateChild(updatedChild);\n    if (success) {\n      setChildData(updatedChild);\n      toast({ title: \"نجاح\", description: \"تم حفظ دراسة الحالة بنجاح.\" });\n      setIsCaseStudyDialogOpen(false);\n    } else {\n      toast({ title: \"خطأ\", description: \"فشل في حفظ دراسة الحالة.\", variant: \"destructive\" });\n    }\n  };\n\n  const renderCaseStudySection = (title: string, data?: Record<string, string | undefined>) => {\n    if (!data || Object.values(data).every(val => !val)) {\n      return (\n        <Card>\n          <CardHeader><CardTitle>{title}</CardTitle></CardHeader>\n          <CardContent><p className=\"text-muted-foreground\">لا توجد بيانات متاحة لهذا القسم.</p></CardContent>\n        </Card>\n      );\n    }\n\n    const keyMap: Record<string, string> = {\n        childName: \"اسم الطفل:\",\n        birthDate: \"تاريخ الميلاد:\",\n        currentAge: \"العمر الحالي (بالسنوات والأشهر):\",\n        gender: \"الجنس:\",\n        guardianName: \"اسم ولي الأمر (الأم/الأب/الوصي):\",\n        guardianPhoneNumber: \"رقم هاتف ولي الأمر:\",\n        homeAddress: \"عنوان المنزل:\",\n        guardianRelationship: \"علاقة ولي الأمر بالطفل:\",\n        hasSiblings: \"هل هناك إخوة أو أخوات للطفل؟\",\n        siblingsInfo: \"إذا كان نعم، اذكر أسماءهم وأعمارهم:\",\n        motherAgeAtPregnancy: \"كم كان عمر الأم عندما حملت بهذا الطفل؟\",\n        fullTermPregnancy: \"هل كان الحمل كاملاً (9 أشهر) أم حدثت ولادة مبكرة؟\",\n        prematureBirthMonth: \"إذا كانت ولادة مبكرة، في أي شهر؟\",\n        motherHealthIssuesDuringPregnancy: \"هل واجهت الأم أي مشاكل صحية خلال فترة الحمل؟ (مثل: سكري الحمل، ارتفاع ضغط الدم، أخذ أدوية معينة، إلخ.)\",\n        motherHealthIssuesDetails: \"إذا كان نعم، يرجى التوضيح (مشاكل صحية للأم):\",\n        deliveryType: \"هل حدثت الولادة بشكل طبيعي أم قيصري؟\",\n        childHealthIssuesAtBirth: \"هل واجه الطفل أي مشاكل صحية عند الولادة أو بعدها مباشرة؟ (مثل: نقص الأكسجين، مشاكل في التنفس، الحاجة للبقاء في الحضانة، إلخ.)\",\n        childHealthIssuesDetails: \"إذا كان نعم، يرجى التوضيح (مشاكل صحية للطفل):\",\n        favoriteToys: \"ما هي الأشياء التي يحب الطفل اللعب بها أو يحبها كثيراً؟ (مثل: ألعاب معينة، كتب، فقاعات، كرات، سيارات، حيوانات محشوة، إلخ.)\",\n        enjoyableActivities: \"ما هي الأنشطة التي يستمتع بها الطفل؟ (مثل: الاستماع للموسيقى، الرقص، المشي في الخارج، مشاهدة التلفزيون، الرسم، اللعب بالماء، إلخ.)\",\n        favoriteFoods: \"هل هناك أطعمة معينة يحبها الطفل جداً؟ (مثل: بسكويت، فواكه، حلويات، عصير، إلخ.)\",\n        happinessExpression: \"ماذا يفعل الطفل عندما يكون سعيداً أو متحفزاً؟ (مثل: يبتسم، يضحك، يصفق، يقفز، يصدر أصواتاً، يحاول الاقتراب من الشيء الذي يحبه، إلخ.)\",\n        motivationMethods: \"عندما تريد من الطفل أن يفعل شيئاً ما (مثل: يجلس، ينظر إليك)، ما الذي تستخدمه لتحفيزه أو مكافأته عادةً؟ (مثال: إعطاء لعبة يحبها، التصفير، الحضن، قول \\\"أحسنت\\\"، تشغيل أغنية، إلخ.)\",\n        smilesAtGuardian: \"هل يبتسم الطفل عندما يراك أو يسمع صوتك؟\",\n        communicatesNeeds: \"هل يحاول الطفل التواصل معك عندما يحتاج شيئًا؟ (مثل: الإشارة، النظر إليك، إصدار أصوات، البكاء بطريقة معينة، إلخ.)\",\n    };\n\n\n    return (\n      <Card>\n        <CardHeader><CardTitle>{title}</CardTitle></CardHeader>\n        <CardContent className=\"space-y-2 text-sm\">\n          {Object.entries(data).map(([key, value]) => {\n            const displayValue = value || \"غير محدد\";\n            const label = keyMap[key] || key;\n            if (key === 'gender') {\n                const genderLabel = displayValue === 'male' ? 'ذكر' : displayValue === 'female' ? 'أنثى' : displayValue === 'other' ? 'آخر' : 'غير معروف';\n                return <p key={key}><strong>{label}</strong> {genderLabel}</p>;\n            }\n             if (key === 'hasSiblings' || key === 'fullTermPregnancy' || key === 'motherHealthIssuesDuringPregnancy' || key === 'childHealthIssuesAtBirth' || key === 'smilesAtGuardian' || key === 'communicatesNeeds') {\n                const boolLabel = displayValue === 'yes' ? 'نعم' : displayValue === 'no' ? 'لا' : displayValue === 'sometimes' ? 'أحياناً' : 'غير محدد';\n                return <p key={key}><strong>{label}</strong> {boolLabel}</p>;\n            }\n            if (key === 'deliveryType') {\n                const deliveryLabel = displayValue === 'natural' ? 'طبيعي' : displayValue === 'cesarean' ? 'قيصري' : 'غير محدد';\n                return <p key={key}><strong>{label}</strong> {deliveryLabel}</p>;\n            }\n            if (value && (key.toLowerCase().includes('date') || key === 'birthDate')) {\n                return <p key={key}><strong>{label}</strong> {formatDate(value)}</p>;\n            }\n            if (key.toLowerCase().includes('details') || key === 'siblingsInfo' || key === 'favoriteToys' || key === 'enjoyableActivities' || key === 'favoriteFoods' || key === 'happinessExpression' || key === 'motivationMethods') {\n              return (\n                <div key={key} className=\"space-y-0.5\">\n                  <strong>{label}</strong>\n                  <pre className=\"whitespace-pre-wrap font-sans text-sm p-2 bg-muted/30 rounded-md border\">{displayValue}</pre>\n                </div>\n              );\n            }\n            return <p key={key}><strong>{label}</strong> {displayValue}</p>;\n          })}\n        </CardContent>\n      </Card>\n    );\n  };\n\n  const handlePrintCaseStudy = () => {\n    if (!caseStudyPrintRef.current) {\n        toast({ title: \"خطأ في الطباعة\", description: \"لم يتم العثور على محتوى دراسة الحالة للطباعة.\", variant: \"destructive\" });\n        return;\n    }\n\n    const printWindow = window.open('', '_blank');\n    if (printWindow) {\n        printWindow.document.write('<html><head><title>' + `دراسة حالة لـ ${childData.name}` + '</title>');\n        printWindow.document.write(`\n            <style>\n              body { font-family: 'Tajawal', 'Noto Sans Arabic', system-ui, sans-serif; direction: rtl; padding: 20px; margin: 0; }\n              h1, h2, h3 { color: #333; margin-bottom: 0.8em; margin-top: 1.2em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }\n              h1 { font-size: 1.8em; }\n              h2 { font-size: 1.5em; }\n              h3 { font-size: 1.2em; color: hsl(var(--primary)); }\n              p { margin-bottom: 0.8em; line-height: 1.6; }\n              pre { white-space: pre-wrap; font-family: inherit; background-color: #f9f9f9; border: 1px solid #eee; padding: 10px; border-radius: 4px; margin-top: 0.3em; }\n              strong { font-weight: bold; }\n              .print-section { margin-bottom: 20px; page-break-inside: avoid; }\n              .print-section-title { font-size: 1.3em; font-weight: bold; color: hsl(var(--accent)); margin-bottom: 10px; }\n              .print-item { margin-bottom: 5px; }\n              .print-item strong { display: block; margin-bottom: 2px; color: #555; }\n              @media print {\n                body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }\n                @page { margin: 20mm; }\n                button, .no-print { display: none !important; }\n              }\n            </style>\n        `);\n        printWindow.document.write('</head><body dir=\"rtl\">');\n        printWindow.document.write('<h1>' + `دراسة حالة لـ ${childData.name}` + '</h1>');\n\n        const sections = caseStudyPrintRef.current.querySelectorAll('div.print-section-card > div > h3, div.print-section-card > div > div > p, div.print-section-card > div > div > div');\n\n        let currentSectionHtml = \"\";\n        let currentSectionTitle = \"\";\n\n        sections.forEach(el => {\n            if (el.tagName.toLowerCase() === 'h3') { // This is CardTitle inside CardHeader\n                if (currentSectionHtml) { // Print previous section\n                    printWindow.document.write('<h2>' + currentSectionTitle + '</h2>');\n                    printWindow.document.write('<div class=\"print-section\">' + currentSectionHtml + '</div>');\n                }\n                currentSectionTitle = el.textContent || \"قسم\";\n                currentSectionHtml = \"\"; // Reset for new section\n            } else {\n                 // For p and div elements containing label and value\n                 const labelElement = el.querySelector('strong');\n                 const valueElement = labelElement ? labelElement.nextSibling : el.querySelector('pre') || el; // Get text node or pre\n\n                 if (labelElement && valueElement) {\n                     currentSectionHtml += '<div class=\"print-item\"><strong>' + labelElement.textContent + '</strong>';\n                     if(valueElement.nodeName === \"PRE\") {\n                         currentSectionHtml += '<pre>' + valueElement.textContent + '</pre></div>';\n                     } else {\n                          currentSectionHtml += (valueElement.textContent || \"\") + '</div>';\n                     }\n                 } else if (el.textContent?.trim()) { // Fallback for simple p tags\n                     currentSectionHtml += `<p>${el.textContent}</p>`;\n                 }\n            }\n        });\n\n        if (currentSectionHtml) { // Print the last section\n            printWindow.document.write('<h2>' + currentSectionTitle + '</h2>');\n            printWindow.document.write('<div class=\"print-section\">' + currentSectionHtml + '</div>');\n        }\n\n        printWindow.document.write('</body></html>');\n        printWindow.document.close();\n        printWindow.focus();\n        printWindow.print();\n    } else {\n        toast({ title: \"خطأ\", description: \"يرجى السماح بالنوافذ المنبثقة لطباعة هذا المحتوى.\", variant: \"destructive\"});\n    }\n  };\n\n\n  return (\n    <>\n      <div className=\"container mx-auto py-8 space-y-8\">\n        <Card className=\"overflow-hidden shadow-lg\">\n          <CardHeader className=\"bg-muted/30 p-6 flex flex-col sm:flex-row items-start sm:items-center gap-6\">\n            <Avatar className=\"h-28 w-28 border-4 border-primary shadow-lg\">\n              <AvatarImage src={childData.avatarUrl} alt={childData.name} data-ai-hint=\"child portrait\" className=\"object-cover\" />\n              <AvatarFallback className=\"text-3xl font-semibold bg-gradient-to-br from-primary/20 to-primary/10\">{fallbackName}</AvatarFallback>\n            </Avatar>\n            <div className=\"flex-1\">\n              <div className=\"flex items-center gap-3 mb-2\">\n                <Badge variant=\"outline\" className=\"text-sm font-mono\">\n                  <Hash className=\"h-4 w-4 mr-1\" />\n                  {childData.childIdNumber}\n                </Badge>\n              </div>\n              <CardTitle className=\"text-3xl font-bold text-primary\">{childData.name}</CardTitle>\n              <div className=\"text-muted-foreground mt-1\">\n                <p className=\"text-sm flex items-center gap-1\"><UserCircle2 className=\"h-4 w-4\" />الجنس: {childData.gender === 'male' ? 'ذكر' : childData.gender === 'female' ? 'أنثى' : childData.gender === 'other' ? 'آخر' : 'غير معروف'}</p>\n                <AgeDisplay birthDate={childData.birthDate} label=\"العمر الحالي:\" className=\"text-lg\" />\n                <p className=\"text-sm\">تاريخ الميلاد: {formatDate(childData.birthDate)}</p>\n              </div>\n            </div>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleOpenProfileFormDialog}>\n              <Edit className=\"ml-2 h-4 w-4\" /> تعديل الملف الشخصي\n            </Button>\n          </CardHeader>\n          <CardContent className=\"p-6 grid md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"text-lg font-semibold mb-2 text-foreground/90\">تفاصيل التسجيل</h3>\n              <div className=\"space-y-1 text-sm\">\n                <p className=\"flex items-center gap-1\"><Briefcase className=\"h-4 w-4 text-accent\" /> <strong className=\"text-muted-foreground\">تاريخ الالتحاق:</strong> {isValidISO(childData.enrollmentDate) ? formatDate(childData.enrollmentDate) : 'غير محدد'}</p>\n                <p className=\"flex items-center gap-1\"><Users className=\"h-4 w-4 text-accent\" /> <strong className=\"text-muted-foreground\">الأخصائي:</strong> {childData.specialistName}</p>\n              </div>\n            </div>\n            {latestAssessment && isValidISO(latestAssessment.assessmentDate) ? (\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2 text-foreground/90\">آخر تقييم</h3>\n                <p className=\"text-sm\"><strong className=\"text-muted-foreground\">التاريخ:</strong> {formatDate(latestAssessment.assessmentDate)}</p>\n                <AgeDisplay birthDate={childData.birthDate} assessmentDate={latestAssessment.assessmentDate} label=\"العمر عند التقييم:\" className=\"text-sm\" />\n              </div>\n            ) : (\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2 text-foreground/90\">آخر تقييم</h3>\n                <p className=\"text-sm text-muted-foreground\">لا يوجد تقييم سابق لهذا الطفل أو تاريخ التقييم غير صالح.</p>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        <div className=\"space-y-4\">\n          {alerts.serviceCompleted && (\n            <Alert variant=\"default\" className=\"bg-yellow-100 border-yellow-500 text-yellow-700 dark:bg-yellow-900/30 dark:border-yellow-700 dark:text-yellow-300\">\n              <Info className=\"h-5 w-5 text-yellow-600 dark:text-yellow-400\" />\n              <AlertTitle className=\"font-semibold\">اكتمال الخدمة (محتمل)</AlertTitle>\n              <ShadCnAlertDescription>\n                بلغ الطفل {childData.name} عمر 6 سنوات أو أكثر. يرجى مراجعة استمرارية الخدمة المقدمة له.\n              </ShadCnAlertDescription>\n            </Alert>\n          )}\n\n          {!alerts.serviceCompleted && alerts.noAssessment && (\n            <Alert variant=\"default\" className=\"bg-blue-100 border-blue-500 text-blue-700 dark:bg-blue-900/30 dark:border-blue-700 dark:text-blue-300\">\n              <Info className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n              <AlertTitle className=\"font-semibold\">تقييم أولي مطلوب</AlertTitle>\n              <ShadCnAlertDescription>\n                لا يوجد تقييم حالي للطفل {childData.name}. يرجى إجراء تقييم أولي لتتبع تطوره.\n              </ShadCnAlertDescription>\n            </Alert>\n          )}\n\n          {!alerts.serviceCompleted && alerts.needsReassessment && (\n            <Alert variant=\"destructive\">\n              <AlertTriangle className=\"h-5 w-5\" />\n              <AlertTitle className=\"font-semibold\">إعادة التقييم مطلوبة</AlertTitle>\n              <ShadCnAlertDescription>\n                لقد حان موعد إعادة تقييم الطفل {childData.name}. مر على آخر تقييم أكثر من 4 أشهر (آخر تقييم في: {latestAssessment ? formatDate(latestAssessment.assessmentDate) : 'غير معروف'}).\n              </ShadCnAlertDescription>\n            </Alert>\n          )}\n\n          {!alerts.serviceCompleted && alerts.reassessmentSoon && !alerts.needsReassessment && (\n            <Alert variant=\"default\" className=\"bg-orange-100 border-orange-500 text-orange-700 dark:bg-orange-900/30 dark:border-orange-700 dark:text-orange-300\">\n              <AlertTriangle className=\"h-5 w-5 text-orange-600 dark:text-orange-400\" />\n              <AlertTitle className=\"font-semibold\">تذكير بإعادة التقييم</AlertTitle>\n              <ShadCnAlertDescription>\n                يقترب موعد إعادة تقييم الطفل {childData.name}. الموعد المتوقع: {alerts.reassessmentDueDateString}.\n              </ShadCnAlertDescription>\n            </Alert>\n          )}\n        </div>\n\n        <Separator />\n\n        <Card className=\"shadow-lg\">\n          <CardHeader className=\"flex flex-row justify-between items-center\">\n            <div className=\"flex items-center gap-2\">\n              <ClipboardList className=\"h-6 w-6 text-accent\" />\n              <CardTitle>توثيق الجلسات / محاضر الاجتماعات</CardTitle>\n            </div>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleOpenSessionNoteDialog}>\n              <PlusCircle className=\"ml-2 h-4 w-4\" /> إضافة محضر جديد\n            </Button>\n          </CardHeader>\n          <CardContent>\n            {childData.sessionNotes && childData.sessionNotes.length > 0 ? (\n              <ScrollArea className=\"h-[300px] pr-3\">\n                <div className=\"space-y-4\">\n                  {childData.sessionNotes.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).map(note => (\n                    <Card key={note.id} className=\"bg-muted/50\">\n                      <CardHeader className=\"pb-2 pt-3 px-4\">\n                        <CardTitle className=\"text-md\">\n                          تاريخ الجلسة: {formatDate(note.date)}\n                        </CardTitle>\n                        <CardDescription className=\"text-xs\">الحضور: {note.attendees}</CardDescription>\n                      </CardHeader>\n                      <CardContent className=\"px-4 pb-3 space-y-1 text-sm\">\n                        <div><strong>الهدف المناقش:</strong> {note.goalDiscussed}</div>\n                        <div><strong>الملاحظات:</strong> <pre className=\"whitespace-pre-wrap font-sans text-sm p-2 bg-background border rounded-md\">{note.notes}</pre></div>\n                        {note.nextSteps && <div><strong>الخطوات التالية:</strong> <pre className=\"whitespace-pre-wrap font-sans text-sm p-2 bg-background border rounded-md\">{note.nextSteps}</pre></div>}\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              </ScrollArea>\n            ) : (\n              <p className=\"text-muted-foreground text-center py-4\">لا توجد محاضر جلسات مسجلة لهذا الطفل.</p>\n            )}\n          </CardContent>\n        </Card>\n\n        <Card className=\"shadow-lg\">\n          <CardHeader className=\"flex flex-row justify-between items-center\">\n            <div className=\"flex items-center gap-2\">\n              <FilePenLine className=\"h-6 w-6 text-accent\" />\n              <CardTitle>دراسة حالة</CardTitle>\n            </div>\n            <Button variant=\"outline\" size=\"sm\" onClick={handleOpenCaseStudyDialog}>\n              {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? <Edit className=\"ml-2 h-4 w-4\" /> : <PlusCircle className=\"ml-2 h-4 w-4\" />}\n              {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? \"تعديل دراسة الحالة\" : \"إضافة دراسة حالة\"}\n            </Button>\n          </CardHeader>\n          <CardContent className=\"space-y-4\" ref={caseStudyPrintRef}> {/* Added ref here */}\n            {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? (\n              <>\n                <div className=\"print-section-card\">{renderCaseStudySection(\"معلومات أساسية عن الطفل والأسرة\", childData.caseStudy.basicInfo)}</div>\n                <div className=\"print-section-card\">{renderCaseStudySection(\"معلومات عن فترة الحمل والولادة\", childData.caseStudy.pregnancyAndBirthInfo)}</div>\n                <div className=\"print-section-card\">{renderCaseStudySection(\"استجابة الطفل للمعززات والمكافآت\", childData.caseStudy.reinforcerResponseInfo)}</div>\n              </>\n            ) : (\n              <p className=\"text-muted-foreground text-center py-4\">لم يتم إدخال بيانات دراسة الحالة لهذا الطفل بعد.</p>\n            )}\n          </CardContent>\n        </Card>\n\n        <Separator />\n\n        <div className=\"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2\">\n          <Card className=\"shadow-md hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <CalendarCheck2 className=\"h-6 w-6 text-accent\" />\n                التقييمات\n              </CardTitle>\n              <CardDescription>إدارة ومراجعة تقييمات بورتيج لـ {childData.name}.</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Image src=\"https://placehold.co/600x300.png\" alt=\"توضيح التقييم\" width={600} height={300} className=\"rounded-md mb-4\" data-ai-hint=\"checklist form\" />\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                إجمالي التقييمات: {assessments.length}\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-2\">\n                <Link href={`/children/${childData.id}/assessment/new`} passHref>\n                  <Button className=\"w-full sm:w-auto\">\n                    <PlusCircle className=\"ml-2 h-4 w-4\" /> بدء تقييم جديد\n                  </Button>\n                </Link>\n                <Link href={`/children/${childData.id}/assessment`} passHref>\n                  <Button variant=\"outline\" className=\"w-full sm:w-auto\">عرض سجل التقييمات</Button>\n                </Link>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"shadow-md hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <FileText className=\"h-6 w-6 text-accent\" />\n                التقرير الشامل\n              </CardTitle>\n              <CardDescription>إنشاء وعرض تقارير شاملة مع تحليل للبيانات.</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Image src=\"https://placehold.co/600x300.png\" alt=\"توضيح التقرير الشامل\" width={600} height={300} className=\"rounded-md mb-4\" data-ai-hint=\"report document\" />\n              <p className=\"text-sm text-muted-foreground mb-4\">\n                استخدم الذكاء الاصطناعي لإنشاء تقارير مفصلة وتحليل لنتائج التقييم.\n              </p>\n              <Link href={`/children/${childData.id}/plan`} passHref>\n                <Button className=\"w-full\">\n                  الانتقال إلى التقارير\n                </Button>\n              </Link>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Profile Edit Dialog */}\n      <Dialog open={isProfileFormDialogOpen} onOpenChange={setIsProfileFormDialogOpen}>\n        <DialogContent className=\"sm:max-w-[425px]\">\n          <DialogHeader>\n            <DialogTitle>تعديل بيانات الطفل</DialogTitle>\n          </DialogHeader>\n          <form onSubmit={handleProfileFormSubmit}>\n            <div className=\"grid gap-4 py-4\">\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"childIdNumber-edit-profile\" className=\"text-right\">رقم الطفل</Label>\n                <Input\n                  id=\"childIdNumber-edit-profile\"\n                  name=\"childIdNumber\"\n                  value={profileFormData.childIdNumber || ''}\n                  onChange={handleProfileInputChange}\n                  className=\"col-span-3 font-mono\"\n                  placeholder=\"CH-2024-001\"\n                  required\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"name-edit-profile\" className=\"text-right\">الاسم</Label>\n                <Input id=\"name-edit-profile\" name=\"name\" value={profileFormData.name || ''} onChange={handleProfileInputChange} className=\"col-span-3\" required />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"birthDate-edit-profile\" className=\"text-right\">تاريخ الميلاد</Label>\n                <Input id=\"birthDate-edit-profile\" name=\"birthDate\" type=\"date\" value={profileFormData.birthDate || ''} onChange={handleProfileInputChange} className=\"col-span-3\" required />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"enrollmentDate-edit-profile\" className=\"text-right\">تاريخ الالتحاق</Label>\n                <Input id=\"enrollmentDate-edit-profile\" name=\"enrollmentDate\" type=\"date\" value={profileFormData.enrollmentDate || ''} onChange={handleProfileInputChange} className=\"col-span-3\" required />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"specialistName-edit-profile\" className=\"text-right\">الأخصائي</Label>\n                <Input id=\"specialistName-edit-profile\" name=\"specialistName\" value={profileFormData.specialistName || ''} onChange={handleProfileInputChange} className=\"col-span-3\" required />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"gender-edit-profile\" className=\"text-right\">الجنس</Label>\n                <Select value={profileFormData.gender} onValueChange={(value) => handleProfileGenderChange(value as Child['gender'])}>\n                  <SelectTrigger id=\"gender-edit-profile\" className=\"col-span-3\">\n                    <SelectValue placeholder=\"اختر الجنس\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"male\">ذكر</SelectItem>\n                    <SelectItem value=\"female\">أنثى</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n              <div className=\"grid grid-cols-4 items-start gap-4\">\n                <Label htmlFor=\"avatarUrl-edit-profile\" className=\"text-right pt-2\">صورة شخصية</Label>\n                <div className=\"col-span-3 space-y-3\">\n                  <div className=\"flex items-center gap-3\">\n                    {profileAvatarPreview ? (\n                      <div className=\"relative h-16 w-16 rounded-full overflow-hidden border-2 border-primary shadow-md\">\n                        <Image src={profileAvatarPreview} alt=\"معاينة الصورة\" fill className=\"object-cover\" />\n                      </div>\n                    ) : (\n                      <div className=\"h-16 w-16 rounded-full bg-muted border-2 border-dashed border-muted-foreground/30 flex items-center justify-center\">\n                        <Upload className=\"h-6 w-6 text-muted-foreground\" />\n                      </div>\n                    )}\n                    <div className=\"flex-1\">\n                      <Input\n                        id=\"avatarUrl-edit-profile\"\n                        name=\"avatarUrl\"\n                        type=\"file\"\n                        accept=\"image/*\"\n                        onChange={handleProfileAvatarChange}\n                        className=\"text-sm\"\n                      />\n                      <p className=\"text-xs text-muted-foreground mt-1\">\n                        اختر صورة بصيغة JPG أو PNG\n                      </p>\n                    </div>\n                  </div>\n                  {profileAvatarPreview && (\n                    <Button\n                      type=\"button\"\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={handleRemoveProfileAvatar}\n                      className=\"text-destructive hover:text-destructive\"\n                    >\n                      <X className=\"h-4 w-4 mr-1\" />\n                      إزالة الصورة\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </div>\n            <DialogFooter>\n              <DialogClose asChild>\n                <Button type=\"button\" variant=\"outline\">إلغاء</Button>\n              </DialogClose>\n              <Button type=\"submit\">حفظ التعديلات</Button>\n            </DialogFooter>\n          </form>\n        </DialogContent>\n      </Dialog>\n\n      {/* Session Note Dialog */}\n      <Dialog open={isSessionNoteDialogOpen} onOpenChange={setIsSessionNoteDialogOpen}>\n        <DialogContent className=\"sm:max-w-lg\">\n          <DialogHeader>\n            <DialogTitle>إضافة محضر جلسة جديد</DialogTitle>\n            <DialogDescription>\n              قم بتوثيق تفاصيل الجلسة مع الأسرة أو الفريق.\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"session-date\" className=\"text-right\">\n                التاريخ\n              </Label>\n              <DatePicker\n                date={sessionNoteDate}\n                setDate={setSessionNoteDate}\n                buttonClassName=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"session-goal\" className=\"text-right\">\n                الهدف المناقش\n              </Label>\n              <Textarea\n                id=\"session-goal\"\n                name=\"goalDiscussed\"\n                value={currentSessionNote.goalDiscussed || \"\"}\n                onChange={handleSessionNoteInputChange}\n                className=\"col-span-3 min-h-[80px]\"\n                required\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"session-attendees\" className=\"text-right\">\n                الحضور\n              </Label>\n              <Input\n                id=\"session-attendees\"\n                name=\"attendees\"\n                value={currentSessionNote.attendees || \"\"}\n                onChange={handleSessionNoteInputChange}\n                className=\"col-span-3\"\n                placeholder=\"مثال: الأم, الأب, الأخصائي\"\n                required\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"session-notes\" className=\"text-right\">\n                الملاحظات\n              </Label>\n              <Textarea\n                id=\"session-notes\"\n                name=\"notes\"\n                value={currentSessionNote.notes || \"\"}\n                onChange={handleSessionNoteInputChange}\n                className=\"col-span-3 min-h-[100px]\"\n                required\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"session-nextSteps\" className=\"text-right\">\n                الخطوات التالية (اختياري)\n              </Label>\n              <Textarea\n                id=\"session-nextSteps\"\n                name=\"nextSteps\"\n                value={currentSessionNote.nextSteps || \"\"}\n                onChange={handleSessionNoteInputChange}\n                className=\"col-span-3 min-h-[80px]\"\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <DialogClose asChild>\n              <Button type=\"button\" variant=\"outline\">إلغاء</Button>\n            </DialogClose>\n            <Button type=\"button\" onClick={handleSaveSessionNote}>حفظ المحضر</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Case Study Dialog */}\n      <Dialog open={isCaseStudyDialogOpen} onOpenChange={setIsCaseStudyDialogOpen}>\n        <DialogContent className=\"max-w-2xl max-h-[90vh]\">\n          <DialogHeader>\n            <DialogTitle>\n              {childData.caseStudy && Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val)) ? \"تعديل دراسة الحالة\" : \"إضافة دراسة حالة\"} لـ {childData.name}\n            </DialogTitle>\n            <DialogDescription>\n              املأ المعلومات التالية لتوثيق دراسة حالة الطفل.\n            </DialogDescription>\n          </DialogHeader>\n          <ScrollArea className=\"h-[70vh] p-1\">\n            <div className=\"space-y-6 py-4 pr-3\"> {/* Removed ref from here */}\n              {/* Basic Info Card */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>معلومات أساسية عن الطفل والأسرة</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"cs-childName\">اسم الطفل</Label>\n                    <Input id=\"cs-childName\" value={currentCaseStudyData.basicInfo?.childName || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"childName\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-birthDate\">تاريخ الميلاد</Label>\n                    <DatePicker date={currentCaseStudyData.basicInfo?.birthDate ? parseISO(currentCaseStudyData.basicInfo.birthDate) : undefined} setDate={(date) => handleCaseStudyInputChange(\"basicInfo\", \"birthDate\", date)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-currentAge\">العمر الحالي (بالسنوات والأشهر)</Label>\n                    <Input id=\"cs-currentAge\" value={currentCaseStudyData.basicInfo?.currentAge || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"currentAge\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-gender\">الجنس</Label>\n                    <Select value={currentCaseStudyData.basicInfo?.gender || \"unknown\"} onValueChange={(value) => handleCaseStudyInputChange(\"basicInfo\", \"gender\", value as Child['gender'])}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"male\">ذكر</SelectItem>\n                        <SelectItem value=\"female\">أنثى</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-guardianName\">اسم ولي الأمر (الأم/الأب/الوصي)</Label>\n                    <Input id=\"cs-guardianName\" value={currentCaseStudyData.basicInfo?.guardianName || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"guardianName\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-guardianPhoneNumber\">رقم هاتف ولي الأمر</Label>\n                    <Input id=\"cs-guardianPhoneNumber\" value={currentCaseStudyData.basicInfo?.guardianPhoneNumber || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"guardianPhoneNumber\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-homeAddress\">عنوان المنزل</Label>\n                    <Input id=\"cs-homeAddress\" value={currentCaseStudyData.basicInfo?.homeAddress || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"homeAddress\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-guardianRelationship\">علاقة ولي الأمر بالطفل</Label>\n                    <Input id=\"cs-guardianRelationship\" value={currentCaseStudyData.basicInfo?.guardianRelationship || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"guardianRelationship\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-hasSiblings\">هل هناك إخوة أو أخوات للطفل؟</Label>\n                    <Select value={currentCaseStudyData.basicInfo?.hasSiblings || \"no\"} onValueChange={(value) => handleCaseStudyInputChange(\"basicInfo\", \"hasSiblings\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"yes\">نعم</SelectItem>\n                        <SelectItem value=\"no\">لا</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  {currentCaseStudyData.basicInfo?.hasSiblings === \"yes\" && (\n                    <div>\n                      <Label htmlFor=\"cs-siblingsInfo\">إذا كان نعم، اذكر أسماءهم وأعمارهم</Label>\n                      <Textarea id=\"cs-siblingsInfo\" value={currentCaseStudyData.basicInfo?.siblingsInfo || \"\"} onChange={(e) => handleCaseStudyInputChange(\"basicInfo\", \"siblingsInfo\", e.target.value)} />\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Pregnancy and Birth Info Card */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>معلومات عن فترة الحمل والولادة</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"cs-motherAgeAtPregnancy\">كم كان عمر الأم عندما حملت بهذا الطفل؟</Label>\n                    <Input id=\"cs-motherAgeAtPregnancy\" value={currentCaseStudyData.pregnancyAndBirthInfo?.motherAgeAtPregnancy || \"\"} onChange={(e) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"motherAgeAtPregnancy\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-fullTermPregnancy\">هل كان الحمل كاملاً (9 أشهر) أم حدثت ولادة مبكرة؟</Label>\n                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.fullTermPregnancy || \"yes\"} onValueChange={(value) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"fullTermPregnancy\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"yes\">حمل كامل</SelectItem>\n                        <SelectItem value=\"no\">ولادة مبكرة</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  {currentCaseStudyData.pregnancyAndBirthInfo?.fullTermPregnancy === \"no\" && (\n                    <div>\n                      <Label htmlFor=\"cs-prematureBirthMonth\">إذا كانت ولادة مبكرة، في أي شهر؟</Label>\n                      <Input id=\"cs-prematureBirthMonth\" value={currentCaseStudyData.pregnancyAndBirthInfo?.prematureBirthMonth || \"\"} onChange={(e) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"prematureBirthMonth\", e.target.value)} />\n                    </div>\n                  )}\n                  <div>\n                    <Label htmlFor=\"cs-motherHealthIssuesDuringPregnancy\">هل واجهت الأم أي مشاكل صحية خلال فترة الحمل؟</Label>\n                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.motherHealthIssuesDuringPregnancy || \"no\"} onValueChange={(value) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"motherHealthIssuesDuringPregnancy\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"yes\">نعم</SelectItem>\n                        <SelectItem value=\"no\">لا</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  {currentCaseStudyData.pregnancyAndBirthInfo?.motherHealthIssuesDuringPregnancy === \"yes\" && (\n                    <div>\n                      <Label htmlFor=\"cs-motherHealthIssuesDetails\">إذا كان نعم، يرجى التوضيح (مشاكل صحية للأم)</Label>\n                      <Textarea id=\"cs-motherHealthIssuesDetails\" value={currentCaseStudyData.pregnancyAndBirthInfo?.motherHealthIssuesDetails || \"\"} onChange={(e) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"motherHealthIssuesDetails\", e.target.value)} />\n                    </div>\n                  )}\n                  <div>\n                    <Label htmlFor=\"cs-deliveryType\">هل حدثت الولادة بشكل طبيعي أم قيصري؟</Label>\n                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.deliveryType || \"natural\"} onValueChange={(value) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"deliveryType\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"natural\">طبيعي</SelectItem>\n                        <SelectItem value=\"cesarean\">قيصري</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-childHealthIssuesAtBirth\">هل واجه الطفل أي مشاكل صحية عند الولادة أو بعدها مباشرة؟</Label>\n                    <Select value={currentCaseStudyData.pregnancyAndBirthInfo?.childHealthIssuesAtBirth || \"no\"} onValueChange={(value) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"childHealthIssuesAtBirth\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"yes\">نعم</SelectItem>\n                        <SelectItem value=\"no\">لا</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  {currentCaseStudyData.pregnancyAndBirthInfo?.childHealthIssuesAtBirth === \"yes\" && (\n                    <div>\n                      <Label htmlFor=\"cs-childHealthIssuesDetails\">إذا كان نعم، يرجى التوضيح (مشاكل صحية للطفل)</Label>\n                      <Textarea id=\"cs-childHealthIssuesDetails\" value={currentCaseStudyData.pregnancyAndBirthInfo?.childHealthIssuesDetails || \"\"} onChange={(e) => handleCaseStudyInputChange(\"pregnancyAndBirthInfo\", \"childHealthIssuesDetails\", e.target.value)} />\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Reinforcer Response Info Card */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>استجابة الطفل للمعززات والمكافآت</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"cs-favoriteToys\">ما هي الأشياء التي يحب الطفل اللعب بها أو يحبها كثيراً؟</Label>\n                    <Textarea id=\"cs-favoriteToys\" value={currentCaseStudyData.reinforcerResponseInfo?.favoriteToys || \"\"} onChange={(e) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"favoriteToys\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-enjoyableActivities\">ما هي الأنشطة التي يستمتع بها الطفل؟</Label>\n                    <Textarea id=\"cs-enjoyableActivities\" value={currentCaseStudyData.reinforcerResponseInfo?.enjoyableActivities || \"\"} onChange={(e) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"enjoyableActivities\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-favoriteFoods\">هل هناك أطعمة معينة يحبها الطفل جداً؟</Label>\n                    <Textarea id=\"cs-favoriteFoods\" value={currentCaseStudyData.reinforcerResponseInfo?.favoriteFoods || \"\"} onChange={(e) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"favoriteFoods\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-happinessExpression\">ماذا يفعل الطفل عندما يكون سعيداً أو متحفزاً؟</Label>\n                    <Textarea id=\"cs-happinessExpression\" value={currentCaseStudyData.reinforcerResponseInfo?.happinessExpression || \"\"} onChange={(e) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"happinessExpression\", e.target.value)} />\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-motivationMethods\">عندما تريد من الطفل أن يفعل شيئاً ما، ما الذي تستخدمه لتحفيزه أو مكافأته عادةً؟</Label>\n                    <Textarea id=\"cs-motivationMethods\" value={currentCaseStudyData.reinforcerResponseInfo?.motivationMethods || \"\"} onChange={(e) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"motivationMethods\", e.target.value)} />\n                  </div>\n                   <div>\n                    <Label htmlFor=\"cs-smilesAtGuardian\">هل يبتسم الطفل عندما يراك أو يسمع صوتك؟</Label>\n                    <Select value={currentCaseStudyData.reinforcerResponseInfo?.smilesAtGuardian || \"unknown\"} onValueChange={(value) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"smilesAtGuardian\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"yes\">نعم</SelectItem>\n                        <SelectItem value=\"no\">لا</SelectItem>\n                        <SelectItem value=\"sometimes\">أحياناً</SelectItem>\n                        <SelectItem value=\"unknown\">غير معروف/غير ملاحظ</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                  <div>\n                    <Label htmlFor=\"cs-communicatesNeeds\">هل يحاول الطفل التواصل معك عندما يحتاج شيئًا؟</Label>\n                    <Select value={currentCaseStudyData.reinforcerResponseInfo?.communicatesNeeds || \"unknown\"} onValueChange={(value) => handleCaseStudyInputChange(\"reinforcerResponseInfo\", \"communicatesNeeds\", value)}>\n                      <SelectTrigger><SelectValue /></SelectTrigger>\n                      <SelectContent>\n                        <SelectItem value=\"yes\">نعم</SelectItem>\n                        <SelectItem value=\"no\">لا</SelectItem>\n                        <SelectItem value=\"sometimes\">أحياناً</SelectItem>\n                        <SelectItem value=\"unknown\">غير معروف/غير ملاحظ</SelectItem>\n                      </SelectContent>\n                    </Select>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </ScrollArea>\n          <DialogFooter className=\"gap-2 sm:justify-end\">\n            <Button variant=\"outline\" onClick={() => handlePrintCaseStudy()} disabled={!childData.caseStudy || !Object.values(childData.caseStudy).some(section => section && Object.values(section).some(val => val))}>\n                <Printer className=\"ml-2 h-4 w-4\" /> طباعة دراسة الحالة\n            </Button>\n            <DialogClose asChild>\n              <Button type=\"button\" variant=\"outline\">إلغاء</Button>\n            </DialogClose>\n            <Button type=\"button\" onClick={handleSaveCaseStudy}>حفظ دراسة الحالة</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n    </>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAGA,oVAA4D,eAAe;AAC3E;AACA;AACA;AACA;AACA;AACA,kYAAwM,eAAe;AAAvN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AA1BA;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCe,SAAS,mBAAmB,EAAE,OAAO,EAA2B;IAC7E,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,EAAE,WAAW,EAAE,SAAS,kBAAkB,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;IAEpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjF,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACrE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,CAAC;IACpF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,IAAI;IAE7E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,uHAAA,CAAA,uBAAoB;IACpG,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB,OAAO,8BAA8B;IAGtF,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAMhC;QACD,mBAAmB;QACnB,kBAAkB;QAClB,cAAc;QACd,kBAAkB;QAClB,2BAA2B;IAC7B;IAEA,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS;QACvB,IAAI,OAAO;YACT,aAAa;YACb,mBAAmB;YACnB,wBAAwB,MAAM,SAAS,IAAI;YAC3C,wBAAwB,MAAM,SAAS,GAAG,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,MAAM,SAAS,KAAK,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,uHAAA,CAAA,uBAAoB;QACxI;QACA,WAAW;IACb,GAAG;QAAC;QAAS;KAAS;IAEtB,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,MAAM,SAAS,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,cAAc,EAAE,OAAO,GAAG,CAAC,EAAE;YACzH,oBAAoB;QACtB,OAAO;YACL,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;QAEhB,MAAM,QAAQ,IAAI;QAClB,IAAI,wBAAwB;QAC5B,IAAI,uBAAuB;QAC3B,IAAI,mBAAmB;QACvB,IAAI,uBAAuB;QAC3B,IAAI,gBAA+B;QAEnC,MAAM,SAAS,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,UAAU,SAAS,EAAE,MAAM,WAAW;QAClE,IAAI,OAAO,KAAK,IAAI,GAAG;YACrB,uBAAuB;QACzB;QAEA,IAAI,kBAAkB;YACpB,IAAI;gBACF,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,cAAc;gBACnE,IAAI,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;oBAC/B,MAAM,sBAAsB,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;oBAC1D,gBAAgB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,oBAAoB,WAAW;oBAE1D,IAAI,CAAA,GAAA,sIAAA,CAAA,SAAM,AAAD,EAAE,sBAAsB;wBAC/B,wBAAwB;oBAC1B,OAAO;wBACL,MAAM,wBAAwB,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,qBAAqB;wBACpE,IAAI,yBAAyB,MAAM,yBAAyB,GAAG;4BAC7D,uBAAuB;wBACzB;oBACF;gBACF,OAAO;oBACL,mBAAmB;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,mBAAmB;YACrB;QACF,OAAO;YACL,mBAAmB;QACrB;QAEA,UAAU;YACR,mBAAmB,yBAAyB,CAAC;YAC7C,kBAAkB,wBAAwB,CAAC;YAC3C,cAAc,oBAAoB,CAAC;YACnC,kBAAkB;YAClB,2BAA2B;QAC7B;IAEF,GAAG;QAAC;QAAW;KAAiB;IAGhC,MAAM,8BAA8B;QAClC,IAAI,WAAW;YACb,mBAAmB;YACnB,wBAAwB,UAAU,SAAS,IAAI;YAC/C,2BAA2B;QAC7B;IACF;IAEA,MAAM,2BAA2B,CAAC;QAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAU;IACjE;IAEA,MAAM,4BAA4B,CAAC;QACjC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,QAAQ;YAAM,CAAC;IACxD;IAEA,MAAM,4BAA4B,CAAC;QACjC,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YAC9B,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,mBAAmB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW,OAAO,MAAM;oBAAW,CAAU;gBACpF,wBAAwB,OAAO,MAAM;YACvC;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,4BAA4B;QAChC,mBAAmB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAU,CAAU;QACtE,wBAAwB;QACxB,MAAM,YAAY,SAAS,cAAc,CAAC;QAC1C,IAAI,WAAW;YACb,UAAU,KAAK,GAAG;QACpB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,EAAE,cAAc;QAChB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,SAAS,IAAI,CAAC,gBAAgB,cAAc,IAAI,CAAC,gBAAgB,cAAc,EAAE;YACjJ,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAmC,SAAS;YAAc;YAC7F;QACF;QAEA,IAAI,iBAAiB,gBAAgB,SAAS;QAC9C,IAAI,CAAC,kBAAkB,CAAC,sBAAsB;YAC5C,MAAM,WAAW,gBAAgB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW,MAAM;YAC1G,iBAAiB,CAAC,sCAAsC,EAAE,mBAAmB,WAAW;QAC1F,OAAO,IAAI,CAAC,kBAAkB,sBAAsB;YAClD,iBAAiB;QACnB;QAEA,MAAM,sBAA6B;YACjC,GAAG,SAAS;YACZ,MAAM,gBAAgB,IAAI;YAC1B,WAAW,gBAAgB,SAAS;YACpC,gBAAgB,gBAAgB,cAAc;YAC9C,gBAAgB,gBAAgB,cAAc;YAC9C,WAAW;YACX,QAAQ,gBAAgB,MAAM;QAChC;QAEA,MAAM,UAAU,YAAY;QAC5B,IAAI,SAAS;YACX,aAAa;YACb,MAAM;gBAAE,OAAO;gBAAQ,aAAa,CAAC,gBAAgB,EAAE,oBAAoB,IAAI,CAAC,OAAO,CAAC;YAAC;YACzF,2BAA2B;QAC7B,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA8B,SAAS;YAAc;QAC1F;IACF;IAEA,2BAA2B;IAC3B,IAAI,WAAW,CAAC,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,kBAAkB;IAClB,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAyB;;;;;;8BACvC,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;kCAAO;;;;;;;;;;;;;;;;;IAI/B;IAEA,MAAM,eAAe,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW,MAAM;IAExF,SAAS,WAAW,UAAqC;QACvD,IAAI,CAAC,YAAY,OAAO;QACxB,IAAI;YACF,OAAO,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE;QAC1B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,8BAA8B;QAClC,sBAAsB,CAAC;QACvB,mBAAmB,IAAI;QACvB,2BAA2B;IAC7B;IAEA,MAAM,+BAA+B,CAAC;QACpC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,sBAAsB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IAC3D;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,aAAa,IAAI,CAAC,mBAAmB,SAAS,IAAI,CAAC,mBAAmB,KAAK,EAAE;YACvH,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAyC,SAAS;YAAc;YACnG;QACF;QACA,MAAM,UAAuB;YAC3B,IAAI,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE;YACrB,MAAM,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB;YAC9B,eAAe,mBAAmB,aAAa,IAAI;YACnD,WAAW,mBAAmB,SAAS,IAAI;YAC3C,OAAO,mBAAmB,KAAK,IAAI;YACnC,WAAW,mBAAmB,SAAS;QACzC;QAEA,MAAM,eAAe;YACnB,GAAG,SAAS;YACZ,cAAc;mBAAK,UAAW,YAAY,IAAI,EAAE;gBAAG;aAAQ;QAC7D;QAEA,MAAM,UAAU,YAAY;QAC5B,IAAI,SAAS;YACX,aAAa;YACb,MAAM;gBAAE,OAAO;gBAAQ,aAAa;YAA4B;YAChE,2BAA2B;QAC7B,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA2B,SAAS;YAAc;QACvF;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI,CAAC,WAAW;QAEhB,MAAM,SAAS,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,UAAU,SAAS;QAC/C,MAAM,mBAAmB,GAAG,OAAO,KAAK,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC;QAExE,IAAI,wBAAuC,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,uHAAA,CAAA,uBAAoB;QAEzF,IAAI,UAAU,SAAS,EAAE;YACvB,wBAAwB;gBACtB,WAAW;oBACT,GAAG,sBAAsB,SAAS;oBAClC,GAAI,UAAU,SAAS,CAAC,SAAS,IAAI,CAAC,CAAC;gBACzC;gBACA,uBAAuB;oBACrB,GAAG,sBAAsB,qBAAqB;oBAC9C,GAAI,UAAU,SAAS,CAAC,qBAAqB,IAAI,CAAC,CAAC;gBACrD;gBACA,wBAAwB;oBACtB,GAAG,sBAAsB,sBAAsB;oBAC/C,GAAI,UAAU,SAAS,CAAC,sBAAsB,IAAI,CAAC,CAAC;gBACtD;YACF;QACF;QAEA,sBAAsB,SAAS,CAAC,SAAS,GAAG,UAAU,IAAI,IAAI,sBAAsB,SAAS,CAAC,SAAS,IAAI;QAC3G,sBAAsB,SAAS,CAAC,SAAS,GAAG,UAAU,SAAS,IAAI,sBAAsB,SAAS,CAAC,SAAS,IAAI;QAChH,sBAAsB,SAAS,CAAC,UAAU,GAAG;QAC7C,sBAAsB,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,IAAI,sBAAsB,SAAS,CAAC,MAAM,IAAI;QAEvG,wBAAwB;QACxB,yBAAyB;IAC3B;IAGA,MAAM,6BAA6B,CAAC,SAA8B,OAAe;QAC/E,wBAAwB,CAAA;YACpB,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;YAC3C,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACpB,QAAQ,CAAC,QAAQ,GAAG,CAAC;YACzB;YACC,QAAQ,CAAC,QAAQ,AAAQ,CAAC,MAAM,GAAG,iBAAiB,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,gBAAgB;YAC1F,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,eAAe;YACnB,GAAG,SAAS;YACZ,WAAW;QACb;QAEA,MAAM,UAAU,YAAY;QAC5B,IAAI,SAAS;YACX,aAAa;YACb,MAAM;gBAAE,OAAO;gBAAQ,aAAa;YAA6B;YACjE,yBAAyB;QAC3B,OAAO;YACL,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAA4B,SAAS;YAAc;QACxF;IACF;IAEA,MAAM,yBAAyB,CAAC,OAAe;QAC7C,IAAI,CAAC,QAAQ,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA,MAAO,CAAC,MAAM;YACnD,qBACE,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCAAC,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAE;;;;;;;;;;;kCACxB,8OAAC,gIAAA,CAAA,cAAW;kCAAC,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAGxD;QAEA,MAAM,SAAiC;YACnC,WAAW;YACX,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,qBAAqB;YACrB,aAAa;YACb,sBAAsB;YACtB,aAAa;YACb,cAAc;YACd,sBAAsB;YACtB,mBAAmB;YACnB,qBAAqB;YACrB,mCAAmC;YACnC,2BAA2B;YAC3B,cAAc;YACd,0BAA0B;YAC1B,0BAA0B;YAC1B,cAAc;YACd,qBAAqB;YACrB,eAAe;YACf,qBAAqB;YACrB,mBAAmB;YACnB,kBAAkB;YAClB,mBAAmB;QACvB;QAGA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BAAC,cAAA,8OAAC,gIAAA,CAAA,YAAS;kCAAE;;;;;;;;;;;8BACxB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;wBACrC,MAAM,eAAe,SAAS;wBAC9B,MAAM,QAAQ,MAAM,CAAC,IAAI,IAAI;wBAC7B,IAAI,QAAQ,UAAU;4BAClB,MAAM,cAAc,iBAAiB,SAAS,QAAQ,iBAAiB,WAAW,SAAS,iBAAiB,UAAU,QAAQ;4BAC9H,qBAAO,8OAAC;;kDAAY,8OAAC;kDAAQ;;;;;;oCAAe;oCAAE;;+BAA/B;;;;;wBACnB;wBACC,IAAI,QAAQ,iBAAiB,QAAQ,uBAAuB,QAAQ,uCAAuC,QAAQ,8BAA8B,QAAQ,sBAAsB,QAAQ,qBAAqB;4BACzM,MAAM,YAAY,iBAAiB,QAAQ,QAAQ,iBAAiB,OAAO,OAAO,iBAAiB,cAAc,YAAY;4BAC7H,qBAAO,8OAAC;;kDAAY,8OAAC;kDAAQ;;;;;;oCAAe;oCAAE;;+BAA/B;;;;;wBACnB;wBACA,IAAI,QAAQ,gBAAgB;4BACxB,MAAM,gBAAgB,iBAAiB,YAAY,UAAU,iBAAiB,aAAa,UAAU;4BACrG,qBAAO,8OAAC;;kDAAY,8OAAC;kDAAQ;;;;;;oCAAe;oCAAE;;+BAA/B;;;;;wBACnB;wBACA,IAAI,SAAS,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,QAAQ,WAAW,GAAG;4BACtE,qBAAO,8OAAC;;kDAAY,8OAAC;kDAAQ;;;;;;oCAAe;oCAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;;+BAA1C;;;;;wBACnB;wBACA,IAAI,IAAI,WAAW,GAAG,QAAQ,CAAC,cAAc,QAAQ,kBAAkB,QAAQ,kBAAkB,QAAQ,yBAAyB,QAAQ,mBAAmB,QAAQ,yBAAyB,QAAQ,qBAAqB;4BACzN,qBACE,8OAAC;gCAAc,WAAU;;kDACvB,8OAAC;kDAAQ;;;;;;kDACT,8OAAC;wCAAI,WAAU;kDAA2E;;;;;;;+BAFlF;;;;;wBAKd;wBACA,qBAAO,8OAAC;;8CAAY,8OAAC;8CAAQ;;;;;;gCAAe;gCAAE;;2BAA/B;;;;;oBACjB;;;;;;;;;;;;IAIR;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC5B,MAAM;gBAAE,OAAO;gBAAkB,aAAa;gBAAiD,SAAS;YAAc;YACtH;QACJ;QAEA,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,aAAa;YACb,YAAY,QAAQ,CAAC,KAAK,CAAC,wBAAwB,CAAC,cAAc,EAAE,UAAU,IAAI,EAAE,GAAG;YACvF,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;QAoB5B,CAAC;YACD,YAAY,QAAQ,CAAC,KAAK,CAAC;YAC3B,YAAY,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc,EAAE,UAAU,IAAI,EAAE,GAAG;YAExE,MAAM,WAAW,kBAAkB,OAAO,CAAC,gBAAgB,CAAC;YAE5D,IAAI,qBAAqB;YACzB,IAAI,sBAAsB;YAE1B,SAAS,OAAO,CAAC,CAAA;gBACb,IAAI,GAAG,OAAO,CAAC,WAAW,OAAO,MAAM;oBACnC,IAAI,oBAAoB;wBACpB,YAAY,QAAQ,CAAC,KAAK,CAAC,SAAS,sBAAsB;wBAC1D,YAAY,QAAQ,CAAC,KAAK,CAAC,gCAAgC,qBAAqB;oBACpF;oBACA,sBAAsB,GAAG,WAAW,IAAI;oBACxC,qBAAqB,IAAI,wBAAwB;gBACrD,OAAO;oBACF,oDAAoD;oBACpD,MAAM,eAAe,GAAG,aAAa,CAAC;oBACtC,MAAM,eAAe,eAAe,aAAa,WAAW,GAAG,GAAG,aAAa,CAAC,UAAU,IAAI,uBAAuB;oBAErH,IAAI,gBAAgB,cAAc;wBAC9B,sBAAsB,qCAAqC,aAAa,WAAW,GAAG;wBACtF,IAAG,aAAa,QAAQ,KAAK,OAAO;4BAChC,sBAAsB,UAAU,aAAa,WAAW,GAAG;wBAC/D,OAAO;4BACF,sBAAsB,CAAC,aAAa,WAAW,IAAI,EAAE,IAAI;wBAC9D;oBACJ,OAAO,IAAI,GAAG,WAAW,EAAE,QAAQ;wBAC/B,sBAAsB,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC;oBACpD;gBACL;YACJ;YAEA,IAAI,oBAAoB;gBACpB,YAAY,QAAQ,CAAC,KAAK,CAAC,SAAS,sBAAsB;gBAC1D,YAAY,QAAQ,CAAC,KAAK,CAAC,gCAAgC,qBAAqB;YACpF;YAEA,YAAY,QAAQ,CAAC,KAAK,CAAC;YAC3B,YAAY,QAAQ,CAAC,KAAK;YAC1B,YAAY,KAAK;YACjB,YAAY,KAAK;QACrB,OAAO;YACH,MAAM;gBAAE,OAAO;gBAAO,aAAa;gBAAqD,SAAS;YAAa;QAClH;IACF;IAGA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,UAAU,SAAS;gDAAE,KAAK,UAAU,IAAI;gDAAE,gBAAa;gDAAiB,WAAU;;;;;;0DACpG,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DAA0E;;;;;;;;;;;;kDAEtG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;sEACjC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,UAAU,aAAa;;;;;;;;;;;;0DAG5B,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAmC,UAAU,IAAI;;;;;;0DACtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;0EAAkC,8OAAC,4NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAY;4DAAQ,UAAU,MAAM,KAAK,SAAS,QAAQ,UAAU,MAAM,KAAK,WAAW,SAAS,UAAU,MAAM,KAAK,UAAU,QAAQ;;;;;;;kEAChN,8OAAC,8IAAA,CAAA,UAAU;wDAAC,WAAW,UAAU,SAAS;wDAAE,OAAM;wDAAgB,WAAU;;;;;;kEAC5E,8OAAC;wDAAE,WAAU;;4DAAU;4DAAgB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,UAAU,SAAS;;;;;;;;;;;;;;;;;;;kDAGzE,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAGrC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;0EAA0B,8OAAC,4MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAwB;0EAAC,8OAAC;gEAAO,WAAU;0EAAwB;;;;;;4DAAwB;4DAAE,WAAW,UAAU,cAAc,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,UAAU,cAAc,IAAI;;;;;;;kEACvO,8OAAC;wDAAE,WAAU;;0EAA0B,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAwB;0EAAC,8OAAC;gEAAO,WAAU;0EAAwB;;;;;;4DAAkB;4DAAE,UAAU,cAAc;;;;;;;;;;;;;;;;;;;oCAG1K,oBAAoB,WAAW,iBAAiB,cAAc,kBAC7D,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAC9D,8OAAC;gDAAE,WAAU;;kEAAU,8OAAC;wDAAO,WAAU;kEAAwB;;;;;;oDAAiB;oDAAE,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,cAAc;;;;;;;0DAC9H,8OAAC,8IAAA,CAAA,UAAU;gDAAC,WAAW,UAAU,SAAS;gDAAE,gBAAgB,iBAAiB,cAAc;gDAAE,OAAM;gDAAqB,WAAU;;;;;;;;;;;6DAGpI,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAC9D,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;kCAMrD,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,gBAAgB,kBACtB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC,iIAAA,CAAA,aAAU;wCAAC,WAAU;kDAAgB;;;;;;kDACtC,8OAAC,iIAAA,CAAA,mBAAsB;;4CAAC;4CACX,UAAU,IAAI;4CAAC;;;;;;;;;;;;;4BAK/B,CAAC,OAAO,gBAAgB,IAAI,OAAO,YAAY,kBAC9C,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC,iIAAA,CAAA,aAAU;wCAAC,WAAU;kDAAgB;;;;;;kDACtC,8OAAC,iIAAA,CAAA,mBAAsB;;4CAAC;4CACI,UAAU,IAAI;4CAAC;;;;;;;;;;;;;4BAK9C,CAAC,OAAO,gBAAgB,IAAI,OAAO,iBAAiB,kBACnD,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC,iIAAA,CAAA,aAAU;wCAAC,WAAU;kDAAgB;;;;;;kDACtC,8OAAC,iIAAA,CAAA,mBAAsB;;4CAAC;4CACU,UAAU,IAAI;4CAAC;4CAAkD,mBAAmB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,cAAc,IAAI;4CAAY;;;;;;;;;;;;;4BAKnL,CAAC,OAAO,gBAAgB,IAAI,OAAO,gBAAgB,IAAI,CAAC,OAAO,iBAAiB,kBAC/E,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC,iIAAA,CAAA,aAAU;wCAAC,WAAU;kDAAgB;;;;;;kDACtC,8OAAC,iIAAA,CAAA,mBAAsB;;4CAAC;4CACQ,UAAU,IAAI;4CAAC;4CAAmB,OAAO,yBAAyB;4CAAC;;;;;;;;;;;;;;;;;;;kCAMzG,8OAAC,qIAAA,CAAA,YAAS;;;;;kCAEV,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;kDAEb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAG3C,8OAAC,gIAAA,CAAA,cAAW;0CACT,UAAU,YAAY,IAAI,UAAU,YAAY,CAAC,MAAM,GAAG,kBACzD,8OAAC,0IAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;kDACZ,UAAU,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,IAAI,GAAG,CAAC,CAAA,qBAClG,8OAAC,gIAAA,CAAA,OAAI;gDAAe,WAAU;;kEAC5B,8OAAC,gIAAA,CAAA,aAAU;wDAAC,WAAU;;0EACpB,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;;oEAAU;oEACd,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,IAAI;;;;;;;0EAErC,8OAAC,gIAAA,CAAA,kBAAe;gEAAC,WAAU;;oEAAU;oEAAS,KAAK,SAAS;;;;;;;;;;;;;kEAE9D,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAuB;oEAAE,KAAK,aAAa;;;;;;;0EACxD,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAmB;kFAAC,8OAAC;wEAAI,WAAU;kFAA6E,KAAK,KAAK;;;;;;;;;;;;4DACtI,KAAK,SAAS,kBAAI,8OAAC;;kFAAI,8OAAC;kFAAO;;;;;;oEAAyB;kFAAC,8OAAC;wEAAI,WAAU;kFAA6E,KAAK,SAAS;;;;;;;;;;;;;;;;;;;+CAV7J,KAAK,EAAE;;;;;;;;;;;;;;yDAiBxB,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;;;;;;kCAK5D,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;;kDAEb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;;4CAC1C,UAAU,SAAS,IAAI,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,CAAC,CAAA,UAAW,WAAW,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,MAAO,sBAAQ,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAAoB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAC1L,UAAU,SAAS,IAAI,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,CAAC,CAAA,UAAW,WAAW,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,MAAO,QAAQ,uBAAuB;;;;;;;;;;;;;0CAG5J,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;gCAAY,KAAK;;oCAAmB;oCACxD,UAAU,SAAS,IAAI,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,CAAC,CAAA,UAAW,WAAW,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,MAAO,sBACvH;;0DACE,8OAAC;gDAAI,WAAU;0DAAsB,uBAAuB,mCAAmC,UAAU,SAAS,CAAC,SAAS;;;;;;0DAC5H,8OAAC;gDAAI,WAAU;0DAAsB,uBAAuB,kCAAkC,UAAU,SAAS,CAAC,qBAAqB;;;;;;0DACvI,8OAAC;gDAAI,WAAU;0DAAsB,uBAAuB,oCAAoC,UAAU,SAAS,CAAC,sBAAsB;;;;;;;qEAG5I,8OAAC;wCAAE,WAAU;kDAAyC;;;;;;;;;;;;;;;;;;kCAK5D,8OAAC,qIAAA,CAAA,YAAS;;;;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAGpD,8OAAC,gIAAA,CAAA,kBAAe;;oDAAC;oDAAiC,UAAU,IAAI;oDAAC;;;;;;;;;;;;;kDAEnE,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAI;gDAAmC,KAAI;gDAAgB,OAAO;gDAAK,QAAQ;gDAAK,WAAU;gDAAkB,gBAAa;;;;;;0DACpI,8OAAC;gDAAE,WAAU;;oDAAqC;oDAC7B,YAAY,MAAM;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,eAAe,CAAC;wDAAE,QAAQ;kEAC9D,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;kEAG3C,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,WAAW,CAAC;wDAAE,QAAQ;kEAC1D,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM/D,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAwB;;;;;;;0DAG9C,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC,6HAAA,CAAA,UAAK;gDAAC,KAAI;gDAAmC,KAAI;gDAAuB,OAAO;gDAAK,QAAQ;gDAAK,WAAU;gDAAkB,gBAAa;;;;;;0DAC3I,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,KAAK,CAAC;gDAAE,QAAQ;0DACpD,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAyB,cAAc;0BACnD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,kIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,8OAAC;4BAAK,UAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAA6B,WAAU;8DAAa;;;;;;8DACnE,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO,gBAAgB,aAAa,IAAI;oDACxC,UAAU;oDACV,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAoB,WAAU;8DAAa;;;;;;8DAC1D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAoB,MAAK;oDAAO,OAAO,gBAAgB,IAAI,IAAI;oDAAI,UAAU;oDAA0B,WAAU;oDAAa,QAAQ;;;;;;;;;;;;sDAElJ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAyB,WAAU;8DAAa;;;;;;8DAC/D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAAyB,MAAK;oDAAY,MAAK;oDAAO,OAAO,gBAAgB,SAAS,IAAI;oDAAI,UAAU;oDAA0B,WAAU;oDAAa,QAAQ;;;;;;;;;;;;sDAE7K,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAA8B,WAAU;8DAAa;;;;;;8DACpE,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAA8B,MAAK;oDAAiB,MAAK;oDAAO,OAAO,gBAAgB,cAAc,IAAI;oDAAI,UAAU;oDAA0B,WAAU;oDAAa,QAAQ;;;;;;;;;;;;sDAE5L,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAA8B,WAAU;8DAAa;;;;;;8DACpE,8OAAC,iIAAA,CAAA,QAAK;oDAAC,IAAG;oDAA8B,MAAK;oDAAiB,OAAO,gBAAgB,cAAc,IAAI;oDAAI,UAAU;oDAA0B,WAAU;oDAAa,QAAQ;;;;;;;;;;;;sDAEhL,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAsB,WAAU;8DAAa;;;;;;8DAC5D,8OAAC,kIAAA,CAAA,SAAM;oDAAC,OAAO,gBAAgB,MAAM;oDAAE,eAAe,CAAC,QAAU,0BAA0B;;sEACzF,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,IAAG;4DAAsB,WAAU;sEAChD,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAO;;;;;;8EACzB,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAS;;;;;;;;;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAyB,WAAU;8DAAkB;;;;;;8DACpE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,qCACC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wEAAC,KAAK;wEAAsB,KAAI;wEAAgB,IAAI;wEAAC,WAAU;;;;;;;;;;yFAGvE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAGtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iIAAA,CAAA,QAAK;4EACJ,IAAG;4EACH,MAAK;4EACL,MAAK;4EACL,QAAO;4EACP,UAAU;4EACV,WAAU;;;;;;sFAEZ,8OAAC;4EAAE,WAAU;sFAAqC;;;;;;;;;;;;;;;;;;wDAKrD,sCACC,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;4DACT,WAAU;;8EAEV,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC,kIAAA,CAAA,eAAY;;sDACX,8OAAC,kIAAA,CAAA,cAAW;4CAAC,OAAO;sDAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;0DAAU;;;;;;;;;;;sDAE1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAyB,cAAc;0BACnD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAe,WAAU;sDAAa;;;;;;sDAGrD,8OAAC,0IAAA,CAAA,aAAU;4CACT,MAAM;4CACN,SAAS;4CACT,iBAAgB;;;;;;;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAe,WAAU;sDAAa;;;;;;sDAGrD,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAK;4CACL,OAAO,mBAAmB,aAAa,IAAI;4CAC3C,UAAU;4CACV,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAoB,WAAU;sDAAa;;;;;;sDAG1D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,mBAAmB,SAAS,IAAI;4CACvC,UAAU;4CACV,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAa;;;;;;sDAGtD,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAK;4CACL,OAAO,mBAAmB,KAAK,IAAI;4CACnC,UAAU;4CACV,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAoB,WAAU;sDAAa;;;;;;sDAG1D,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,MAAK;4CACL,OAAO,mBAAmB,SAAS,IAAI;4CACvC,UAAU;4CACV,WAAU;;;;;;;;;;;;;;;;;;sCAIhB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,OAAO;8CAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;kDAAU;;;;;;;;;;;8CAE1C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAS;8CAAuB;;;;;;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAuB,cAAc;0BACjD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;;wCACT,UAAU,SAAS,IAAI,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,CAAC,CAAA,UAAW,WAAW,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,MAAO,QAAQ,uBAAuB;wCAAmB;wCAAK,UAAU,IAAI;;;;;;;8CAEhM,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC;gCAAI,WAAU;;oCAAsB;kDAEnC,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAe,OAAO,qBAAqB,SAAS,EAAE,aAAa;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAEvK,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC,0IAAA,CAAA,aAAU;gEAAC,MAAM,qBAAqB,SAAS,EAAE,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB,SAAS,CAAC,SAAS,IAAI;gEAAW,SAAS,CAAC,OAAS,2BAA2B,aAAa,aAAa;;;;;;;;;;;;kEAExM,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAgB;;;;;;0EAC/B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAgB,OAAO,qBAAqB,SAAS,EAAE,cAAc;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAE1K,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,SAAS,EAAE,UAAU;gEAAW,eAAe,CAAC,QAAU,2BAA2B,aAAa,UAAU;;kFAC9I,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;0FACzB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAIjC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAkB;;;;;;0EACjC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAkB,OAAO,qBAAqB,SAAS,EAAE,gBAAgB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAEhL,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAyB;;;;;;0EACxC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAyB,OAAO,qBAAqB,SAAS,EAAE,uBAAuB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAErM,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAiB;;;;;;0EAChC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAiB,OAAO,qBAAqB,SAAS,EAAE,eAAe;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAE7K,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA0B;;;;;;0EACzC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAA0B,OAAO,qBAAqB,SAAS,EAAE,wBAAwB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,wBAAwB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAExM,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAiB;;;;;;0EAChC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,SAAS,EAAE,eAAe;gEAAM,eAAe,CAAC,QAAU,2BAA2B,aAAa,eAAe;;kFACnJ,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;oDAI5B,qBAAqB,SAAS,EAAE,gBAAgB,uBAC/C,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAkB;;;;;;0EACjC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAAkB,OAAO,qBAAqB,SAAS,EAAE,gBAAgB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,aAAa,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAOzL,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA0B;;;;;;0EACzC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAA0B,OAAO,qBAAqB,qBAAqB,EAAE,wBAAwB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,yBAAyB,wBAAwB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAEhO,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAuB;;;;;;0EACtC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,qBAAqB,EAAE,qBAAqB;gEAAO,eAAe,CAAC,QAAU,2BAA2B,yBAAyB,qBAAqB;;kFACxL,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;oDAI5B,qBAAqB,qBAAqB,EAAE,sBAAsB,sBACjE,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAyB;;;;;;0EACxC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,IAAG;gEAAyB,OAAO,qBAAqB,qBAAqB,EAAE,uBAAuB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,yBAAyB,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAG/N,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAuC;;;;;;0EACtD,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,qBAAqB,EAAE,qCAAqC;gEAAM,eAAe,CAAC,QAAU,2BAA2B,yBAAyB,qCAAqC;;kFACvN,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;oDAI5B,qBAAqB,qBAAqB,EAAE,sCAAsC,uBACjF,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA+B;;;;;;0EAC9C,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAA+B,OAAO,qBAAqB,qBAAqB,EAAE,6BAA6B;gEAAI,UAAU,CAAC,IAAM,2BAA2B,yBAAyB,6BAA6B,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAGpP,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAkB;;;;;;0EACjC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,qBAAqB,EAAE,gBAAgB;gEAAW,eAAe,CAAC,QAAU,2BAA2B,yBAAyB,gBAAgB;;kFAClL,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;0FAC5B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAW;;;;;;;;;;;;;;;;;;;;;;;;kEAInC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA8B;;;;;;0EAC7C,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,qBAAqB,EAAE,4BAA4B;gEAAM,eAAe,CAAC,QAAU,2BAA2B,yBAAyB,4BAA4B;;kFACrM,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;oDAI5B,qBAAqB,qBAAqB,EAAE,6BAA6B,uBACxE,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAA8B;;;;;;0EAC7C,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAA8B,OAAO,qBAAqB,qBAAqB,EAAE,4BAA4B;gEAAI,UAAU,CAAC,IAAM,2BAA2B,yBAAyB,4BAA4B,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAOrP,8OAAC,gIAAA,CAAA,OAAI;;0DACH,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAkB;;;;;;0EACjC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAAkB,OAAO,qBAAqB,sBAAsB,EAAE,gBAAgB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,0BAA0B,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAE7M,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAyB;;;;;;0EACxC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAAyB,OAAO,qBAAqB,sBAAsB,EAAE,uBAAuB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,0BAA0B,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAElO,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAmB;;;;;;0EAClC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAAmB,OAAO,qBAAqB,sBAAsB,EAAE,iBAAiB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,0BAA0B,iBAAiB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAEhN,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAyB;;;;;;0EACxC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAAyB,OAAO,qBAAqB,sBAAsB,EAAE,uBAAuB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,0BAA0B,uBAAuB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAElO,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAuB;;;;;;0EACtC,8OAAC,oIAAA,CAAA,WAAQ;gEAAC,IAAG;gEAAuB,OAAO,qBAAqB,sBAAsB,EAAE,qBAAqB;gEAAI,UAAU,CAAC,IAAM,2BAA2B,0BAA0B,qBAAqB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kEAE3N,8OAAC;;0EACA,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAsB;;;;;;0EACrC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,sBAAsB,EAAE,oBAAoB;gEAAW,eAAe,CAAC,QAAU,2BAA2B,0BAA0B,oBAAoB;;kFAC5L,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;0FACvB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAY;;;;;;0FAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;kEAIlC,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAuB;;;;;;0EACtC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,qBAAqB,sBAAsB,EAAE,qBAAqB;gEAAW,eAAe,CAAC,QAAU,2BAA2B,0BAA0B,qBAAqB;;kFAC9L,8OAAC,kIAAA,CAAA,gBAAa;kFAAC,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAC3B,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAK;;;;;;0FACvB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAY;;;;;;0FAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1C,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM;oCAAwB,UAAU,CAAC,UAAU,SAAS,IAAI,CAAC,OAAO,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,CAAC,CAAA,UAAW,WAAW,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,MAAO;;sDACjM,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAExC,8OAAC,kIAAA,CAAA,cAAW;oCAAC,OAAO;8CAClB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;kDAAU;;;;;;;;;;;8CAE1C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAS;8CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;AAOhE", "debugId": null}}]}