
// This file is no longer used directly by the new assessment page.
// Its content has been moved and adapted into /src/components/assessment/AssessmentEditorClientPage.tsx
// This file can be deleted or kept for reference, but it's not part of the active flow for new/edit assessments.

"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import type { Child, PortageDimension, Assessment, AssessedSkill } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import AssessmentFormComponent from '@/components/assessment/AssessmentForm';
import { MOCK_ASSESSMENTS_DATA } from '@/lib/constants'; // HACK for prototype
import { useToast } from '@/hooks/use-toast';
import { formatDate } from '@/lib/utils';

interface NewAssessmentClientPageProps {
  child: Child;
  portageChecklist: PortageDimension[];
}

export default function NewAssessmentClientPage({ child, portageChecklist }: NewAssessmentClientPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  const handleSaveAssessment = async (formData: { assessedSkills: AssessedSkill[] }) => {
    setIsSaving(true);
    const newAssessmentId = `assess-${child.id}-${Date.now()}`;
    const currentAssessmentDate = new Date().toISOString();

    const newAssessment: Assessment = {
      id: newAssessmentId,
      childId: child.id,
      assessmentDate: currentAssessmentDate,
      assessedSkills: formData.assessedSkills.filter(s => s.status), // Only include skills that were actually assessed
    };

    // HACK: Directly mutate mock data for prototyping. In a real app, this would be an API call.
    MOCK_ASSESSMENTS_DATA.push(newAssessment);
    // console.log("Updated MOCK_ASSESSMENTS_DATA:", MOCK_ASSESSMENTS_DATA);


    toast({
      title: "نجاح",
      description: `تم حفظ تقييم ${child.name} بنجاح.`,
    });

    // Navigate to the view assessment page
    router.push(`/children/${child.id}/assessment/${newAssessmentId}`);
    // No need to setIsSaving(false) here as we are navigating away.
    // If there was an error, we would set it to false.
  };

  return (
    <div className="container mx-auto py-8">
      <Link href={`/children/${child.id}/assessment`} className="inline-flex items-center gap-2 text-sm text-primary hover:underline mb-4">
        العودة إلى سجل تقييمات {child.name}
        <ArrowRight className="h-4 w-4" />
      </Link>
      <h1 className="text-3xl font-bold mb-2 text-primary">تقييم جديد لـ {child.name}</h1>
      <p className="text-muted-foreground mb-6">أكمل قائمة بورتيج أدناه. تاريخ التقييم: {formatDate(new Date().toISOString())}</p>
      
      <AssessmentFormComponent
        child={child}
        portageChecklist={portageChecklist}
        onSubmit={handleSaveAssessment}
        isSubmitting={isSaving}
      />
    </div>
  );
}
