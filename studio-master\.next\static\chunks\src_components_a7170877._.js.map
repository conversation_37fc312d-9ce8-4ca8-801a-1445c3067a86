{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item.dataKey || item.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;;;AALA;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,MAAM,+BAAiB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,WAQpC,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE;;IAChD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,6LAAC;YACC,cAAY;YACZ,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ypBACA;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,6LAAC,sKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;MA/BM;AAiCN,MAAM,eAAe,0JAAA,CAAA,UAAyB;AAE9C,MAAM,oCAAsB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAWzC,CACE,EACE,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACT,EACD;;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;gBACjC,OAAO;YACT;YAEA,MAAM,CAAC,KAAK,GAAG;YACf,MAAM,MAAM,GAAG,YAAY,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,SAAS;YACjE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;YAElB,IAAI,gBAAgB;gBAClB,qBACE,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;8BAC/B,eAAe,OAAO;;;;;;YAG7B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,qBAAO,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAAkB;;;;;;QAC7D;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,6LAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,6LAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;;QAzHqB;;;;QAAA;;;;AA2HvB,oBAAoB,WAAW,GAAG;AAElC,MAAM,cAAc,yJAAA,CAAA,SAAwB;AAE5C,MAAM,mCAAqB,IAAA,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,YAQxC,CACE,EAAE,SAAS,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,gBAAgB,QAAQ,EAAE,OAAO,EAAE,EAC3E;;IAEA,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,6LAAC,WAAW,IAAI;;;;6CAEhB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;;QA1CqB;;;;QAAA;;;;AA4CvB,mBAAmB,WAAW,GAAG;AAEjC,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/AgeDistributionChart.tsx"], "sourcesContent": ["\"use client\";\n\nimport type { ChartConfig } from \"@/components/ui/chart\";\nimport { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, XAxis, YAxis, Tooltip, Cell } from \"recharts\";\nimport { ChartContainer, ChartTooltip, ChartTooltipContent } from \"@/components/ui/chart\";\n\ninterface AgeDistributionDataPoint {\n  label: string;\n  minMonths: number;\n  maxMonths: number;\n  count: number;\n  fill: string;\n}\n\ninterface AgeDistributionChartProps {\n  data: AgeDistributionDataPoint[];\n  config: ChartConfig;\n}\n\nexport default function AgeDistributionChart({ data, config }: AgeDistributionChartProps) {\n  if (!data || data.length === 0) {\n    return <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض توزيع الأعمار.</p>;\n  }\n\n  return (\n    <div className=\"h-[300px] w-full\">\n      <ChartContainer config={config} className=\"w-full h-full\">\n        <BarChart accessibilityLayer data={data} layout=\"vertical\" margin={{ right: 20, left: 20 }}>\n          <CartesianGrid horizontal={false} />\n          <XAxis type=\"number\" dataKey=\"count\" allowDecimals={false} stroke=\"hsl(var(--muted-foreground))\" fontSize={12} />\n          <YAxis\n            type=\"category\"\n            dataKey=\"label\"\n            stroke=\"hsl(var(--muted-foreground))\"\n            fontSize={12}\n            width={80}\n            tickLine={false}\n            axisLine={false}\n          />\n          <ChartTooltip\n            cursor={{ fill: 'hsl(var(--muted))' }}\n            content={<ChartTooltipContent />}\n          />\n          <Bar dataKey=\"count\" radius={4}>\n            {data.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.fill} />\n            ))}\n          </Bar>\n        </BarChart>\n      </ChartContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAmBe,SAAS,qBAAqB,EAAE,IAAI,EAAE,MAAM,EAA6B;IACtF,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBAAO,6LAAC;YAAE,WAAU;sBAAyC;;;;;;IAC/D;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAQ,WAAU;sBACxC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;gBAAC,kBAAkB;gBAAC,MAAM;gBAAM,QAAO;gBAAW,QAAQ;oBAAE,OAAO;oBAAI,MAAM;gBAAG;;kCACvF,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,YAAY;;;;;;kCAC3B,6LAAC,wJAAA,CAAA,QAAK;wBAAC,MAAK;wBAAS,SAAQ;wBAAQ,eAAe;wBAAO,QAAO;wBAA+B,UAAU;;;;;;kCAC3G,6LAAC,wJAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,UAAU;wBACV,OAAO;wBACP,UAAU;wBACV,UAAU;;;;;;kCAEZ,6LAAC,oIAAA,CAAA,eAAY;wBACX,QAAQ;4BAAE,MAAM;wBAAoB;wBACpC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;;;;;;;;;;kCAE/B,6LAAC,sJAAA,CAAA,MAAG;wBAAC,SAAQ;wBAAQ,QAAQ;kCAC1B,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,IAAI;+BAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;KAjCwB", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/ProgressOverviewChart.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Tooltip as RechartsTooltip, Legend as RechartsLegend } from \"recharts\";\nimport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  type ChartConfig\n} from \"@/components/ui/chart\";\n\ninterface ProgressOverviewChartProps {\n  mastered: number;\n  implemented: number;\n  pending: number;\n}\n\nexport default function ProgressOverviewChart({ mastered, implemented, pending }: ProgressOverviewChartProps) {\n  const chartData = [\n    { name: 'أهداف متقنة', value: mastered, fill: 'hsl(var(--chart-2))' },\n    { name: 'أهداف قيد التنفيذ', value: implemented, fill: 'hsl(var(--chart-1))' },\n    { name: 'أهداف معلقة/بدون تقييم', value: pending, fill: 'hsl(var(--chart-4))' },\n  ].filter(item => item.value > 0); // Filter out zero values to avoid empty slices\n\n  if (chartData.length === 0) {\n    return <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض مخطط تقدم الأهداف.</p>;\n  }\n\n  const chartConfig = chartData.reduce((acc, item) => {\n    acc[item.name] = { label: item.name, color: item.fill };\n    return acc;\n  }, {} as ChartConfig);\n\n\n  return (\n    <div className=\"h-[300px] w-full\">\n      <ChartContainer config={chartConfig} className=\"w-full h-full\">\n        <PieChart>\n          <ChartTooltip content={<ChartTooltipContent nameKey=\"name\" hideLabel />} />\n          <Pie\n            data={chartData}\n            dataKey=\"value\"\n            nameKey=\"name\"\n            cx=\"50%\"\n            cy=\"50%\"\n            outerRadius={100}\n            labelLine={false}\n            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {\n              const RADIAN = Math.PI / 180;\n              const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n              const x = cx + radius * Math.cos(-midAngle * RADIAN);\n              const y = cy + radius * Math.sin(-midAngle * RADIAN);\n              if (chartData[index].value === 0) return null; // Don't render label for zero value slices\n              return (\n                <text\n                  x={x}\n                  y={y}\n                  fill=\"hsl(var(--primary-foreground))\"\n                  textAnchor={x > cx ? 'start' : 'end'}\n                  dominantBaseline=\"central\"\n                  fontSize=\"12px\"\n                >\n                  {`${(percent * 100).toFixed(0)}%`}\n                </text>\n              );\n            }}\n          >\n            {chartData.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.fill} />\n            ))}\n          </Pie>\n          <ChartLegend\n            content={<ChartLegendContent nameKey=\"name\" wrapperStyle={{paddingTop: 20}} />}\n            verticalAlign=\"bottom\"\n            align=\"center\"\n          />\n        </PieChart>\n      </ChartContainer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAHA;;;;AAkBe,SAAS,sBAAsB,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAA8B;IAC1G,MAAM,YAAY;QAChB;YAAE,MAAM;YAAe,OAAO;YAAU,MAAM;QAAsB;QACpE;YAAE,MAAM;YAAqB,OAAO;YAAa,MAAM;QAAsB;QAC7E;YAAE,MAAM;YAA0B,OAAO;YAAS,MAAM;QAAsB;KAC/E,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG,IAAI,+CAA+C;IAEjF,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBAAO,6LAAC;YAAE,WAAU;sBAAyC;;;;;;IAC/D;IAEA,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK;QACzC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,IAAI;YAAE,OAAO,KAAK,IAAI;QAAC;QACtD,OAAO;IACT,GAAG,CAAC;IAGJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAa,WAAU;sBAC7C,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kCACP,6LAAC,oIAAA,CAAA,eAAY;wBAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;4BAAC,SAAQ;4BAAO,SAAS;;;;;;;;;;;kCACpE,6LAAC,kJAAA,CAAA,MAAG;wBACF,MAAM;wBACN,SAAQ;wBACR,SAAQ;wBACR,IAAG;wBACH,IAAG;wBACH,aAAa;wBACb,WAAW;wBACX,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;4BAC1E,MAAM,SAAS,KAAK,EAAE,GAAG;4BACzB,MAAM,SAAS,cAAc,CAAC,cAAc,WAAW,IAAI;4BAC3D,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,2CAA2C;4BAC1F,qBACE,6LAAC;gCACC,GAAG;gCACH,GAAG;gCACH,MAAK;gCACL,YAAY,IAAI,KAAK,UAAU;gCAC/B,kBAAiB;gCACjB,UAAS;0CAER,GAAG,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;wBAGvC;kCAEC,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,IAAI;+BAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC,oIAAA,CAAA,cAAW;wBACV,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;4BAAC,SAAQ;4BAAO,cAAc;gCAAC,YAAY;4BAAE;;;;;;wBACzE,eAAc;wBACd,OAAM;;;;;;;;;;;;;;;;;;;;;;AAMlB;KA/DwB", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/WORK/personal/portage/studio-master/src/components/dashboard/GenderDistributionChart.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from \"recharts\";\nimport {\n  Chart<PERSON>ontainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  type ChartConfig\n} from \"@/components/ui/chart\";\n\ninterface GenderDistributionChartProps {\n  maleCount: number;\n  femaleCount: number;\n}\n\nexport default function GenderDistributionChart({ maleCount, femaleCount }: GenderDistributionChartProps) {\n  const chartData = [\n    { name: 'ذكور', value: maleCount, fill: 'hsl(var(--chart-1))' }, // Primary blue\n    { name: 'إناث', value: femaleCount, fill: 'hsl(var(--chart-3))' }, // Tealish or another contrasting color\n  ].filter(item => item.value > 0);\n\n  if (chartData.length === 0) {\n    return <p className=\"text-muted-foreground text-center py-4\">لا توجد بيانات كافية لعرض توزيع الجنس.</p>;\n  }\n\n  const chartConfig = chartData.reduce((acc, item) => {\n    acc[item.name] = { label: item.name, color: item.fill };\n    return acc;\n  }, {} as ChartConfig);\n\n  return (\n    <div className=\"h-[300px] w-full\">\n      <ChartContainer config={chartConfig} className=\"w-full h-full\">\n        <PieChart>\n          <ChartTooltip content={<ChartTooltipContent nameKey=\"name\" hideLabel />} />\n          <Pie\n            data={chartData}\n            dataKey=\"value\"\n            nameKey=\"name\"\n            cx=\"50%\"\n            cy=\"50%\"\n            outerRadius={80} // Adjusted outerRadius for better fit with label\n            labelLine={false}\n            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }) => {\n              const RADIAN = Math.PI / 180;\n              // Position label slightly outside the pie slice for clarity\n              const radius = outerRadius + 15; \n              const x = cx + radius * Math.cos(-midAngle * RADIAN);\n              const y = cy + radius * Math.sin(-midAngle * RADIAN);\n              if (chartData[index].value === 0) return null;\n              return (\n                <text\n                  x={x}\n                  y={y}\n                  fill=\"hsl(var(--foreground))\" // Use foreground for better visibility\n                  textAnchor={x > cx ? 'start' : 'end'}\n                  dominantBaseline=\"central\"\n                  fontSize=\"12px\"\n                >\n                  {`${name}: ${(percent * 100).toFixed(0)}%`}\n                </text>\n              );\n            }}\n          >\n            {chartData.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.fill} />\n            ))}\n          </Pie>\n          <ChartLegend\n            content={<ChartLegendContent nameKey=\"name\" wrapperStyle={{paddingTop: 10}} />}\n            verticalAlign=\"bottom\"\n            align=\"center\"\n          />\n        </PieChart>\n      </ChartContainer>\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAHA;;;;AAiBe,SAAS,wBAAwB,EAAE,SAAS,EAAE,WAAW,EAAgC;IACtG,MAAM,YAAY;QAChB;YAAE,MAAM;YAAQ,OAAO;YAAW,MAAM;QAAsB;QAC9D;YAAE,MAAM;YAAQ,OAAO;YAAa,MAAM;QAAsB;KACjE,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,GAAG;IAE9B,IAAI,UAAU,MAAM,KAAK,GAAG;QAC1B,qBAAO,6LAAC;YAAE,WAAU;sBAAyC;;;;;;IAC/D;IAEA,MAAM,cAAc,UAAU,MAAM,CAAC,CAAC,KAAK;QACzC,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,IAAI;YAAE,OAAO,KAAK,IAAI;QAAC;QACtD,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,oIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAa,WAAU;sBAC7C,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kCACP,6LAAC,oIAAA,CAAA,eAAY;wBAAC,uBAAS,6LAAC,oIAAA,CAAA,sBAAmB;4BAAC,SAAQ;4BAAO,SAAS;;;;;;;;;;;kCACpE,6LAAC,kJAAA,CAAA,MAAG;wBACF,MAAM;wBACN,SAAQ;wBACR,SAAQ;wBACR,IAAG;wBACH,IAAG;wBACH,aAAa;wBACb,WAAW;wBACX,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;4BAC1E,MAAM,SAAS,KAAK,EAAE,GAAG;4BACzB,4DAA4D;4BAC5D,MAAM,SAAS,cAAc;4BAC7B,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,MAAM,IAAI,KAAK,SAAS,KAAK,GAAG,CAAC,CAAC,WAAW;4BAC7C,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO;4BACzC,qBACE,6LAAC;gCACC,GAAG;gCACH,GAAG;gCACH,MAAK,yBAAyB,uCAAuC;;gCACrE,YAAY,IAAI,KAAK,UAAU;gCAC/B,kBAAiB;gCACjB,UAAS;0CAER,GAAG,KAAK,EAAE,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;wBAGhD;kCAEC,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,IAAI;+BAAjC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC,oIAAA,CAAA,cAAW;wBACV,uBAAS,6LAAC,oIAAA,CAAA,qBAAkB;4BAAC,SAAQ;4BAAO,cAAc;gCAAC,YAAY;4BAAE;;;;;;wBACzE,eAAc;wBACd,OAAM;;;;;;;;;;;;;;;;;;;;;;AAMlB;KA9DwB", "debugId": null}}]}