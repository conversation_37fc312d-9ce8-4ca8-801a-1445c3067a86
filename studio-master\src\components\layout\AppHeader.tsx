"use client";

import Link from 'next/link';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON> } from 'lucide-react'; // Using <PERSON> as a placeholder logo icon
import { APP_NAME } from '@/lib/constants';

export default function AppHeader() {
  return (
    <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-card px-4 shadow-sm md:px-6">
      <div className="md:hidden">
        <SidebarTrigger />
      </div>
      <div className="flex w-full items-center justify-between">
        <Link href="/" className="flex items-center gap-2 text-lg font-semibold md:text-base">
          <Brain className="h-6 w-6 text-primary" />
          <span className="font-bold">{APP_NAME}</span>
        </Link>
        <div className="flex items-center gap-4">
          {/* Placeholder for future elements like search or notifications */}
          <Avatar>
            <AvatarImage src="https://placehold.co/40x40.png" alt="المستخدم" data-ai-hint="user avatar" />
            <AvatarFallback>م</AvatarFallback>
          </Avatar>
        </div>
      </div>
    </header>
  );
}
