"use client";

import React, { useState, useEffect } from 'react';
import { calculateAge, formatDate } from '@/lib/utils';
import type { CalculatedAge } from '@/lib/types';
import { Skeleton } from '@/components/ui/skeleton';

interface AgeDisplayProps {
  birthDate: string; // ISO date string
  assessmentDate?: string; // Optional ISO date string, defaults to today
  className?: string;
  label?: string;
}

export default function AgeDisplay({ birthDate, assessmentDate, className, label = "العمر:" }: AgeDisplayProps) {
  const [age, setAge] = useState<CalculatedAge | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    try {
      const calculated = calculateAge(birthDate, assessmentDate);
      setAge(calculated);
    } catch (error) {
      // console.error("Error calculating age:", error);
      setAge(null); // Set to null or a default error state if needed
    } finally {
      setIsLoading(false);
    }
  }, [birthDate, assessmentDate]);

  if (isLoading) {
    return <Skeleton className={`h-5 w-32 ${className}`} />;
  }

  if (!age) {
    return <span className={className}>تاريخ غير صالح</span>;
  }

  return (
    <span className={className}>
      {label}{' '}
      {age.years > 0 && `${age.years}س `}
      {age.months > 0 && `${age.months}ش `}
      {`${age.days}ي`}
      {! (age.years > 0 || age.months > 0 || age.days > 0) && assessmentDate && birthDate > assessmentDate! && '(تاريخ ميلاد مستقبلي)'}
      {! (age.years > 0 || age.months > 0 || age.days > 0) && !assessmentDate && new Date(birthDate) > new Date() && '(تاريخ ميلاد مستقبلي)'}
    </span>
  );
}
