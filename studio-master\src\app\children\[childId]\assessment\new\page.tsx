
import type { Child } from '@/lib/types';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
// Removed ArrowRight as it's handled in AssessmentEditorClientPage
import { MOCK_CHILDREN_DATA, PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import AssessmentEditorClientPage from '@/components/assessment/AssessmentEditorClientPage'; // Updated import

async function getChildById(id: string): Promise<Child | undefined> {
  // In a real app, fetch from a database
  return MOCK_CHILDREN_DATA.find(child => child.id === id);
}

export default async function NewAssessmentPage({ params }: { params: { childId: string } }) {
  const child = await getChildById(params.childId);

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  return (
     <AssessmentEditorClientPage 
        child={child} 
        portageChecklist={PORTAGE_CHECKLIST_DATA} 
        // No existingAssessment is passed, so it's in "new" mode
     />
  );
}
