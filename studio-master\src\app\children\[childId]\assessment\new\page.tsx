
'use client';

import { useEffect, useState } from 'react';
import type { Child } from '@/lib/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { PORTAGE_CHECKLIST_DATA } from '@/lib/constants';
import AssessmentEditorClientPage from '@/components/assessment/AssessmentEditorClientPage';
import { useChildren } from '@/hooks/use-storage';

export default function NewAssessmentPage({ params }: { params: { childId: string } }) {
  const { getChild, loading: childrenLoading } = useChildren();
  const [child, setChild] = useState<Child | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Wait for children hook to be ready
    if (childrenLoading) {
      return;
    }

    const childData = getChild(params.childId);
    setChild(childData || null);
    setLoading(false);
  }, [params.childId, getChild, childrenLoading]);

  if (loading || childrenLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">جاري تحميل بيانات الطفل...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!child) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h1 className="text-2xl font-semibold">لم يتم العثور على الطفل</h1>
        <Link href="/children">
          <Button variant="link">العودة إلى قائمة الأطفال</Button>
        </Link>
      </div>
    );
  }

  return (
     <AssessmentEditorClientPage
        child={child}
        portageChecklist={PORTAGE_CHECKLIST_DATA}
        // No existingAssessment is passed, so it's in "new" mode
     />
  );
}
