import { useState, useEffect, useCallback } from 'react';
import type { Child, Assessment, User, LearningPlan } from '@/lib/types';
import {
  initializeStorage,
  getChildren,
  getChildById,
  saveChild,
  deleteChild,
  getAssessments,
  getAssessmentsByChildId,
  getAssessmentById,
  saveAssessment,
  deleteAssessment,
  getUsers,
  getUserById,
  saveUser,
  getLearningPlans,
  getLearningPlansByChildId,
  saveLearningPlan,
  exportAllData,
  importAllData,
  clearAllData,
  getStorageInfo,
} from '@/lib/storage';

// Custom hook for children management
export function useChildren() {
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshChildren = useCallback(() => {
    setChildren(getChildren());
  }, []);

  useEffect(() => {
    initializeStorage();
    refreshChildren();
    setLoading(false);
  }, [refreshChildren]);

  const addChild = useCallback((child: Child) => {
    const success = saveChild(child);
    if (success) {
      refreshChildren();
    }
    return success;
  }, [refreshChildren]);

  const updateChild = useCallback((child: Child) => {
    const success = saveChild(child);
    if (success) {
      refreshChildren();
    }
    return success;
  }, [refreshChildren]);

  const removeChild = useCallback((childId: string) => {
    const success = deleteChild(childId);
    if (success) {
      refreshChildren();
    }
    return success;
  }, [refreshChildren]);

  const getChild = useCallback((childId: string) => {
    return getChildById(childId);
  }, []);

  return {
    children,
    loading,
    addChild,
    updateChild,
    removeChild,
    getChild,
    refreshChildren,
  };
}

// Custom hook for assessments management
export function useAssessments(childId?: string) {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshAssessments = useCallback(() => {
    if (childId) {
      setAssessments(getAssessmentsByChildId(childId));
    } else {
      setAssessments(getAssessments());
    }
  }, [childId]);

  useEffect(() => {
    initializeStorage();
    refreshAssessments();
    setLoading(false);
  }, [refreshAssessments]);

  const addAssessment = useCallback((assessment: Assessment) => {
    const success = saveAssessment(assessment);
    if (success) {
      refreshAssessments();
    }
    return success;
  }, [refreshAssessments]);

  const updateAssessment = useCallback((assessment: Assessment) => {
    const success = saveAssessment(assessment);
    if (success) {
      refreshAssessments();
    }
    return success;
  }, [refreshAssessments]);

  const removeAssessment = useCallback((assessmentId: string) => {
    const success = deleteAssessment(assessmentId);
    if (success) {
      refreshAssessments();
    }
    return success;
  }, [refreshAssessments]);

  const getAssessment = useCallback((assessmentId: string) => {
    return getAssessmentById(assessmentId);
  }, []);

  return {
    assessments,
    loading,
    addAssessment,
    updateAssessment,
    removeAssessment,
    getAssessment,
    refreshAssessments,
  };
}

// Custom hook for users management
export function useUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshUsers = useCallback(() => {
    setUsers(getUsers());
  }, []);

  useEffect(() => {
    initializeStorage();
    refreshUsers();
    setLoading(false);
  }, [refreshUsers]);

  const addUser = useCallback((user: User) => {
    const success = saveUser(user);
    if (success) {
      refreshUsers();
    }
    return success;
  }, [refreshUsers]);

  const updateUser = useCallback((user: User) => {
    const success = saveUser(user);
    if (success) {
      refreshUsers();
    }
    return success;
  }, [refreshUsers]);

  const getUser = useCallback((userId: string) => {
    return getUserById(userId);
  }, []);

  return {
    users,
    loading,
    addUser,
    updateUser,
    getUser,
    refreshUsers,
  };
}

// Custom hook for learning plans management
export function useLearningPlans(childId?: string) {
  const [learningPlans, setLearningPlans] = useState<LearningPlan[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshLearningPlans = useCallback(() => {
    if (childId) {
      setLearningPlans(getLearningPlansByChildId(childId));
    } else {
      setLearningPlans(getLearningPlans());
    }
  }, [childId]);

  useEffect(() => {
    initializeStorage();
    refreshLearningPlans();
    setLoading(false);
  }, [refreshLearningPlans]);

  const addLearningPlan = useCallback((plan: LearningPlan) => {
    const success = saveLearningPlan(plan);
    if (success) {
      refreshLearningPlans();
    }
    return success;
  }, [refreshLearningPlans]);

  const updateLearningPlan = useCallback((plan: LearningPlan) => {
    const success = saveLearningPlan(plan);
    if (success) {
      refreshLearningPlans();
    }
    return success;
  }, [refreshLearningPlans]);

  return {
    learningPlans,
    loading,
    addLearningPlan,
    updateLearningPlan,
    refreshLearningPlans,
  };
}

// Custom hook for data management
export function useDataManagement() {
  const [storageInfo, setStorageInfo] = useState<any>(null);

  const refreshStorageInfo = useCallback(() => {
    setStorageInfo(getStorageInfo());
  }, []);

  useEffect(() => {
    refreshStorageInfo();
  }, [refreshStorageInfo]);

  const exportData = useCallback(() => {
    return exportAllData();
  }, []);

  const importData = useCallback((data: any) => {
    const success = importAllData(data);
    if (success) {
      refreshStorageInfo();
    }
    return success;
  }, [refreshStorageInfo]);

  const clearData = useCallback(() => {
    const success = clearAllData();
    if (success) {
      refreshStorageInfo();
    }
    return success;
  }, [refreshStorageInfo]);

  return {
    storageInfo,
    exportData,
    importData,
    clearData,
    refreshStorageInfo,
  };
}
